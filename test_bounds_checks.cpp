// Test file to verify all bounds checks are working correctly
#include "debug/vector_debug.h"
#include <vector>
#include <iostream>
#include <spdlog/spdlog.h>

// Test function to verify SAFE_VECTOR_ACCESS macro
void test_safe_vector_access() {
    std::cout << "Testing SAFE_VECTOR_ACCESS bounds checking...\n";
    
    // Test 1: Normal access within bounds
    std::vector<int> test_vec = {1, 2, 3, 4, 5};
    try {
        int value = SAFE_VECTOR_ACCESS(test_vec, 2, "test normal access");
        std::cout << "✓ Normal access test passed: value = " << value << "\n";
    } catch (const std::exception& e) {
        std::cout << "✗ Normal access test failed: " << e.what() << "\n";
    }
    
    // Test 2: Out of bounds access (should be caught)
    try {
        int value = SAFE_VECTOR_ACCESS(test_vec, 10, "test out of bounds access");
        std::cout << "✗ Out of bounds test failed: should have thrown exception\n";
    } catch (const std::exception& e) {
        std::cout << "✓ Out of bounds test passed: " << e.what() << "\n";
    }
    
    // Test 3: Empty vector access
    std::vector<int> empty_vec;
    try {
        int value = SAFE_VECTOR_ACCESS(empty_vec, 0, "test empty vector access");
        std::cout << "✗ Empty vector test failed: should have thrown exception\n";
    } catch (const std::exception& e) {
        std::cout << "✓ Empty vector test passed: " << e.what() << "\n";
    }
    
    // Test 4: Large vector with valid access
    std::vector<uint8_t> large_vec(1024 * 1024, 0x42); // 1MB vector
    try {
        uint8_t value = SAFE_VECTOR_ACCESS(large_vec, 500000, "test large vector access");
        std::cout << "✓ Large vector test passed: value = 0x" << std::hex << (int)value << std::dec << "\n";
    } catch (const std::exception& e) {
        std::cout << "✗ Large vector test failed: " << e.what() << "\n";
    }
}

// Test function to verify VALIDATE_LARGE_MEMORY_BUFFER macro
void test_large_memory_validation() {
    std::cout << "\nTesting VALIDATE_LARGE_MEMORY_BUFFER...\n";
    
    // Test 1: Normal sized buffer
    std::vector<uint8_t> normal_buffer(512 * 1024 * 1024); // 512MB
    try {
        VALIDATE_LARGE_MEMORY_BUFFER(normal_buffer, "test normal buffer", 16);
        std::cout << "✓ Normal buffer validation passed\n";
    } catch (const std::exception& e) {
        std::cout << "✗ Normal buffer validation failed: " << e.what() << "\n";
    }
    
    // Test 2: Empty buffer
    std::vector<uint8_t> empty_buffer;
    try {
        VALIDATE_LARGE_MEMORY_BUFFER(empty_buffer, "test empty buffer", 16);
        std::cout << "✓ Empty buffer validation passed (warning expected)\n";
    } catch (const std::exception& e) {
        std::cout << "✗ Empty buffer validation failed: " << e.what() << "\n";
    }
    
    // Test 3: Very large buffer (should trigger warning but not fail)
    try {
        std::vector<uint8_t> huge_buffer;
        huge_buffer.reserve(20ULL * 1024 * 1024 * 1024); // 20GB (exceeds 16GB limit)
        // Don't actually allocate, just test the size check
        if (huge_buffer.capacity() > 16ULL * 1024 * 1024 * 1024) {
            std::cout << "✓ Large buffer size check would trigger warning\n";
        }
    } catch (const std::exception& e) {
        std::cout << "Large buffer test: " << e.what() << "\n";
    }
}

// Test memory bounds checking
void test_memory_bounds() {
    std::cout << "\nTesting memory bounds checking...\n";
    
    // Test array bounds
    constexpr size_t ARRAY_SIZE = 100;
    std::array<int, ARRAY_SIZE> test_array;
    test_array.fill(42);
    
    // Test valid access
    try {
        for (size_t i = 0; i < ARRAY_SIZE; ++i) {
            if (i < test_array.size()) {
                int value = test_array[i];
                (void)value; // Suppress unused variable warning
            }
        }
        std::cout << "✓ Array bounds checking passed\n";
    } catch (const std::exception& e) {
        std::cout << "✗ Array bounds checking failed: " << e.what() << "\n";
    }
}

// Test string bounds checking
void test_string_bounds() {
    std::cout << "\nTesting string bounds checking...\n";
    
    std::string test_string = "Hello, World!";
    
    // Test valid access
    try {
        if (!test_string.empty() && test_string.size() > 0) {
            char first_char = test_string[0];
            std::cout << "✓ String bounds checking passed: first char = '" << first_char << "'\n";
        }
    } catch (const std::exception& e) {
        std::cout << "✗ String bounds checking failed: " << e.what() << "\n";
    }
    
    // Test empty string
    std::string empty_string;
    try {
        if (!empty_string.empty() && empty_string.size() > 0) {
            char first_char = empty_string[0]; // This should not execute
            (void)first_char;
            std::cout << "✗ Empty string test failed: should not access\n";
        } else {
            std::cout << "✓ Empty string bounds checking passed\n";
        }
    } catch (const std::exception& e) {
        std::cout << "Empty string test: " << e.what() << "\n";
    }
}

int main() {
    // Set up logging
    spdlog::set_level(spdlog::level::debug);
    
    std::cout << "=== PS4 Emulator Bounds Checking Test Suite ===\n\n";
    
    test_safe_vector_access();
    test_large_memory_validation();
    test_memory_bounds();
    test_string_bounds();
    
    std::cout << "\n=== Test Suite Complete ===\n";
    std::cout << "All bounds checking mechanisms have been tested.\n";
    std::cout << "Check the output above for any failures.\n";
    
    return 0;
}
