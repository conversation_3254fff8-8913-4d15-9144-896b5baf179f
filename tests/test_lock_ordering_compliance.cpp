/**
 * @file test_lock_ordering_compliance.cpp
 * @brief Tests for lock ordering compliance in PS4 filesystem
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "ps4/ps4_filesystem.h"
#include "common/lock_ordering.h"
#include "utils/test_fixtures.h"
#include <thread>
#include <chrono>
#include <atomic>
#include <fcntl.h>

using namespace ps4;
using namespace ps4::test;

class LockOrderingComplianceTest : public LockOrderingTestFixture {
protected:
    void SetUp() override {
        LockOrderingTestFixture::SetUp();
        
        // Create test files for lock ordering tests
        test_data_ = GenerateTestData(1024);
        test_file_path_ = CreateTestFile("lock_test.bin", test_data_);
        
        // Mount test directory
        std::wstring wide_path(GetTestDirectory().wstring());
        GetFilesystem()->MountDirectory(wide_path);
        
        guest_file_path_ = "/mnt/" + GetTestDirectory().filename().string() + "/lock_test.bin";
    }
    
protected:
    std::vector<uint8_t> test_data_;
    std::string test_file_path_;
    std::string guest_file_path_;
};

// Basic lock ordering tests
TEST_F(LockOrderingComplianceTest, FileOperationsRespectLockOrdering) {
    // These operations should not trigger lock ordering violations
    int fd = GetFilesystem()->OpenFile(guest_file_path_, O_RDWR, 0644);
    ASSERT_GT(fd, 0);
    
    // Read operation
    std::vector<uint8_t> read_buffer(512);
    ssize_t bytes_read = GetFilesystem()->ReadFile(fd, read_buffer.data(), read_buffer.size());
    EXPECT_GT(bytes_read, 0);
    
    // Seek operation
    off_t new_pos = GetFilesystem()->SeekFile(fd, 0, SEEK_SET);
    EXPECT_EQ(new_pos, 0);
    
    // Write operation
    std::vector<uint8_t> write_data = GenerateTestData(256, 0xCD);
    ssize_t bytes_written = GetFilesystem()->WriteFile(fd, write_data.data(), write_data.size());
    EXPECT_GT(bytes_written, 0);
    
    // Close operation
    EXPECT_EQ(GetFilesystem()->CloseFile(fd), 0);
    
    // If we reach here without lock ordering violations, the test passes
    SUCCEED();
}

TEST_F(LockOrderingComplianceTest, DirectoryOperationsRespectLockOrdering) {
    // Directory operations should not trigger lock ordering violations
    EXPECT_TRUE(GetFilesystem()->CreateDirectory("/test/lock_test_dir", 0755));
    EXPECT_TRUE(GetFilesystem()->RemoveDirectory("/test/lock_test_dir"));
    
    // If we reach here without lock ordering violations, the test passes
    SUCCEED();
}

TEST_F(LockOrderingComplianceTest, CacheOperationsRespectLockOrdering) {
    // Cache operations should not trigger lock ordering violations
    GetFilesystem()->InitializeCache(1024 * 1024); // 1MB cache
    
    // Cache file
    std::vector<uint8_t> cache_data = GenerateTestData(1024);
    GetFilesystem()->CacheFile("/test/cached_file.bin", std::move(cache_data));
    
    // Get cached file
    std::vector<uint8_t> retrieved_data;
    bool found = GetFilesystem()->GetCachedFile("/test/cached_file.bin", retrieved_data);
    EXPECT_TRUE(found);
    
    // Invalidate cached file
    GetFilesystem()->InvalidateCachedFile("/test/cached_file.bin");
    
    // Clear cache
    GetFilesystem()->ClearCache();
    
    // If we reach here without lock ordering violations, the test passes
    SUCCEED();
}

// Concurrent access tests
TEST_F(LockOrderingComplianceTest, ConcurrentFileAccess) {
    const int num_threads = 4;
    const int operations_per_thread = 50;
    std::vector<std::thread> threads;
    std::atomic<int> successful_operations{0};
    std::atomic<bool> lock_violation_detected{false};
    
    // Launch threads that perform concurrent file operations
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, t, operations_per_thread, &successful_operations, &lock_violation_detected]() {
            try {
                for (int i = 0; i < operations_per_thread; ++i) {
                    // Open file
                    int fd = GetFilesystem()->OpenFile(guest_file_path_, O_RDONLY, 0644);
                    if (fd > 0) {
                        // Read some data
                        std::vector<uint8_t> buffer(256);
                        ssize_t bytes_read = GetFilesystem()->ReadFile(fd, buffer.data(), buffer.size());
                        
                        if (bytes_read > 0) {
                            successful_operations++;
                        }
                        
                        // Close file
                        GetFilesystem()->CloseFile(fd);
                    }
                    
                    // Small delay to increase chance of contention
                    std::this_thread::sleep_for(std::chrono::microseconds(10));
                }
            } catch (const std::exception& e) {
                // If lock ordering is violated, an exception might be thrown
                lock_violation_detected = true;
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Check results
    EXPECT_FALSE(lock_violation_detected.load()) << "Lock ordering violation detected";
    EXPECT_GT(successful_operations.load(), 0) << "No successful operations completed";
}

TEST_F(LockOrderingComplianceTest, ConcurrentCacheAccess) {
    const int num_threads = 4;
    const int operations_per_thread = 100;
    std::vector<std::thread> threads;
    std::atomic<bool> lock_violation_detected{false};
    
    // Initialize cache
    GetFilesystem()->InitializeCache(1024 * 1024);
    
    // Launch threads that perform concurrent cache operations
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, t, operations_per_thread, &lock_violation_detected]() {
            try {
                for (int i = 0; i < operations_per_thread; ++i) {
                    std::string filename = "/test/thread_" + std::to_string(t) + "_file_" + std::to_string(i) + ".bin";
                    
                    // Cache some data
                    std::vector<uint8_t> data = GenerateTestData(512, static_cast<uint8_t>(t + i));
                    GetFilesystem()->CacheFile(filename, std::move(data));
                    
                    // Try to retrieve data
                    std::vector<uint8_t> retrieved_data;
                    GetFilesystem()->GetCachedFile(filename, retrieved_data);
                    
                    // Occasionally invalidate
                    if (i % 10 == 0) {
                        GetFilesystem()->InvalidateCachedFile(filename);
                    }
                }
            } catch (const std::exception& e) {
                lock_violation_detected = true;
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    EXPECT_FALSE(lock_violation_detected.load()) << "Lock ordering violation detected in cache operations";
}

TEST_F(LockOrderingComplianceTest, ConcurrentDirectoryOperations) {
    const int num_threads = 4;
    const int operations_per_thread = 20;
    std::vector<std::thread> threads;
    std::atomic<bool> lock_violation_detected{false};
    
    // Launch threads that perform concurrent directory operations
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, t, operations_per_thread, &lock_violation_detected]() {
            try {
                for (int i = 0; i < operations_per_thread; ++i) {
                    std::string dirname = "/test/thread_" + std::to_string(t) + "_dir_" + std::to_string(i);
                    
                    // Create directory
                    GetFilesystem()->CreateDirectory(dirname, 0755);
                    
                    // Remove directory
                    GetFilesystem()->RemoveDirectory(dirname);
                }
            } catch (const std::exception& e) {
                lock_violation_detected = true;
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    EXPECT_FALSE(lock_violation_detected.load()) << "Lock ordering violation detected in directory operations";
}

// Memory operations lock ordering tests
TEST_F(LockOrderingComplianceTest, MemoryOperationsRespectLockOrdering) {
    // Set up mock expectations for memory operations
    EXPECT_CALL(*GetMockEmulator(), AllocateMemory(_, _))
        .WillRepeatedly(::testing::Return(0x10000000));
    EXPECT_CALL(*GetMockEmulator(), FreeMemory(_))
        .WillRepeatedly(::testing::Return(true));
    EXPECT_CALL(*GetMockEmulator(), ProtectMemory(_, _, _))
        .WillRepeatedly(::testing::Return(true));
    
    // Memory operations should not trigger lock ordering violations
    uint64_t addr1 = GetFilesystem()->AllocateVirtualMemory(4096, 4096, false);
    EXPECT_NE(addr1, 0);
    
    uint64_t addr2 = GetFilesystem()->AllocateVirtualMemory(8192, 4096, true);
    EXPECT_NE(addr2, 0);
    
    EXPECT_TRUE(GetFilesystem()->ProtectMemory(addr1, 4096, 0x7));
    EXPECT_TRUE(GetFilesystem()->FreeVirtualMemory(addr1));
    EXPECT_TRUE(GetFilesystem()->FreeVirtualMemory(addr2));
    
    // If we reach here without lock ordering violations, the test passes
    SUCCEED();
}

// State management lock ordering tests
TEST_F(LockOrderingComplianceTest, StateOperationsRespectLockOrdering) {
    // State operations should not trigger lock ordering violations
    std::string state = GetFilesystem()->DumpState();
    EXPECT_FALSE(state.empty());
    
    // Save state
    std::stringstream state_stream;
    GetFilesystem()->SaveState(state_stream);
    
    // Load state (create new filesystem for this)
    auto new_filesystem = std::make_unique<PS4Filesystem>(*GetMockEmulator());
    new_filesystem->Initialize();
    
    state_stream.seekg(0);
    new_filesystem->LoadState(state_stream);
    
    // If we reach here without lock ordering violations, the test passes
    SUCCEED();
}

// Mixed operations lock ordering tests
TEST_F(LockOrderingComplianceTest, MixedOperationsRespectLockOrdering) {
    // Perform a mix of different operations that might interact
    
    // File operations
    int fd = GetFilesystem()->OpenFile(guest_file_path_, O_RDWR, 0644);
    ASSERT_GT(fd, 0);
    
    // Cache operations
    GetFilesystem()->InitializeCache(1024 * 1024);
    std::vector<uint8_t> cache_data = GenerateTestData(512);
    GetFilesystem()->CacheFile("/test/mixed_test.bin", std::move(cache_data));
    
    // Directory operations
    GetFilesystem()->CreateDirectory("/test/mixed_dir", 0755);
    
    // Memory operations
    EXPECT_CALL(*GetMockEmulator(), AllocateMemory(_, _))
        .WillOnce(::testing::Return(0x10000000));
    uint64_t addr = GetFilesystem()->AllocateVirtualMemory(4096, 4096, false);
    
    // More file operations
    std::vector<uint8_t> read_buffer(256);
    GetFilesystem()->ReadFile(fd, read_buffer.data(), read_buffer.size());
    
    // State operations
    std::string state = GetFilesystem()->DumpState();
    
    // Cleanup
    GetFilesystem()->CloseFile(fd);
    GetFilesystem()->RemoveDirectory("/test/mixed_dir");
    
    EXPECT_CALL(*GetMockEmulator(), FreeMemory(_))
        .WillOnce(::testing::Return(true));
    GetFilesystem()->FreeVirtualMemory(addr);
    
    // If we reach here without lock ordering violations, the test passes
    SUCCEED();
}

// Stress test for lock ordering
TEST_F(LockOrderingComplianceTest, StressTestLockOrdering) {
    const int num_threads = 8;
    const int operations_per_thread = 200;
    std::vector<std::thread> threads;
    std::atomic<bool> lock_violation_detected{false};
    std::atomic<int> total_operations{0};
    
    // Initialize cache for stress test
    GetFilesystem()->InitializeCache(2 * 1024 * 1024);
    
    // Set up mock expectations
    EXPECT_CALL(*GetMockEmulator(), AllocateMemory(_, _))
        .WillRepeatedly(::testing::Return(0x10000000));
    EXPECT_CALL(*GetMockEmulator(), FreeMemory(_))
        .WillRepeatedly(::testing::Return(true));
    
    // Launch threads that perform various operations concurrently
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, t, operations_per_thread, &lock_violation_detected, &total_operations]() {
            try {
                for (int i = 0; i < operations_per_thread; ++i) {
                    int operation = (t + i) % 5;
                    
                    switch (operation) {
                        case 0: {
                            // File operations
                            int fd = GetFilesystem()->OpenFile(guest_file_path_, O_RDONLY, 0644);
                            if (fd > 0) {
                                std::vector<uint8_t> buffer(128);
                                GetFilesystem()->ReadFile(fd, buffer.data(), buffer.size());
                                GetFilesystem()->CloseFile(fd);
                            }
                            break;
                        }
                        case 1: {
                            // Cache operations
                            std::string filename = "/stress/file_" + std::to_string(t) + "_" + std::to_string(i);
                            std::vector<uint8_t> data = GenerateTestData(256, static_cast<uint8_t>(i));
                            GetFilesystem()->CacheFile(filename, std::move(data));
                            break;
                        }
                        case 2: {
                            // Directory operations
                            std::string dirname = "/stress/dir_" + std::to_string(t) + "_" + std::to_string(i);
                            GetFilesystem()->CreateDirectory(dirname, 0755);
                            GetFilesystem()->RemoveDirectory(dirname);
                            break;
                        }
                        case 3: {
                            // Memory operations
                            uint64_t addr = GetFilesystem()->AllocateVirtualMemory(4096, 4096, false);
                            if (addr != 0) {
                                GetFilesystem()->FreeVirtualMemory(addr);
                            }
                            break;
                        }
                        case 4: {
                            // State operations
                            GetFilesystem()->DumpState();
                            break;
                        }
                    }
                    
                    total_operations++;
                    
                    // Small delay to increase contention
                    if (i % 20 == 0) {
                        std::this_thread::sleep_for(std::chrono::microseconds(1));
                    }
                }
            } catch (const std::exception& e) {
                lock_violation_detected = true;
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Check results
    EXPECT_FALSE(lock_violation_detected.load()) << "Lock ordering violation detected during stress test";
    EXPECT_EQ(total_operations.load(), num_threads * operations_per_thread) 
        << "Not all operations completed successfully";
}
