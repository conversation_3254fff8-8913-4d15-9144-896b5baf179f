/**
 * @file main_test.cpp
 * @brief Main entry point for PS4 Filesystem test suite
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <spdlog/spdlog.h>
#include <filesystem>
#include <iostream>
#include <fstream>
#include <vector>

// Test environment setup
class PS4FilesystemTestEnvironment : public ::testing::Environment {
public:
    void SetUp() override {
        // Configure logging for tests
        spdlog::set_level(spdlog::level::debug);
        spdlog::set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v");
        
        // Create test directories
        std::filesystem::create_directories("test_data");
        std::filesystem::create_directories("test_data/pfs");
        std::filesystem::create_directories("test_data/mount_points");
        std::filesystem::create_directories("test_data/temp");
        
        // Create test files
        CreateTestFiles();
        
        std::cout << "PS4 Filesystem Test Environment initialized" << std::endl;
    }
    
    void TearDown() override {
        // Clean up test directories
        try {
            std::filesystem::remove_all("test_data");
        } catch (const std::exception& e) {
            std::cerr << "Warning: Failed to clean up test data: " << e.what() << std::endl;
        }
        
        std::cout << "PS4 Filesystem Test Environment cleaned up" << std::endl;
    }
    
private:
    void CreateTestFiles() {
        // Create a test binary file
        std::ofstream testFile("test_data/test_file.bin", std::ios::binary);
        if (testFile.is_open()) {
            std::vector<uint8_t> testData(1024, 0xAB);
            testFile.write(reinterpret_cast<const char*>(testData.data()), testData.size());
            testFile.close();
        }
        
        // Create a test text file
        std::ofstream textFile("test_data/test_text.txt");
        if (textFile.is_open()) {
            textFile << "This is a test file for PS4 filesystem testing.\n";
            textFile << "It contains multiple lines of text.\n";
            textFile << "Used for various file operation tests.\n";
            textFile.close();
        }
        
        // Create a large test file for performance testing
        std::ofstream largeFile("test_data/large_test_file.bin", std::ios::binary);
        if (largeFile.is_open()) {
            std::vector<uint8_t> largeData(1024 * 1024, 0xCD); // 1MB file
            largeFile.write(reinterpret_cast<const char*>(largeData.data()), largeData.size());
            largeFile.close();
        }
        
        // Create directory structure for mount point tests
        std::filesystem::create_directories("test_data/mount_points/app0");
        std::filesystem::create_directories("test_data/mount_points/savedata");
        std::filesystem::create_directories("test_data/mount_points/system");
        
        // Create files in mount point directories
        std::ofstream app0File("test_data/mount_points/app0/game_data.bin", std::ios::binary);
        if (app0File.is_open()) {
            std::vector<uint8_t> gameData(512, 0xEF);
            app0File.write(reinterpret_cast<const char*>(gameData.data()), gameData.size());
            app0File.close();
        }
    }
};

int main(int argc, char** argv) {
    // Initialize Google Test and Google Mock
    ::testing::InitGoogleTest(&argc, argv);
    ::testing::InitGoogleMock(&argc, argv);
    
    // Add global test environment
    ::testing::AddGlobalTestEnvironment(new PS4FilesystemTestEnvironment);
    
    // Configure test behavior
    ::testing::FLAGS_gtest_death_test_style = "threadsafe";
    ::testing::FLAGS_gtest_catch_exceptions = true;
    
    std::cout << "Starting PS4 Filesystem Test Suite..." << std::endl;
    std::cout << "Test data directory: " << std::filesystem::absolute("test_data") << std::endl;
    
    // Run all tests
    int result = RUN_ALL_TESTS();
    
    if (result == 0) {
        std::cout << "All PS4 Filesystem tests passed!" << std::endl;
    } else {
        std::cout << "Some PS4 Filesystem tests failed. Check output above." << std::endl;
    }
    
    return result;
}
