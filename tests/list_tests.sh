#!/bin/bash
# PS4 Filesystem Test List Script for Unix/Linux

echo "========================================"
echo "PS4 Filesystem Test Suite - Available Tests"
echo "========================================"

if [ ! -f "../build/tests/ps4_filesystem_tests" ]; then
    echo "Error: Test executable not found. Please build the tests first."
    echo "Run: cmake --build ../build --target ps4_filesystem_tests --config Debug"
    exit 1
fi

echo
echo "Available Test Suites:"
echo

echo "1. FileLRUCache Tests:"
../build/tests/ps4_filesystem_tests --gtest_list_tests --gtest_filter="FileLRUCacheTest.*"

echo
echo "2. MountPointManager Tests:"
../build/tests/ps4_filesystem_tests --gtest_list_tests --gtest_filter="MountPointManagerTest.*"

echo
echo "3. HandleTable Tests:"
../build/tests/ps4_filesystem_tests --gtest_list_tests --gtest_filter="HandleTableTest.*"

echo
echo "4. PS4Filesystem Core Tests:"
../build/tests/ps4_filesystem_tests --gtest_list_tests --gtest_filter="PS4FilesystemCoreTest.*"

echo
echo "5. PS4Filesystem PFS Tests:"
../build/tests/ps4_filesystem_tests --gtest_list_tests --gtest_filter="PS4FilesystemPFSTest.*"

echo
echo "6. PS4Filesystem Device Tests:"
../build/tests/ps4_filesystem_tests --gtest_list_tests --gtest_filter="PS4FilesystemDeviceTest.*"

echo
echo "7. PS4Filesystem Integration Tests:"
../build/tests/ps4_filesystem_tests --gtest_list_tests --gtest_filter="PS4FilesystemIntegrationTest.*"

echo
echo "8. PS4Filesystem Performance Tests:"
../build/tests/ps4_filesystem_tests --gtest_list_tests --gtest_filter="PS4FilesystemPerformanceTest.*"

echo
echo "9. Lock Ordering Compliance Tests:"
../build/tests/ps4_filesystem_tests --gtest_list_tests --gtest_filter="LockOrderingComplianceTest.*"

echo
echo "========================================"
echo "To run a specific test suite, use:"
echo "ps4_filesystem_tests --gtest_filter=\"TestSuiteName.*\""
echo
echo "To run all tests, use:"
echo "ps4_filesystem_tests"
echo "========================================"
