/**
 * @file test_ps4_filesystem_performance.cpp
 * @brief Performance tests for PS4 filesystem functionality
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "ps4/ps4_filesystem.h"
#include "utils/test_fixtures.h"
#include "utils/test_helpers.h"
#include <chrono>
#include <fcntl.h>

using namespace ps4;
using namespace ps4::test;

class PS4FilesystemPerformanceTest : public PerformanceTestFixture {
protected:
    void SetUp() override {
        PerformanceTestFixture::SetUp();
        
        // Set up mock expectations for performance tests
        EXPECT_CALL(*GetMockEmulator(), Initialize())
            .WillRepeatedly(::testing::Return(true));
        EXPECT_CALL(*GetMockEmulator(), GetProcessId())
            .WillRepeatedly(::testing::Return(1234));
    }
    
    // Performance thresholds (adjust based on system capabilities)
    static constexpr auto MAX_FILE_OPEN_TIME = std::chrono::milliseconds(10);
    static constexpr auto MAX_SMALL_READ_TIME = std::chrono::milliseconds(5);
    static constexpr auto MAX_LARGE_READ_TIME = std::chrono::milliseconds(100);
    static constexpr auto MAX_CACHE_ACCESS_TIME = std::chrono::microseconds(100);
};

// File operation performance tests
TEST_F(PS4FilesystemPerformanceTest, FileOpenPerformance) {
    // Mount test directory
    std::wstring wide_path(GetTestDirectory().wstring());
    ASSERT_TRUE(GetFilesystem()->MountDirectory(wide_path));
    
    std::string guest_path = "/mnt/" + GetTestDirectory().filename().string() + "/small_file.bin";
    
    // Measure file open performance
    const int num_iterations = 100;
    auto start_time = GetCurrentTime();
    
    for (int i = 0; i < num_iterations; ++i) {
        int fd = GetFilesystem()->OpenFile(guest_path, O_RDONLY, 0644);
        if (fd > 0) {
            GetFilesystem()->CloseFile(fd);
        }
    }
    
    auto total_time = GetElapsedTime(start_time);
    auto avg_time = total_time / num_iterations;
    
    EXPECT_LT(avg_time, MAX_FILE_OPEN_TIME) 
        << "Average file open time: " << FormatDuration(avg_time);
    
    std::cout << "File open performance: " << FormatDuration(avg_time) 
              << " average per operation" << std::endl;
}

TEST_F(PS4FilesystemPerformanceTest, SmallFileReadPerformance) {
    // Mount test directory
    std::wstring wide_path(GetTestDirectory().wstring());
    ASSERT_TRUE(GetFilesystem()->MountDirectory(wide_path));
    
    std::string guest_path = "/mnt/" + GetTestDirectory().filename().string() + "/small_file.bin";
    
    int fd = GetFilesystem()->OpenFile(guest_path, O_RDONLY, 0644);
    ASSERT_GT(fd, 0);
    
    // Measure small read performance
    const int num_reads = 1000;
    std::vector<uint8_t> buffer(256); // 256 byte reads
    
    auto start_time = GetCurrentTime();
    
    for (int i = 0; i < num_reads; ++i) {
        GetFilesystem()->SeekFile(fd, 0, SEEK_SET); // Reset position
        GetFilesystem()->ReadFile(fd, buffer.data(), buffer.size());
    }
    
    auto total_time = GetElapsedTime(start_time);
    auto avg_time = total_time / num_reads;
    
    EXPECT_LT(avg_time, MAX_SMALL_READ_TIME)
        << "Average small read time: " << FormatDuration(avg_time);
    
    GetFilesystem()->CloseFile(fd);
    
    std::cout << "Small read performance: " << FormatDuration(avg_time)
              << " average per 256-byte read" << std::endl;
}

TEST_F(PS4FilesystemPerformanceTest, LargeFileReadPerformance) {
    // Mount test directory
    std::wstring wide_path(GetTestDirectory().wstring());
    ASSERT_TRUE(GetFilesystem()->MountDirectory(wide_path));
    
    std::string guest_path = "/mnt/" + GetTestDirectory().filename().string() + "/large_file.bin";
    
    int fd = GetFilesystem()->OpenFile(guest_path, O_RDONLY, 0644);
    ASSERT_GT(fd, 0);
    
    // Measure large read performance
    const int num_reads = 10;
    std::vector<uint8_t> buffer(64 * 1024); // 64KB reads
    
    auto start_time = GetCurrentTime();
    
    for (int i = 0; i < num_reads; ++i) {
        GetFilesystem()->SeekFile(fd, 0, SEEK_SET); // Reset position
        GetFilesystem()->ReadFile(fd, buffer.data(), buffer.size());
    }
    
    auto total_time = GetElapsedTime(start_time);
    auto avg_time = total_time / num_reads;
    
    EXPECT_LT(avg_time, MAX_LARGE_READ_TIME)
        << "Average large read time: " << FormatDuration(avg_time);
    
    GetFilesystem()->CloseFile(fd);
    
    // Calculate throughput
    double throughput = (64.0 * 1024.0 * num_reads) / 
                       std::chrono::duration<double>(total_time).count();
    
    std::cout << "Large read performance: " << FormatDuration(avg_time)
              << " average per 64KB read (" << FormatBytes(static_cast<uint64_t>(throughput)) 
              << "/s throughput)" << std::endl;
}

// Cache performance tests
TEST_F(PS4FilesystemPerformanceTest, CacheAccessPerformance) {
    // Initialize cache
    GetFilesystem()->InitializeCache(10 * 1024 * 1024); // 10MB cache
    
    // Pre-populate cache with test data
    const int num_files = 100;
    std::vector<std::string> filenames;
    
    for (int i = 0; i < num_files; ++i) {
        std::string filename = "/cache_perf/file_" + std::to_string(i) + ".bin";
        std::vector<uint8_t> data = GenerateTestData(1024, static_cast<uint8_t>(i));
        GetFilesystem()->CacheFile(filename, std::move(data));
        filenames.push_back(filename);
    }
    
    // Measure cache access performance
    const int num_accesses = 10000;
    auto start_time = GetCurrentTime();
    
    for (int i = 0; i < num_accesses; ++i) {
        std::string filename = filenames[i % num_files];
        std::vector<uint8_t> retrieved_data;
        GetFilesystem()->GetCachedFile(filename, retrieved_data);
    }
    
    auto total_time = GetElapsedTime(start_time);
    auto avg_time = total_time / num_accesses;
    
    EXPECT_LT(avg_time, MAX_CACHE_ACCESS_TIME)
        << "Average cache access time: " << FormatDuration(avg_time);
    
    std::cout << "Cache access performance: " << FormatDuration(avg_time)
              << " average per access" << std::endl;
}

TEST_F(PS4FilesystemPerformanceTest, CacheEvictionPerformance) {
    // Initialize small cache to force evictions
    GetFilesystem()->InitializeCache(1024 * 1024); // 1MB cache
    
    const int num_files = 1000;
    auto start_time = GetCurrentTime();
    
    // Add files that will trigger evictions
    for (int i = 0; i < num_files; ++i) {
        std::string filename = "/eviction_test/file_" + std::to_string(i) + ".bin";
        std::vector<uint8_t> data = GenerateTestData(2048, static_cast<uint8_t>(i)); // 2KB each
        GetFilesystem()->CacheFile(filename, std::move(data));
    }
    
    auto total_time = GetElapsedTime(start_time);
    auto avg_time = total_time / num_files;
    
    std::cout << "Cache eviction performance: " << FormatDuration(avg_time)
              << " average per file (with evictions)" << std::endl;
    
    // Verify cache is still functional
    double hit_ratio = GetFilesystem()->GetCacheHitRatio();
    EXPECT_GE(hit_ratio, 0.0);
    EXPECT_LE(hit_ratio, 1.0);
}

// Concurrent access performance tests
TEST_F(PS4FilesystemPerformanceTest, ConcurrentFileAccessPerformance) {
    // Mount test directory
    std::wstring wide_path(GetTestDirectory().wstring());
    ASSERT_TRUE(GetFilesystem()->MountDirectory(wide_path));
    
    std::string guest_path = "/mnt/" + GetTestDirectory().filename().string() + "/medium_file.bin";
    
    const int num_threads = 4;
    const int operations_per_thread = 100;
    std::atomic<int> completed_operations{0};
    
    auto start_time = GetCurrentTime();
    
    std::vector<std::thread> threads;
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, &guest_path, operations_per_thread, &completed_operations]() {
            for (int i = 0; i < operations_per_thread; ++i) {
                int fd = GetFilesystem()->OpenFile(guest_path, O_RDONLY, 0644);
                if (fd > 0) {
                    std::vector<uint8_t> buffer(1024);
                    GetFilesystem()->ReadFile(fd, buffer.data(), buffer.size());
                    GetFilesystem()->CloseFile(fd);
                    completed_operations++;
                }
            }
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    
    auto total_time = GetElapsedTime(start_time);
    int total_operations = completed_operations.load();
    auto avg_time = total_time / total_operations;
    
    std::cout << "Concurrent access performance: " << FormatDuration(avg_time)
              << " average per operation (" << num_threads << " threads, "
              << total_operations << " total operations)" << std::endl;
    
    EXPECT_GT(total_operations, 0);
}

// Memory allocation performance tests
TEST_F(PS4FilesystemPerformanceTest, MemoryAllocationPerformance) {
    // Set up mock expectations
    EXPECT_CALL(*GetMockEmulator(), AllocateMemory(_, _))
        .WillRepeatedly(::testing::Return(0x10000000));
    EXPECT_CALL(*GetMockEmulator(), FreeMemory(_))
        .WillRepeatedly(::testing::Return(true));
    
    const int num_allocations = 1000;
    std::vector<uint64_t> addresses;
    
    // Measure allocation performance
    auto start_time = GetCurrentTime();
    
    for (int i = 0; i < num_allocations; ++i) {
        uint64_t addr = GetFilesystem()->AllocateVirtualMemory(4096, 4096, false);
        if (addr != 0) {
            addresses.push_back(addr);
        }
    }
    
    auto alloc_time = GetElapsedTime(start_time);
    
    // Measure deallocation performance
    start_time = GetCurrentTime();
    
    for (uint64_t addr : addresses) {
        GetFilesystem()->FreeVirtualMemory(addr);
    }
    
    auto free_time = GetElapsedTime(start_time);
    
    auto avg_alloc_time = alloc_time / num_allocations;
    auto avg_free_time = free_time / addresses.size();
    
    std::cout << "Memory allocation performance: " << FormatDuration(avg_alloc_time)
              << " average per allocation" << std::endl;
    std::cout << "Memory deallocation performance: " << FormatDuration(avg_free_time)
              << " average per deallocation" << std::endl;
}

// Benchmark test for overall filesystem performance
TEST_F(PS4FilesystemPerformanceTest, OverallPerformanceBenchmark) {
    // This test provides a comprehensive benchmark of filesystem performance
    
    // Mount test directory
    std::wstring wide_path(GetTestDirectory().wstring());
    ASSERT_TRUE(GetFilesystem()->MountDirectory(wide_path));
    
    // Initialize cache
    GetFilesystem()->InitializeCache(5 * 1024 * 1024); // 5MB cache
    
    const int num_iterations = 50;
    PerformanceMeasurement measurement;
    
    measurement.StartMeasurement("Overall Filesystem Benchmark");
    
    for (int i = 0; i < num_iterations; ++i) {
        // File operations
        std::string guest_path = "/mnt/" + GetTestDirectory().filename().string() + "/small_file.bin";
        int fd = GetFilesystem()->OpenFile(guest_path, O_RDONLY, 0644);
        if (fd > 0) {
            std::vector<uint8_t> buffer(1024);
            GetFilesystem()->ReadFile(fd, buffer.data(), buffer.size());
            GetFilesystem()->SeekFile(fd, 0, SEEK_SET);
            GetFilesystem()->CloseFile(fd);
        }
        
        // Cache operations
        std::string cache_file = "/benchmark/cache_" + std::to_string(i);
        std::vector<uint8_t> cache_data = GenerateTestData(512, static_cast<uint8_t>(i));
        GetFilesystem()->CacheFile(cache_file, std::move(cache_data));
        
        std::vector<uint8_t> retrieved_data;
        GetFilesystem()->GetCachedFile(cache_file, retrieved_data);
        
        // Directory operations
        std::string dirname = "/benchmark/dir_" + std::to_string(i);
        GetFilesystem()->CreateDirectory(dirname, 0755);
        GetFilesystem()->RemoveDirectory(dirname);
        
        measurement.RecordOperation(1024 + 512); // Bytes processed
    }
    
    measurement.EndMeasurement();
    measurement.PrintResults();
    
    // Verify performance is reasonable
    EXPECT_LT(measurement.GetAverageLatency(), 50.0) // Less than 50ms average
        << "Benchmark performance is below expectations";
}

// Cache eviction performance test
TEST_F(PS4FilesystemPerformanceTest, CacheEvictionPerformanceDetailed) {
    // Initialize cache with specific size to control evictions
    const uint64_t cache_size = 2 * 1024 * 1024; // 2MB cache
    GetFilesystem()->InitializeCache(cache_size);

    const int num_files = 1000;
    const size_t file_size = 4096; // 4KB per file

    PerformanceMeasurement measurement;
    measurement.StartMeasurement("Cache Eviction Performance");

    // Fill cache beyond capacity to trigger evictions
    for (int i = 0; i < num_files; ++i) {
        std::string filename = "/cache_eviction/file_" + std::to_string(i) + ".bin";
        std::vector<uint8_t> data = GenerateTestData(file_size, static_cast<uint8_t>(i % 256));

        auto start = GetCurrentTime();
        GetFilesystem()->CacheFile(filename, std::move(data));
        auto duration = GetElapsedTime(start);

        // Record individual operation time
        measurement.RecordOperation(file_size);

        // Check cache statistics periodically
        if (i % 100 == 0) {
            uint64_t current_usage = GetFilesystem()->GetCacheUsage();
            double hit_ratio = GetFilesystem()->GetCacheHitRatio();

            std::cout << "After " << i << " files: Cache usage = "
                      << FormatBytes(current_usage) << ", Hit ratio = "
                      << std::fixed << std::setprecision(2) << hit_ratio << std::endl;
        }
    }

    measurement.EndMeasurement();
    measurement.PrintResults();

    // Verify cache is still functional after evictions
    EXPECT_LE(GetFilesystem()->GetCacheUsage(), cache_size);
    EXPECT_GT(GetFilesystem()->GetCacheHitRatio(), 0.0);
}

// File seek performance test
TEST_F(PS4FilesystemPerformanceTest, FileSeekPerformance) {
    // Mount test directory
    std::wstring wide_path(GetTestDirectory().wstring());
    ASSERT_TRUE(GetFilesystem()->MountDirectory(wide_path));

    std::string guest_path = "/mnt/" + GetTestDirectory().filename().string() + "/large_file.bin";

    int fd = GetFilesystem()->OpenFile(guest_path, O_RDONLY, 0644);
    ASSERT_GT(fd, 0);

    const int num_seeks = 1000;
    std::vector<off_t> seek_positions;

    // Generate random seek positions
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<off_t> dis(0, large_file_data_.size() - 1);

    for (int i = 0; i < num_seeks; ++i) {
        seek_positions.push_back(dis(gen));
    }

    // Measure seek performance
    auto start_time = GetCurrentTime();

    for (off_t pos : seek_positions) {
        off_t result = GetFilesystem()->SeekFile(fd, pos, SEEK_SET);
        EXPECT_EQ(result, pos);
    }

    auto total_time = GetElapsedTime(start_time);
    auto avg_time = total_time / num_seeks;

    GetFilesystem()->CloseFile(fd);

    std::cout << "Seek performance: " << FormatDuration(avg_time)
              << " average per seek operation" << std::endl;

    // Seek operations should be very fast
    EXPECT_LT(avg_time, std::chrono::microseconds(100));
}

// Directory operation performance test
TEST_F(PS4FilesystemPerformanceTest, DirectoryOperationPerformance) {
    const int num_directories = 500;
    std::vector<std::string> directory_names;

    // Generate directory names
    for (int i = 0; i < num_directories; ++i) {
        directory_names.push_back("/perf_test/dir_" + std::to_string(i));
    }

    // Measure directory creation performance
    auto start_time = GetCurrentTime();

    for (const auto& dirname : directory_names) {
        bool success = GetFilesystem()->CreateDirectory(dirname, 0755);
        EXPECT_TRUE(success);
    }

    auto create_time = GetElapsedTime(start_time);
    auto avg_create_time = create_time / num_directories;

    // Measure directory removal performance
    start_time = GetCurrentTime();

    for (const auto& dirname : directory_names) {
        bool success = GetFilesystem()->RemoveDirectory(dirname);
        EXPECT_TRUE(success);
    }

    auto remove_time = GetElapsedTime(start_time);
    auto avg_remove_time = remove_time / num_directories;

    std::cout << "Directory create performance: " << FormatDuration(avg_create_time)
              << " average per directory" << std::endl;
    std::cout << "Directory remove performance: " << FormatDuration(avg_remove_time)
              << " average per directory" << std::endl;

    // Directory operations should be reasonably fast
    EXPECT_LT(avg_create_time, std::chrono::milliseconds(10));
    EXPECT_LT(avg_remove_time, std::chrono::milliseconds(10));
}

// Stress test for mixed operations
TEST_F(PS4FilesystemPerformanceTest, MixedOperationsStressTest) {
    // Mount test directory
    std::wstring wide_path(GetTestDirectory().wstring());
    ASSERT_TRUE(GetFilesystem()->MountDirectory(wide_path));

    // Initialize cache
    GetFilesystem()->InitializeCache(5 * 1024 * 1024);

    const int num_iterations = 1000;
    const int num_threads = 4;
    std::vector<std::thread> threads;
    std::atomic<int> completed_operations{0};
    std::atomic<bool> performance_issue{false};

    auto start_time = GetCurrentTime();

    // Launch threads performing mixed operations
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, t, num_iterations, &completed_operations, &performance_issue]() {
            std::string guest_path = "/mnt/" + GetTestDirectory().filename().string() + "/small_file.bin";

            for (int i = 0; i < num_iterations; ++i) {
                auto op_start = GetCurrentTime();

                int operation = (t * num_iterations + i) % 5;
                bool success = false;

                switch (operation) {
                    case 0: {
                        // File read operation
                        int fd = GetFilesystem()->OpenFile(guest_path, O_RDONLY, 0644);
                        if (fd > 0) {
                            std::vector<uint8_t> buffer(512);
                            ssize_t bytes_read = GetFilesystem()->ReadFile(fd, buffer.data(), buffer.size());
                            GetFilesystem()->CloseFile(fd);
                            success = (bytes_read > 0);
                        }
                        break;
                    }
                    case 1: {
                        // Cache operation
                        std::string cache_file = "/stress/cache_" + std::to_string(t) + "_" + std::to_string(i);
                        std::vector<uint8_t> data = GenerateTestData(256, static_cast<uint8_t>(i));
                        GetFilesystem()->CacheFile(cache_file, std::move(data));
                        success = true;
                        break;
                    }
                    case 2: {
                        // Directory operation
                        std::string dirname = "/stress/dir_" + std::to_string(t) + "_" + std::to_string(i);
                        success = GetFilesystem()->CreateDirectory(dirname, 0755);
                        if (success) {
                            GetFilesystem()->RemoveDirectory(dirname);
                        }
                        break;
                    }
                    case 3: {
                        // Cache retrieval
                        std::string cache_file = "/stress/cache_" + std::to_string(t) + "_" + std::to_string(i - 10);
                        std::vector<uint8_t> data;
                        GetFilesystem()->GetCachedFile(cache_file, data);
                        success = true;
                        break;
                    }
                    case 4: {
                        // State dump (lightweight operation)
                        std::string state = GetFilesystem()->DumpState();
                        success = !state.empty();
                        break;
                    }
                }

                auto op_duration = GetElapsedTime(op_start);

                // Flag performance issues if any operation takes too long
                if (op_duration > std::chrono::milliseconds(100)) {
                    performance_issue = true;
                }

                if (success) {
                    completed_operations++;
                }
            }
        });
    }

    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }

    auto total_time = GetElapsedTime(start_time);
    int total_operations = completed_operations.load();
    auto avg_time = total_time / total_operations;

    std::cout << "Mixed operations stress test: " << FormatDuration(avg_time)
              << " average per operation (" << num_threads << " threads, "
              << total_operations << " total operations)" << std::endl;

    EXPECT_FALSE(performance_issue.load()) << "Some operations took longer than expected";
    EXPECT_GT(total_operations, num_threads * num_iterations * 0.9) // At least 90% success rate
        << "Too many operations failed";
}
