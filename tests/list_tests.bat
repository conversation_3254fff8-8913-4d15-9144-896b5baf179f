@echo off
REM PS4 Filesystem Test List Script for Windows

echo ========================================
echo PS4 Filesystem Test Suite - Available Tests
echo ========================================

if not exist "Debug\ps4_filesystem_tests.exe" (
    echo Error: Test executable not found. Please build the tests first.
    echo Run: cmake --build . --target ps4_filesystem_tests
    pause
    exit /b 1
)

echo.
echo Available Test Suites:
echo.

echo 1. FileLRUCache Tests:
Debug\ps4_filesystem_tests.exe --gtest_list_tests --gtest_filter="FileLRUCacheTest.*"

echo.
echo 2. MountPointManager Tests:
Debug\ps4_filesystem_tests.exe --gtest_list_tests --gtest_filter="MountPointManagerTest.*"

echo.
echo 3. HandleTable Tests:
Debug\ps4_filesystem_tests.exe --gtest_list_tests --gtest_filter="HandleTableTest.*"

echo.
echo 4. PS4Filesystem PFS Tests:
Debug\ps4_filesystem_tests.exe --gtest_list_tests --gtest_filter="PS4FilesystemPFSTest.*"

echo.
echo 5. PS4Filesystem Device Tests:
Debug\ps4_filesystem_tests.exe --gtest_list_tests --gtest_filter="PS4FilesystemDeviceTest.*"

echo.
echo ========================================
echo To run a specific test suite, use:
echo ps4_filesystem_tests.exe --gtest_filter="TestSuiteName.*"
echo.
echo To run all tests, use:
echo ps4_filesystem_tests.exe
echo ========================================

pause
