cmake_minimum_required(VERSION 3.16)
project(ps4_filesystem_tests)

set(CMAKE_TOOLCHAIN_FILE "D:/vcpkg/scripts/buildsystems/vcpkg.cmake" CACHE PATH "Vcpkg toolchain file")
# PS4 Filesystem Test Suite CMakeLists.txt

# Enable testing
enable_testing()

# Find Google Test
set(GTest_DIR "D:/grok/lib/cmake/GTest")
find_package(GTest CONFIG REQUIRED)
include(GoogleTest)

# packages
find_package(spdlog CONFIG REQUIRED)
find_package(nlohmann_json CONFIG REQUIRED)
find_package(OpenSSL REQUIRED)

# Test include directories
set(TEST_INCLUDE_DIRS
    ${CMAKE_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/..
    ${CMAKE_SOURCE_DIR}/../ps4
    ${CMAKE_SOURCE_DIR}/../common
    ${CMAKE_SOURCE_DIR}/../memory
    ${CMAKE_SOURCE_DIR}/../emulator
    ${CMAKE_SOURCE_DIR}/../cpu
    ${GTEST_INCLUDE_DIRS}
)

# Test source files for PS4 Filesystem
set(PS4_FILESYSTEM_TEST_SOURCES
    test_file_lru_cache.cpp
    test_mount_point_manager.cpp
    test_handle_table.cpp
    # test_ps4_filesystem_core.cpp          # Temporarily disabled due to emulator dependencies
    test_ps4_filesystem_pfs.cpp
    test_ps4_filesystem_device.cpp
    # test_ps4_filesystem_integration.cpp  # Temporarily disabled due to emulator dependencies
    # test_ps4_filesystem_performance.cpp  # Temporarily disabled due to emulator dependencies
    # test_lock_ordering_compliance.cpp    # Temporarily disabled due to emulator dependencies
)

# Test utilities and fixtures
set(TEST_UTILITY_SOURCES
    utils/test_fixtures.cpp
    # utils/mock_emulator.cpp  # Temporarily disabled due to emulator dependencies
    utils/test_helpers.cpp
    utils/filesystem_test_base.cpp
)

# Create test executable
add_executable(ps4_filesystem_tests
    ${PS4_FILESYSTEM_TEST_SOURCES}
    ${TEST_UTILITY_SOURCES}
    main_test.cpp
)

# Link required libraries for tests
target_link_libraries(ps4_filesystem_tests PRIVATE
    GTest::gtest
    GTest::gtest_main
    GTest::gmock
    GTest::gmock_main
    spdlog::spdlog
    nlohmann_json::nlohmann_json
    OpenSSL::SSL
    OpenSSL::Crypto
    ${CMAKE_THREAD_LIBS_INIT}
)

# Include directories for tests
target_include_directories(ps4_filesystem_tests PRIVATE
    ${TEST_INCLUDE_DIRS}
)

# Compile definitions for tests
target_compile_definitions(ps4_filesystem_tests PRIVATE
    DEBUG_MODE
    TESTING_MODE
    _DEBUG
    SPDLOG_NO_COMPILE_TIME_FMT
)

# Test-specific compiler flags
target_compile_options(ps4_filesystem_tests PRIVATE
    $<$<CXX_COMPILER_ID:MSVC>:/W3>
    $<$<CXX_COMPILER_ID:GNU,Clang>:-Wall -Wextra>
    $<$<CXX_COMPILER_ID:Intel>:-Wall -Wextra>
)

# Add source files that tests depend on
target_sources(ps4_filesystem_tests PRIVATE
    ../ps4/ps4_filesystem.cpp
    ../common/lock_ordering.cpp
)

# Register tests with CTest
gtest_discover_tests(ps4_filesystem_tests
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    PROPERTIES VS_DEBUGGER_WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}"
)

# Individual test targets for easier debugging
add_test(NAME FileLRUCacheTests COMMAND ps4_filesystem_tests --gtest_filter="FileLRUCacheTest.*")
add_test(NAME MountPointManagerTests COMMAND ps4_filesystem_tests --gtest_filter="MountPointManagerTest.*")
add_test(NAME HandleTableTests COMMAND ps4_filesystem_tests --gtest_filter="HandleTableTest.*")
add_test(NAME PS4FilesystemCoreTests COMMAND ps4_filesystem_tests --gtest_filter="PS4FilesystemCoreTest.*")
add_test(NAME PS4FilesystemPFSTests COMMAND ps4_filesystem_tests --gtest_filter="PS4FilesystemPFSTest.*")
add_test(NAME PS4FilesystemDeviceTests COMMAND ps4_filesystem_tests --gtest_filter="PS4FilesystemDeviceTest.*")
add_test(NAME PS4FilesystemIntegrationTests COMMAND ps4_filesystem_tests --gtest_filter="PS4FilesystemIntegrationTest.*")
add_test(NAME PS4FilesystemPerformanceTests COMMAND ps4_filesystem_tests --gtest_filter="PS4FilesystemPerformanceTest.*")
add_test(NAME LockOrderingComplianceTests COMMAND ps4_filesystem_tests --gtest_filter="LockOrderingComplianceTest.*")
add_test(AllTestsInMain main)

# Test data directory
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/test_data)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/test_data/pfs)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/test_data/mount_points)

# Copy test data files
configure_file(${CMAKE_CURRENT_SOURCE_DIR}/data/test_file.bin ${CMAKE_CURRENT_BINARY_DIR}/test_data/test_file.bin COPYONLY)
configure_file(${CMAKE_CURRENT_SOURCE_DIR}/data/test_pfs.pkg ${CMAKE_CURRENT_BINARY_DIR}/test_data/pfs/test_pfs.pkg COPYONLY)

# Custom target for running all tests
add_custom_target(run_filesystem_tests
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure
    DEPENDS ps4_filesystem_tests
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
    COMMENT "Running PS4 Filesystem Tests"
)

# Custom target for running tests with verbose output
add_custom_target(run_filesystem_tests_verbose
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure --verbose
    DEPENDS ps4_filesystem_tests
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
    COMMENT "Running PS4 Filesystem Tests (Verbose)"
)

# Memory leak detection for tests (if available)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    if(CMAKE_CXX_COMPILER_ID STREQUAL "Intel")
        target_compile_options(ps4_filesystem_tests PRIVATE -fsanitize=address,leak)
        target_link_options(ps4_filesystem_tests PRIVATE -fsanitize=address,leak)
    endif()
endif()
