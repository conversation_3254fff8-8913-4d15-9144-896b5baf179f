/**
 * @file test_fixtures.h
 * @brief Common test fixtures and utilities for PS4 Filesystem tests
 */

#pragma once

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "ps4/ps4_filesystem.h"
// #include "ps4/ps4_emulator.h"  // Temporarily disabled for testing
// #include "mock_emulator.h"     // Temporarily disabled for testing
#include "filesystem_test_base.h"
#include <filesystem>
#include <memory>
#include <vector>
#include <string>
#include <chrono>
#include <functional>

namespace ps4::test {

/**
 * @brief Base test fixture for filesystem tests
 */
class FilesystemTestFixture : public ::testing::Test {
protected:
    void SetUp() override;
    void TearDown() override;
    
    // Helper methods for creating test data
    std::string CreateTestFile(const std::string& filename, const std::vector<uint8_t>& data);
    std::string CreateTestDirectory(const std::string& dirname);
    std::vector<uint8_t> GenerateTestData(size_t size, uint8_t pattern = 0xAB);
    
    // Test directory management
    std::filesystem::path GetTestDirectory() const { return test_dir_; }
    
    // Access to test objects
    PS4Filesystem* GetFilesystem() { return filesystem_.get(); }
    // MockPS4Emulator* GetMockEmulator() { return mock_emulator_.get(); }
    
private:
    void SetupDefaultMockExpectations();
    static uint64_t GetTestId();
    
protected:
    std::filesystem::path test_dir_;
    // std::unique_ptr<MockPS4Emulator> mock_emulator_;
    std::unique_ptr<PS4Filesystem> filesystem_;
};

/**
 * @brief Test fixture specifically for cache testing
 */
class CacheTestFixture : public ::testing::Test {
protected:
    void SetUp() override;
    void TearDown() override;
    
    // Helper methods
    void FillCache();
    std::vector<uint8_t> GenerateTestData(size_t size, uint8_t pattern = 0xAB);
    
    // Access to cache
    FileLRUCache* GetCache() { return cache_.get(); }
    
    // Test parameters
    static constexpr uint64_t cache_size_ = 1024 * 1024; // 1MB cache
    
private:
    std::unique_ptr<FileLRUCache> cache_;
};

/**
 * @brief Test fixture for performance testing
 */
class PerformanceTestFixture : public FilesystemTestFixture {
protected:
    void SetUp() override;
    void TearDown() override;
    
    // Performance measurement utilities
    std::chrono::microseconds MeasureOperation(const std::function<void()>& operation);
    
    // Pre-created test data for performance tests
    std::vector<uint8_t> small_file_data_;
    std::vector<uint8_t> medium_file_data_;
    std::vector<uint8_t> large_file_data_;
    
    std::string small_file_path_;
    std::string medium_file_path_;
    std::string large_file_path_;
};

/**
 * @brief Test fixture for lock ordering compliance tests
 */
class LockOrderingTestFixture : public FilesystemTestFixture {
protected:
    void SetUp() override {
        FilesystemTestFixture::SetUp();
        // Enable lock ordering checks in debug mode
    }
    
    // Helper methods for testing lock ordering
    void TestConcurrentAccess(const std::function<void()>& operation1,
                             const std::function<void()>& operation2);
};

/**
 * @brief Test fixture for PFS (PlayStation File System) testing
 */
class PFSTestFixture : public FilesystemTestFixture {
protected:
    void SetUp() override;
    void TearDown() override;
    
    // PFS-specific helper methods
    std::string CreatePFSFile(const std::string& filename, size_t size, bool encrypted = true);
    std::vector<uint8_t> GeneratePFSKey();
    
private:
    std::vector<uint8_t> pfs_key_;
    std::string pfs_mount_point_;
};

/**
 * @brief Test fixture for device file testing
 */
class DeviceFileTestFixture : public FilesystemTestFixture {
protected:
    void SetUp() override;
    void TearDown() override;
    
    // Device file helper methods
    void RegisterTestDeviceHandler(const std::string& devicePath);
    
private:
    std::vector<std::string> registered_devices_;
};

/**
 * @brief Test fixture for integration testing
 */
class IntegrationTestFixture : public FilesystemTestFixture {
protected:
    void SetUp() override;
    void TearDown() override;
    
    // Integration test helper methods
    void SetupComplexFileStructure();
    void VerifyFileSystemIntegrity();
    
private:
    std::vector<std::string> created_files_;
    std::vector<std::string> created_directories_;
};

// Utility functions for test data generation
std::vector<uint8_t> GenerateTestData(size_t size, uint8_t pattern = 0xAB);
std::string GenerateRandomString(size_t length);

// Test parameter structures
struct FileTestParams {
    std::string filename;
    size_t size;
    int flags;
    mode_t mode;
    bool should_succeed;
};

struct CacheTestParams {
    uint64_t cache_size;
    size_t num_files;
    size_t file_size;
    bool expect_eviction;
};

struct PerformanceTestParams {
    size_t file_size;
    size_t num_operations;
    std::chrono::microseconds max_expected_time;
};

// Parameterized test helpers
class FileOperationTest : public FilesystemTestFixture,
                         public ::testing::WithParamInterface<FileTestParams> {
};

class CachePerformanceTest : public CacheTestFixture,
                            public ::testing::WithParamInterface<CacheTestParams> {
};

class FilesystemPerformanceTest : public PerformanceTestFixture,
                                 public ::testing::WithParamInterface<PerformanceTestParams> {
};

// Custom matchers for filesystem testing
MATCHER_P(FileExists, path, "File exists at path") {
    return std::filesystem::exists(path);
}

MATCHER_P(DirectoryExists, path, "Directory exists at path") {
    return std::filesystem::exists(path) && std::filesystem::is_directory(path);
}

MATCHER_P2(FileHasSize, path, expected_size, "File has expected size") {
    if (!std::filesystem::exists(path)) {
        return false;
    }
    return std::filesystem::file_size(path) == expected_size;
}

MATCHER_P2(FileContains, path, expected_data, "File contains expected data") {
    if (!std::filesystem::exists(path)) {
        return false;
    }
    
    std::ifstream file(path, std::ios::binary);
    if (!file.is_open()) {
        return false;
    }
    
    std::vector<uint8_t> actual_data((std::istreambuf_iterator<char>(file)),
                                     std::istreambuf_iterator<char>());
    
    return actual_data == expected_data;
}

} // namespace ps4::test
