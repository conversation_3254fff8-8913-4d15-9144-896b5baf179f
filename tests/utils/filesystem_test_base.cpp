/**
 * @file filesystem_test_base.cpp
 * @brief Base class implementation for filesystem testing
 */

#include "filesystem_test_base.h"
#include <filesystem>
#include <fstream>

namespace ps4::test {

FilesystemTestBase::FilesystemTestBase() = default;

FilesystemTestBase::~FilesystemTestBase() = default;

void FilesystemTestBase::SetUpTestEnvironment() {
    // Create base test directory
    test_root_dir_ = std::filesystem::temp_directory_path() / "ps4_fs_test_base";
    std::filesystem::create_directories(test_root_dir_);
    
    // Create standard subdirectories
    std::filesystem::create_directories(test_root_dir_ / "files");
    std::filesystem::create_directories(test_root_dir_ / "cache");
    std::filesystem::create_directories(test_root_dir_ / "mount_points");
    std::filesystem::create_directories(test_root_dir_ / "temp");
}

void FilesystemTestBase::TearDownTestEnvironment() {
    // Clean up test directory
    try {
        if (std::filesystem::exists(test_root_dir_)) {
            std::filesystem::remove_all(test_root_dir_);
        }
    } catch (const std::exception& e) {
        // Log warning but don't fail
        std::cerr << "Warning: Failed to clean up test environment: " << e.what() << std::endl;
    }
}

std::filesystem::path FilesystemTestBase::GetTestRootDirectory() const {
    return test_root_dir_;
}

std::filesystem::path FilesystemTestBase::GetTestFilesDirectory() const {
    return test_root_dir_ / "files";
}

std::filesystem::path FilesystemTestBase::GetTestCacheDirectory() const {
    return test_root_dir_ / "cache";
}

std::filesystem::path FilesystemTestBase::GetTestMountPointsDirectory() const {
    return test_root_dir_ / "mount_points";
}

std::filesystem::path FilesystemTestBase::GetTestTempDirectory() const {
    return test_root_dir_ / "temp";
}

std::string FilesystemTestBase::CreateTestFile(const std::string& relative_path, 
                                               const std::vector<uint8_t>& content) {
    std::filesystem::path full_path = GetTestFilesDirectory() / relative_path;
    
    // Create parent directories if needed
    std::filesystem::create_directories(full_path.parent_path());
    
    // Write file content
    std::ofstream file(full_path, std::ios::binary);
    if (!file.is_open()) {
        throw std::runtime_error("Failed to create test file: " + full_path.string());
    }
    
    file.write(reinterpret_cast<const char*>(content.data()), content.size());
    file.close();
    
    return full_path.string();
}

std::string FilesystemTestBase::CreateTestDirectory(const std::string& relative_path) {
    std::filesystem::path full_path = GetTestFilesDirectory() / relative_path;
    std::filesystem::create_directories(full_path);
    return full_path.string();
}

bool FilesystemTestBase::DeleteTestFile(const std::string& relative_path) {
    std::filesystem::path full_path = GetTestFilesDirectory() / relative_path;
    try {
        return std::filesystem::remove(full_path);
    } catch (const std::exception&) {
        return false;
    }
}

bool FilesystemTestBase::DeleteTestDirectory(const std::string& relative_path) {
    std::filesystem::path full_path = GetTestFilesDirectory() / relative_path;
    try {
        return std::filesystem::remove_all(full_path) > 0;
    } catch (const std::exception&) {
        return false;
    }
}

bool FilesystemTestBase::TestFileExists(const std::string& relative_path) const {
    std::filesystem::path full_path = GetTestFilesDirectory() / relative_path;
    return std::filesystem::exists(full_path) && std::filesystem::is_regular_file(full_path);
}

bool FilesystemTestBase::TestDirectoryExists(const std::string& relative_path) const {
    std::filesystem::path full_path = GetTestFilesDirectory() / relative_path;
    return std::filesystem::exists(full_path) && std::filesystem::is_directory(full_path);
}

size_t FilesystemTestBase::GetTestFileSize(const std::string& relative_path) const {
    std::filesystem::path full_path = GetTestFilesDirectory() / relative_path;
    try {
        return std::filesystem::file_size(full_path);
    } catch (const std::exception&) {
        return 0;
    }
}

std::vector<uint8_t> FilesystemTestBase::ReadTestFile(const std::string& relative_path) const {
    std::filesystem::path full_path = GetTestFilesDirectory() / relative_path;
    
    std::ifstream file(full_path, std::ios::binary);
    if (!file.is_open()) {
        return {};
    }
    
    std::vector<uint8_t> content((std::istreambuf_iterator<char>(file)),
                                 std::istreambuf_iterator<char>());
    return content;
}

void FilesystemTestBase::CreateTestFileStructure() {
    // Create a standard test file structure
    
    // Small files
    CreateTestFile("small/file1.txt", GenerateTestData(100, 0x01));
    CreateTestFile("small/file2.bin", GenerateTestData(256, 0x02));
    CreateTestFile("small/file3.dat", GenerateTestData(512, 0x03));
    
    // Medium files
    CreateTestFile("medium/data1.bin", GenerateTestData(4096, 0x11));
    CreateTestFile("medium/data2.bin", GenerateTestData(8192, 0x12));
    
    // Large files
    CreateTestFile("large/bigfile1.bin", GenerateTestData(64 * 1024, 0x21));
    CreateTestFile("large/bigfile2.bin", GenerateTestData(128 * 1024, 0x22));
    
    // Nested directories
    CreateTestFile("nested/level1/level2/deep_file.txt", GenerateTestData(1024, 0x31));
    
    // Special files
    CreateTestFile("special/empty.bin", {});
    CreateTestFile("special/single_byte.bin", {0xFF});
    
    // Files with special names
    CreateTestFile("special/file with spaces.txt", GenerateTestData(200, 0x41));
    CreateTestFile("special/file.with.dots.bin", GenerateTestData(300, 0x42));
}

std::vector<uint8_t> FilesystemTestBase::GenerateTestData(size_t size, uint8_t pattern) {
    std::vector<uint8_t> data(size);
    
    if (pattern == 0) {
        // Generate random data
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<int> dis(0, 255);

        for (auto& byte : data) {
            byte = static_cast<uint8_t>(dis(gen));
        }
    } else {
        // Generate pattern-based data
        for (size_t i = 0; i < size; ++i) {
            data[i] = static_cast<uint8_t>((pattern + i) % 256);
        }
    }
    
    return data;
}

std::string FilesystemTestBase::GenerateUniqueFilename(const std::string& prefix, 
                                                       const std::string& extension) {
    static std::atomic<uint64_t> counter{0};
    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    
    return prefix + "_" + std::to_string(timestamp) + "_" + 
           std::to_string(counter++) + extension;
}

void FilesystemTestBase::CleanupTestFiles() {
    try {
        if (std::filesystem::exists(GetTestFilesDirectory())) {
            std::filesystem::remove_all(GetTestFilesDirectory());
            std::filesystem::create_directories(GetTestFilesDirectory());
        }
    } catch (const std::exception& e) {
        std::cerr << "Warning: Failed to cleanup test files: " << e.what() << std::endl;
    }
}

} // namespace ps4::test
