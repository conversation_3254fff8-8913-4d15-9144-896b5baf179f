/**
 * @file test_helpers.cpp
 * @brief Helper functions and utilities for PS4 filesystem tests
 */

#include "test_helpers.h"
#include <fstream>
#include <random>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <iostream>
#include <algorithm>

namespace ps4::test {

// File system test helpers
bool CreateTestFileWithContent(const std::filesystem::path& path, const std::string& content) {
    std::ofstream file(path);
    if (!file.is_open()) {
        return false;
    }
    
    file << content;
    return file.good();
}

bool CreateTestFileWithBinaryData(const std::filesystem::path& path, const std::vector<uint8_t>& data) {
    std::ofstream file(path, std::ios::binary);
    if (!file.is_open()) {
        return false;
    }
    
    file.write(reinterpret_cast<const char*>(data.data()), data.size());
    return file.good();
}

bool ReadFileContent(const std::filesystem::path& path, std::string& content) {
    std::ifstream file(path);
    if (!file.is_open()) {
        return false;
    }
    
    content.assign((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    return true;
}

bool ReadFileBinaryData(const std::filesystem::path& path, std::vector<uint8_t>& data) {
    std::ifstream file(path, std::ios::binary);
    if (!file.is_open()) {
        return false;
    }
    
    data.assign((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    return true;
}

bool CompareFiles(const std::filesystem::path& path1, const std::filesystem::path& path2) {
    std::vector<uint8_t> data1, data2;
    
    if (!ReadFileBinaryData(path1, data1) || !ReadFileBinaryData(path2, data2)) {
        return false;
    }
    
    return data1 == data2;
}

// Data generation helpers
std::vector<uint8_t> GenerateSequentialData(size_t size, uint8_t start_value) {
    std::vector<uint8_t> data(size);
    for (size_t i = 0; i < size; ++i) {
        data[i] = static_cast<uint8_t>((start_value + i) % 256);
    }
    return data;
}

std::vector<uint8_t> GenerateRandomData(size_t size, uint32_t seed) {
    std::vector<uint8_t> data(size);
    std::mt19937 gen(seed);
    std::uniform_int_distribution<int> dis(0, 255);

    for (auto& byte : data) {
        byte = static_cast<uint8_t>(dis(gen));
    }
    
    return data;
}

std::vector<uint8_t> GeneratePatternData(size_t size, const std::vector<uint8_t>& pattern) {
    std::vector<uint8_t> data(size);
    
    for (size_t i = 0; i < size; ++i) {
        data[i] = pattern[i % pattern.size()];
    }
    
    return data;
}

std::string GenerateRandomString(size_t length, const std::string& charset) {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, charset.size() - 1);
    
    std::string result;
    result.reserve(length);
    
    for (size_t i = 0; i < length; ++i) {
        result += charset[dis(gen)];
    }
    
    return result;
}

// Path manipulation helpers
std::string NormalizePath(const std::string& path) {
    std::filesystem::path p(path);
    return p.lexically_normal().string();
}

std::string JoinPaths(const std::string& base, const std::string& relative) {
    std::filesystem::path result = std::filesystem::path(base) / relative;
    return result.string();
}

std::vector<std::string> SplitPath(const std::string& path) {
    std::vector<std::string> components;
    std::filesystem::path p(path);
    
    for (const auto& component : p) {
        if (component != "/" && component != "\\") {
            components.push_back(component.string());
        }
    }
    
    return components;
}

// Timing and performance helpers
TimePoint GetCurrentTime() {
    return std::chrono::high_resolution_clock::now();
}

Duration GetElapsedTime(const TimePoint& start) {
    return std::chrono::high_resolution_clock::now() - start;
}

double GetElapsedMilliseconds(const TimePoint& start) {
    auto elapsed = GetElapsedTime(start);
    return std::chrono::duration<double, std::milli>(elapsed).count();
}

double GetElapsedMicroseconds(const TimePoint& start) {
    auto elapsed = GetElapsedTime(start);
    return std::chrono::duration<double, std::micro>(elapsed).count();
}

// Test validation helpers
bool ValidateFileDescriptor(int fd) {
    return fd >= 0;
}

bool ValidateFileSize(const std::filesystem::path& path, size_t expected_size) {
    try {
        return std::filesystem::file_size(path) == expected_size;
    } catch (const std::exception&) {
        return false;
    }
}

bool ValidateDirectoryExists(const std::filesystem::path& path) {
    return std::filesystem::exists(path) && std::filesystem::is_directory(path);
}

bool ValidateFileExists(const std::filesystem::path& path) {
    return std::filesystem::exists(path) && std::filesystem::is_regular_file(path);
}

// Error simulation helpers
ErrorSimulator::ErrorSimulator() : error_rate_(0.0), gen_(std::random_device{}()) {}

void ErrorSimulator::SetErrorRate(double rate) {
    error_rate_ = std::clamp(rate, 0.0, 1.0);
}

bool ErrorSimulator::ShouldSimulateError() {
    if (error_rate_ <= 0.0) {
        return false;
    }
    
    std::uniform_real_distribution<double> dis(0.0, 1.0);
    return dis(gen_) < error_rate_;
}

void ErrorSimulator::SimulateDelay(std::chrono::milliseconds min_delay, std::chrono::milliseconds max_delay) {
    if (min_delay >= max_delay) {
        std::this_thread::sleep_for(min_delay);
        return;
    }
    
    std::uniform_int_distribution<int> dis(min_delay.count(), max_delay.count());
    auto delay = std::chrono::milliseconds(dis(gen_));
    std::this_thread::sleep_for(delay);
}

// Memory helpers
std::vector<uint8_t> AllocateAlignedBuffer(size_t size, size_t alignment) {
    // Simple aligned allocation for testing
    size_t total_size = size + alignment - 1;
    std::vector<uint8_t> buffer(total_size);
    
    // Find aligned offset
    uintptr_t addr = reinterpret_cast<uintptr_t>(buffer.data());
    uintptr_t aligned_addr = (addr + alignment - 1) & ~(alignment - 1);
    size_t offset = aligned_addr - addr;
    
    // Resize to actual needed size starting from aligned offset
    buffer.erase(buffer.begin(), buffer.begin() + offset);
    buffer.resize(size);
    
    return buffer;
}

bool IsAligned(const void* ptr, size_t alignment) {
    uintptr_t addr = reinterpret_cast<uintptr_t>(ptr);
    return (addr % alignment) == 0;
}

// String formatting helpers
std::string FormatBytes(uint64_t bytes) {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    const size_t num_units = sizeof(units) / sizeof(units[0]);
    
    double size = static_cast<double>(bytes);
    size_t unit_index = 0;
    
    while (size >= 1024.0 && unit_index < num_units - 1) {
        size /= 1024.0;
        unit_index++;
    }
    
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2) << size << " " << units[unit_index];
    return oss.str();
}

std::string FormatDuration(const Duration& duration) {
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(duration);
    auto us = std::chrono::duration_cast<std::chrono::microseconds>(duration - ms);
    
    std::ostringstream oss;
    if (ms.count() > 0) {
        oss << ms.count() << "ms";
        if (us.count() > 0) {
            oss << " " << us.count() << "μs";
        }
    } else {
        oss << us.count() << "μs";
    }
    
    return oss.str();
}

std::string FormatHex(const std::vector<uint8_t>& data, size_t max_bytes) {
    std::ostringstream oss;
    size_t bytes_to_show = std::min(data.size(), max_bytes);
    
    for (size_t i = 0; i < bytes_to_show; ++i) {
        if (i > 0) oss << " ";
        oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(data[i]);
    }
    
    if (data.size() > max_bytes) {
        oss << " ... (" << (data.size() - max_bytes) << " more bytes)";
    }
    
    return oss.str();
}

// Test result helpers
TestResult::TestResult(bool success, const std::string& message) 
    : success_(success), message_(message) {}

bool TestResult::IsSuccess() const {
    return success_;
}

const std::string& TestResult::GetMessage() const {
    return message_;
}

TestResult TestResult::Success(const std::string& message) {
    return TestResult(true, message);
}

TestResult TestResult::Failure(const std::string& message) {
    return TestResult(false, message);
}

// PerformanceMeasurement implementation
void PerformanceMeasurement::StartMeasurement(const std::string& operation_name) {
    operation_name_ = operation_name;
    start_time_ = GetCurrentTime();
    operation_times_.clear();
    total_bytes_processed_ = 0;
}

void PerformanceMeasurement::EndMeasurement() {
    // Record the overall measurement time
    auto end_time = GetCurrentTime();
    auto total_duration = end_time - start_time_;
    operation_times_.push_back(total_duration);
}

void PerformanceMeasurement::RecordOperation(size_t bytes_processed) {
    auto current_time = GetCurrentTime();
    auto operation_duration = current_time - start_time_;
    operation_times_.push_back(operation_duration);
    total_bytes_processed_ += bytes_processed;
    start_time_ = current_time; // Reset for next operation
}

double PerformanceMeasurement::GetAverageLatency() const {
    if (operation_times_.empty()) {
        return 0.0;
    }

    auto total_duration = Duration::zero();
    for (const auto& duration : operation_times_) {
        total_duration += duration;
    }

    auto average_duration = total_duration / operation_times_.size();
    return std::chrono::duration<double, std::milli>(average_duration).count();
}

double PerformanceMeasurement::GetThroughput() const {
    if (operation_times_.empty() || total_bytes_processed_ == 0) {
        return 0.0;
    }

    auto total_duration = Duration::zero();
    for (const auto& duration : operation_times_) {
        total_duration += duration;
    }

    double total_seconds = std::chrono::duration<double>(total_duration).count();
    if (total_seconds <= 0.0) {
        return 0.0;
    }

    return static_cast<double>(total_bytes_processed_) / total_seconds;
}

size_t PerformanceMeasurement::GetOperationCount() const {
    return operation_times_.size();
}

void PerformanceMeasurement::PrintResults() const {
    std::cout << "\n=== Performance Results for: " << operation_name_ << " ===" << std::endl;
    std::cout << "Operations completed: " << GetOperationCount() << std::endl;
    std::cout << "Average latency: " << std::fixed << std::setprecision(3)
              << GetAverageLatency() << " ms" << std::endl;

    if (total_bytes_processed_ > 0) {
        std::cout << "Total bytes processed: " << FormatBytes(total_bytes_processed_) << std::endl;
        std::cout << "Throughput: " << FormatBytes(static_cast<uint64_t>(GetThroughput())) << "/s" << std::endl;
    }

    if (!operation_times_.empty()) {
        // Calculate min/max latencies
        auto min_duration = *std::min_element(operation_times_.begin(), operation_times_.end());
        auto max_duration = *std::max_element(operation_times_.begin(), operation_times_.end());

        double min_latency = std::chrono::duration<double, std::milli>(min_duration).count();
        double max_latency = std::chrono::duration<double, std::milli>(max_duration).count();

        std::cout << "Min latency: " << std::fixed << std::setprecision(3) << min_latency << " ms" << std::endl;
        std::cout << "Max latency: " << std::fixed << std::setprecision(3) << max_latency << " ms" << std::endl;
    }

    std::cout << "=================================================" << std::endl;
}

} // namespace ps4::test
