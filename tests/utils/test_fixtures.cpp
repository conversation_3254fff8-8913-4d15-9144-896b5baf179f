/**
 * @file test_fixtures.cpp
 * @brief Common test fixtures and utilities for PS4 Filesystem tests
 */

#include "test_fixtures.h"
#include <filesystem>
#include <fstream>
#include <random>

namespace ps4::test {

// FilesystemTestFixture implementation
void FilesystemTestFixture::SetUp() {
    // Create unique test directory for this test
    test_dir_ = std::filesystem::temp_directory_path() / 
                ("ps4_fs_test_" + std::to_string(GetTestId()));
    std::filesystem::create_directories(test_dir_);
    
    // Initialize mock emulator
    // mock_emulator_ = std::make_unique<MockPS4Emulator>();

    // Set up default expectations for mock emulator
    // SetupDefaultMockExpectations();

    // Create filesystem instance
    filesystem_ = std::make_unique<PS4Filesystem>();
    
    // Initialize filesystem
    ASSERT_TRUE(filesystem_->Initialize()) << "Failed to initialize filesystem";
}

void FilesystemTestFixture::TearDown() {
    // Shutdown filesystem
    if (filesystem_) {
        filesystem_->Shutdown();
        filesystem_.reset();
    }
    
    // Clean up mock emulator
    // mock_emulator_.reset();
    
    // Clean up test directory
    try {
        std::filesystem::remove_all(test_dir_);
    } catch (const std::exception& e) {
        // Log warning but don't fail test
        std::cerr << "Warning: Failed to clean up test directory: " << e.what() << std::endl;
    }
}

std::string FilesystemTestFixture::CreateTestFile(const std::string& filename, 
                                                  const std::vector<uint8_t>& data) {
    std::filesystem::path filepath = test_dir_ / filename;
    std::filesystem::create_directories(filepath.parent_path());
    
    std::ofstream file(filepath, std::ios::binary);
    if (!file.is_open()) {
        throw std::runtime_error("Failed to create test file: " + filepath.string());
    }
    
    file.write(reinterpret_cast<const char*>(data.data()), data.size());
    file.close();
    
    return filepath.string();
}

std::string FilesystemTestFixture::CreateTestDirectory(const std::string& dirname) {
    std::filesystem::path dirpath = test_dir_ / dirname;
    std::filesystem::create_directories(dirpath);
    return dirpath.string();
}

std::vector<uint8_t> FilesystemTestFixture::GenerateTestData(size_t size, uint8_t pattern) {
    std::vector<uint8_t> data(size);
    if (pattern == 0) {
        // Generate random data
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<int> dis(0, 255);

        for (auto& byte : data) {
            byte = static_cast<uint8_t>(dis(gen));
        }
    } else {
        // Fill with pattern
        std::fill(data.begin(), data.end(), pattern);
    }
    return data;
}

void FilesystemTestFixture::SetupDefaultMockExpectations() {
    // Set up default mock behavior that most tests will need
    // This can be overridden in specific tests
    // mock_emulator_->SetupDefaultBehavior();
}

uint64_t FilesystemTestFixture::GetTestId() {
    static uint64_t counter = 0;
    return ++counter;
}

// CacheTestFixture implementation
void CacheTestFixture::SetUp() {
    cache_ = std::make_unique<FileLRUCache>(cache_size_);
}

void CacheTestFixture::TearDown() {
    if (cache_) {
        cache_->Clear();
        cache_.reset();
    }
}

std::vector<uint8_t> CacheTestFixture::GenerateTestData(size_t size, uint8_t pattern) {
    std::vector<uint8_t> data(size);
    if (pattern == 0) {
        // Generate random data
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<int> dis(0, 255);

        for (auto& byte : data) {
            byte = static_cast<uint8_t>(dis(gen));
        }
    } else {
        std::fill(data.begin(), data.end(), pattern);
    }
    return data;
}

void CacheTestFixture::FillCache() {
    // Fill cache to near capacity
    size_t file_size = 1024; // 1KB per file
    size_t num_files = (cache_size_ / file_size) - 1; // Leave some room

    for (size_t i = 0; i < num_files; ++i) {
        std::string filename = "/test/file_" + std::to_string(i) + ".bin";
        std::vector<uint8_t> data = GenerateTestData(file_size, static_cast<uint8_t>(i));
        cache_->Put(filename, std::move(data));
    }
}

// PerformanceTestFixture implementation
void PerformanceTestFixture::SetUp() {
    FilesystemTestFixture::SetUp();
    
    // Create performance test data
    small_file_data_ = GenerateTestData(1024); // 1KB
    medium_file_data_ = GenerateTestData(64 * 1024); // 64KB
    large_file_data_ = GenerateTestData(1024 * 1024); // 1MB
    
    // Create test files
    small_file_path_ = CreateTestFile("small_file.bin", small_file_data_);
    medium_file_path_ = CreateTestFile("medium_file.bin", medium_file_data_);
    large_file_path_ = CreateTestFile("large_file.bin", large_file_data_);
}

void PerformanceTestFixture::TearDown() {
    FilesystemTestFixture::TearDown();
}

std::chrono::microseconds PerformanceTestFixture::MeasureOperation(
    const std::function<void()>& operation) {
    auto start = std::chrono::high_resolution_clock::now();
    operation();
    auto end = std::chrono::high_resolution_clock::now();
    
    return std::chrono::duration_cast<std::chrono::microseconds>(end - start);
}

// Test data generators
std::vector<uint8_t> GenerateTestData(size_t size, uint8_t pattern) {
    std::vector<uint8_t> data(size);
    if (pattern == 0) {
        // Generate random data
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<int> dis(0, 255);

        for (auto& byte : data) {
            byte = static_cast<uint8_t>(dis(gen));
        }
    } else {
        // Fill with pattern
        std::fill(data.begin(), data.end(), pattern);
    }
    return data;
}



std::string GenerateRandomString(size_t length) {
    const std::string chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, chars.size() - 1);

    std::string result;
    result.reserve(length);

    for (size_t i = 0; i < length; ++i) {
        result += chars[dis(gen)];
    }

    return result;
}

// PFSTestFixture implementation
void PFSTestFixture::SetUp() {
    FilesystemTestFixture::SetUp();

    // Generate PFS key for testing
    pfs_key_ = GeneratePFSKey();
    pfs_mount_point_ = "/pfs_test_mount";
}

void PFSTestFixture::TearDown() {
    // Unmount any PFS mounts
    if (!pfs_mount_point_.empty()) {
        GetFilesystem()->UnmountPFS(pfs_mount_point_);
    }

    FilesystemTestFixture::TearDown();
}

std::string PFSTestFixture::CreatePFSFile(const std::string& filename, size_t size, bool encrypted) {
    // Create PFS file using the filesystem
    bool success = GetFilesystem()->CreatePFSFile(filename, size, encrypted);
    if (!success) {
        throw std::runtime_error("Failed to create PFS file: " + filename);
    }
    return filename;
}

std::vector<uint8_t> PFSTestFixture::GeneratePFSKey() {
    // Generate a 256-bit (32-byte) key for PFS encryption
    return GenerateTestData(32, 0);
}

// DeviceFileTestFixture implementation
void DeviceFileTestFixture::SetUp() {
    FilesystemTestFixture::SetUp();

    // Initialize device files
    GetFilesystem()->InitializeAllDeviceFiles();
}

void DeviceFileTestFixture::TearDown() {
    // Clean up registered devices
    for (const auto& device : registered_devices_) {
        // Note: There's no unregister method in the interface,
        // so we just track them for potential cleanup
    }
    registered_devices_.clear();

    FilesystemTestFixture::TearDown();
}

void DeviceFileTestFixture::RegisterTestDeviceHandler(const std::string& devicePath) {
    auto test_handler = [](void* buffer, size_t size, bool is_write) -> bool {
        if (!is_write && buffer && size > 0) {
            // For read operations, fill with test pattern
            memset(buffer, 0xAB, size);
        }
        return true;
    };

    bool success = GetFilesystem()->RegisterDeviceHandler(devicePath, test_handler);
    if (success) {
        registered_devices_.push_back(devicePath);
    }
}

// IntegrationTestFixture implementation
void IntegrationTestFixture::SetUp() {
    FilesystemTestFixture::SetUp();

    // Set up complex file structure for integration testing
    SetupComplexFileStructure();
}

void IntegrationTestFixture::TearDown() {
    // Clean up created files and directories
    for (const auto& file : created_files_) {
        try {
            std::filesystem::remove(file);
        } catch (const std::exception&) {
            // Ignore cleanup errors
        }
    }

    for (const auto& dir : created_directories_) {
        try {
            std::filesystem::remove_all(dir);
        } catch (const std::exception&) {
            // Ignore cleanup errors
        }
    }

    FilesystemTestFixture::TearDown();
}

void IntegrationTestFixture::SetupComplexFileStructure() {
    // Create a complex directory structure for integration testing
    std::vector<std::string> directories = {
        "integration_test/level1",
        "integration_test/level1/level2",
        "integration_test/level1/level2/level3",
        "integration_test/parallel1",
        "integration_test/parallel2"
    };

    for (const auto& dir : directories) {
        std::string full_path = CreateTestDirectory(dir);
        created_directories_.push_back(full_path);
    }

    // Create test files in various directories
    std::vector<std::pair<std::string, std::vector<uint8_t>>> files = {
        {"integration_test/root_file.txt", GenerateTestData(512, 0x01)},
        {"integration_test/level1/level1_file.bin", GenerateTestData(1024, 0x02)},
        {"integration_test/level1/level2/level2_file.dat", GenerateTestData(2048, 0x03)},
        {"integration_test/level1/level2/level3/deep_file.bin", GenerateTestData(4096, 0x04)},
        {"integration_test/parallel1/parallel_file1.txt", GenerateTestData(256, 0x05)},
        {"integration_test/parallel2/parallel_file2.txt", GenerateTestData(256, 0x06)}
    };

    for (const auto& [filename, data] : files) {
        std::string full_path = CreateTestFile(filename, data);
        created_files_.push_back(full_path);
    }
}

void IntegrationTestFixture::VerifyFileSystemIntegrity() {
    // Verify that all created files and directories still exist and have correct content
    for (const auto& dir : created_directories_) {
        if (!std::filesystem::exists(dir) || !std::filesystem::is_directory(dir)) {
            throw std::runtime_error("Directory integrity check failed: " + dir);
        }
    }

    for (const auto& file : created_files_) {
        if (!std::filesystem::exists(file) || !std::filesystem::is_regular_file(file)) {
            throw std::runtime_error("File integrity check failed: " + file);
        }
    }
}

} // namespace ps4::test
