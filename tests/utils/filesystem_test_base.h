/**
 * @file filesystem_test_base.h
 * @brief Base class for filesystem testing utilities
 */

#pragma once

#include <filesystem>
#include <vector>
#include <string>
#include <chrono>
#include <random>
#include <atomic>
#include <iostream>

namespace ps4::test {

/**
 * @brief Base class providing common filesystem testing utilities
 */
class FilesystemTestBase {
public:
    FilesystemTestBase();
    virtual ~FilesystemTestBase();
    
    // Test environment setup/teardown
    void SetUpTestEnvironment();
    void TearDownTestEnvironment();
    
    // Directory access
    std::filesystem::path GetTestRootDirectory() const;
    std::filesystem::path GetTestFilesDirectory() const;
    std::filesystem::path GetTestCacheDirectory() const;
    std::filesystem::path GetTestMountPointsDirectory() const;
    std::filesystem::path GetTestTempDirectory() const;
    
    // File operations
    std::string CreateTestFile(const std::string& relative_path, const std::vector<uint8_t>& content);
    std::string CreateTestDirectory(const std::string& relative_path);
    bool DeleteTestFile(const std::string& relative_path);
    bool DeleteTestDirectory(const std::string& relative_path);
    
    // File queries
    bool TestFileExists(const std::string& relative_path) const;
    bool TestDirectoryExists(const std::string& relative_path) const;
    size_t GetTestFileSize(const std::string& relative_path) const;
    std::vector<uint8_t> ReadTestFile(const std::string& relative_path) const;
    
    // Test data generation
    std::vector<uint8_t> GenerateTestData(size_t size, uint8_t pattern = 0);
    std::string GenerateUniqueFilename(const std::string& prefix = "test", 
                                       const std::string& extension = ".bin");
    
    // Bulk operations
    void CreateTestFileStructure();
    void CleanupTestFiles();
    
protected:
    std::filesystem::path test_root_dir_;
};

} // namespace ps4::test
