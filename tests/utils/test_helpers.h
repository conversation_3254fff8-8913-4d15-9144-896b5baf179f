/**
 * @file test_helpers.h
 * @brief Helper functions and utilities for PS4 filesystem tests
 */

#pragma once

#include <filesystem>
#include <vector>
#include <string>
#include <chrono>
#include <random>
#include <thread>
#include <algorithm>
#include <iostream>

namespace ps4::test {

// Type aliases for convenience
using TimePoint = std::chrono::high_resolution_clock::time_point;
using Duration = std::chrono::high_resolution_clock::duration;

// File system test helpers
bool CreateTestFileWithContent(const std::filesystem::path& path, const std::string& content);
bool CreateTestFileWithBinaryData(const std::filesystem::path& path, const std::vector<uint8_t>& data);
bool ReadFileContent(const std::filesystem::path& path, std::string& content);
bool ReadFileBinaryData(const std::filesystem::path& path, std::vector<uint8_t>& data);
bool CompareFiles(const std::filesystem::path& path1, const std::filesystem::path& path2);

// Data generation helpers
std::vector<uint8_t> GenerateSequentialData(size_t size, uint8_t start_value = 0);
std::vector<uint8_t> GenerateRandomData(size_t size, uint32_t seed = 0);
std::vector<uint8_t> GeneratePatternData(size_t size, const std::vector<uint8_t>& pattern);
std::string GenerateRandomString(size_t length, const std::string& charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789");

// Path manipulation helpers
std::string NormalizePath(const std::string& path);
std::string JoinPaths(const std::string& base, const std::string& relative);
std::vector<std::string> SplitPath(const std::string& path);

// Timing and performance helpers
TimePoint GetCurrentTime();
Duration GetElapsedTime(const TimePoint& start);
double GetElapsedMilliseconds(const TimePoint& start);
double GetElapsedMicroseconds(const TimePoint& start);

// Test validation helpers
bool ValidateFileDescriptor(int fd);
bool ValidateFileSize(const std::filesystem::path& path, size_t expected_size);
bool ValidateDirectoryExists(const std::filesystem::path& path);
bool ValidateFileExists(const std::filesystem::path& path);

// Error simulation helpers
class ErrorSimulator {
public:
    ErrorSimulator();
    
    void SetErrorRate(double rate); // 0.0 = no errors, 1.0 = always error
    bool ShouldSimulateError();
    void SimulateDelay(std::chrono::milliseconds min_delay, std::chrono::milliseconds max_delay);
    
private:
    double error_rate_;
    std::mt19937 gen_;
};

// Memory helpers
std::vector<uint8_t> AllocateAlignedBuffer(size_t size, size_t alignment);
bool IsAligned(const void* ptr, size_t alignment);

// String formatting helpers
std::string FormatBytes(uint64_t bytes);
std::string FormatDuration(const Duration& duration);
std::string FormatHex(const std::vector<uint8_t>& data, size_t max_bytes = 16);

// Test result helpers
class TestResult {
public:
    TestResult(bool success, const std::string& message = "");
    
    bool IsSuccess() const;
    const std::string& GetMessage() const;
    
    static TestResult Success(const std::string& message = "");
    static TestResult Failure(const std::string& message);
    
private:
    bool success_;
    std::string message_;
};

// Template helpers for common test patterns
template<typename T>
bool IsInRange(const T& value, const T& min_val, const T& max_val) {
    return value >= min_val && value <= max_val;
}

template<typename Container>
bool ContainsValue(const Container& container, const typename Container::value_type& value) {
    return std::find(container.begin(), container.end(), value) != container.end();
}

template<typename Func>
auto MeasureExecutionTime(Func&& func) -> std::pair<decltype(func()), Duration> {
    auto start = GetCurrentTime();
    auto result = func();
    auto duration = GetElapsedTime(start);
    return std::make_pair(result, duration);
}

template<typename Func>
Duration MeasureExecutionTimeOnly(Func&& func) {
    auto start = GetCurrentTime();
    func();
    return GetElapsedTime(start);
}

// Retry helper for flaky operations
template<typename Func>
bool RetryOperation(Func&& func, int max_attempts = 3, std::chrono::milliseconds delay = std::chrono::milliseconds(100)) {
    for (int attempt = 0; attempt < max_attempts; ++attempt) {
        if (func()) {
            return true;
        }
        
        if (attempt < max_attempts - 1) {
            std::this_thread::sleep_for(delay);
        }
    }
    return false;
}

// Scoped timer for automatic timing
class ScopedTimer {
public:
    explicit ScopedTimer(const std::string& name) : name_(name), start_(GetCurrentTime()) {}
    
    ~ScopedTimer() {
        auto duration = GetElapsedTime(start_);
        std::cout << name_ << " took " << FormatDuration(duration) << std::endl;
    }
    
private:
    std::string name_;
    TimePoint start_;
};

// Macro for easy scoped timing
#define SCOPED_TIMER(name) ps4::test::ScopedTimer timer_##__LINE__(name)

// Test data constants
namespace TestConstants {
    constexpr size_t SMALL_FILE_SIZE = 1024;           // 1KB
    constexpr size_t MEDIUM_FILE_SIZE = 64 * 1024;     // 64KB
    constexpr size_t LARGE_FILE_SIZE = 1024 * 1024;    // 1MB
    constexpr size_t HUGE_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    
    constexpr uint64_t DEFAULT_CACHE_SIZE = 64 * 1024 * 1024; // 64MB
    constexpr uint64_t SMALL_CACHE_SIZE = 1024 * 1024;        // 1MB
    constexpr uint64_t LARGE_CACHE_SIZE = 256 * 1024 * 1024;  // 256MB
    
    constexpr int DEFAULT_FILE_MODE = 0644;
    constexpr int DEFAULT_DIR_MODE = 0755;
    
    const std::string TEST_FILE_CONTENT = "This is test file content for PS4 filesystem testing.";
    const std::vector<uint8_t> TEST_BINARY_PATTERN = {0xDE, 0xAD, 0xBE, 0xEF};
}

// Common test data generators
namespace TestData {
    inline std::vector<uint8_t> SmallFile() {
        return GenerateSequentialData(TestConstants::SMALL_FILE_SIZE);
    }
    
    inline std::vector<uint8_t> MediumFile() {
        return GenerateRandomData(TestConstants::MEDIUM_FILE_SIZE);
    }
    
    inline std::vector<uint8_t> LargeFile() {
        return GeneratePatternData(TestConstants::LARGE_FILE_SIZE, TestConstants::TEST_BINARY_PATTERN);
    }
    
    inline std::vector<uint8_t> HugeFile() {
        return GenerateSequentialData(TestConstants::HUGE_FILE_SIZE, 0xAB);
    }
    
    inline std::string TextFile() {
        return TestConstants::TEST_FILE_CONTENT;
    }
}

// Performance test helpers
class PerformanceMeasurement {
public:
    void StartMeasurement(const std::string& operation_name);
    void EndMeasurement();
    void RecordOperation(size_t bytes_processed = 0);
    
    double GetAverageLatency() const;
    double GetThroughput() const; // bytes per second
    size_t GetOperationCount() const;
    
    void PrintResults() const;
    
private:
    std::string operation_name_;
    TimePoint start_time_;
    std::vector<Duration> operation_times_;
    size_t total_bytes_processed_ = 0;
};

// Thread safety test helpers
template<typename Func>
void RunConcurrentTest(Func&& func, int num_threads, int operations_per_thread) {
    std::vector<std::thread> threads;
    std::atomic<bool> error_occurred{false};
    
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([&func, t, operations_per_thread, &error_occurred]() {
            try {
                for (int i = 0; i < operations_per_thread; ++i) {
                    func(t, i);
                }
            } catch (const std::exception& e) {
                error_occurred = true;
            }
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    
    if (error_occurred) {
        throw std::runtime_error("Error occurred during concurrent test execution");
    }
}

} // namespace ps4::test
