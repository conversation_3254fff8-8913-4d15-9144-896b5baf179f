/**
 * @file mock_emulator.h
 * @brief Mock PS4 emulator for testing filesystem functionality
 */

#pragma once

#include <gmock/gmock.h>
#include "ps4/ps4_emulator.h"
#include "memory/memory_diagnostics.h"

namespace ps4::test {

/**
 * @brief Mock PS4 emulator for testing
 */
class MockPS4Emulator : public PS4Emulator {
public:
    MockPS4Emulator() = default;
    virtual ~MockPS4Emulator() = default;
    
    // Mock methods for PS4Emulator interface
    MOCK_METHOD(bool, Initialize, (), (override));
    MOCK_METHOD(void, Shutdown, (), (override));
    MOCK_METHOD(bool, LoadGame, (const std::string& path), (override));
    MOCK_METHOD(void, Run, (), (override));
    MOCK_METHOD(void, Pause, (), (override));
    MOCK_METHOD(void, Resume, (), (override));
    MOCK_METHOD(void, Stop, (), (override));
    MOCK_METHOD(bool, IsRunning, (), (const, override));
    MOCK_METHOD(bool, IsPaused, (), (const, override));
    
    // Mock methods for memory management
    MOCK_METHOD(uint64_t, AllocateMemory, (uint64_t size, uint64_t alignment), (override));
    MOCK_METHOD(bool, FreeMemory, (uint64_t address), (override));
    MOCK_METHOD(bool, ProtectMemory, (uint64_t address, uint64_t size, int protection), (override));
    MOCK_METHOD(void*, GetMemoryPointer, (uint64_t address), (override));
    
    // Mock methods for process management
    MOCK_METHOD(uint64_t, GetProcessId, (), (const, override));
    MOCK_METHOD(uint64_t, CreateThread, (uint64_t entry_point, uint64_t stack_size), (override));
    MOCK_METHOD(bool, TerminateThread, (uint64_t thread_id), (override));
    
    // Mock methods for system calls
    MOCK_METHOD(int64_t, HandleSyscall, (int syscall_number, const std::vector<uint64_t>& args), (override));
    
    // Mock methods for diagnostics
    MOCK_METHOD(MemoryDiagnostics*, GetMemoryDiagnostics, (), (override));
    MOCK_METHOD(void, UpdateDiagnostics, (), (override));
    
    // Mock methods for state management
    MOCK_METHOD(void, SaveState, (std::ostream& out), (const, override));
    MOCK_METHOD(void, LoadState, (std::istream& in), (override));
    MOCK_METHOD(std::string, DumpState, (), (const, override));
    
    // Helper methods for test setup
    void SetupDefaultBehavior() {
        // Set up default return values for common calls
        ON_CALL(*this, Initialize())
            .WillByDefault(::testing::Return(true));
            
        ON_CALL(*this, IsRunning())
            .WillByDefault(::testing::Return(false));
            
        ON_CALL(*this, IsPaused())
            .WillByDefault(::testing::Return(false));
            
        ON_CALL(*this, GetProcessId())
            .WillByDefault(::testing::Return(1234));
            
        ON_CALL(*this, AllocateMemory(::testing::_, ::testing::_))
            .WillByDefault(::testing::Return(0x10000000));
            
        ON_CALL(*this, FreeMemory(::testing::_))
            .WillByDefault(::testing::Return(true));
            
        ON_CALL(*this, ProtectMemory(::testing::_, ::testing::_, ::testing::_))
            .WillByDefault(::testing::Return(true));
            
        ON_CALL(*this, GetMemoryPointer(::testing::_))
            .WillByDefault(::testing::Return(nullptr));
            
        ON_CALL(*this, GetMemoryDiagnostics())
            .WillByDefault(::testing::Return(&mock_memory_diagnostics_));
    }
    
    // Mock memory diagnostics
    MockMemoryDiagnostics* GetMockMemoryDiagnostics() {
        return &mock_memory_diagnostics_;
    }
    
private:
    MockMemoryDiagnostics mock_memory_diagnostics_;
};

/**
 * @brief Mock memory diagnostics for testing
 */
class MockMemoryDiagnostics : public MemoryDiagnostics {
public:
    MockMemoryDiagnostics() = default;
    virtual ~MockMemoryDiagnostics() = default;
    
    MOCK_METHOD(void, UpdateMetrics, (), (override));
    MOCK_METHOD(uint64_t, GetTotalAllocated, (), (const, override));
    MOCK_METHOD(uint64_t, GetTotalFree, (), (const, override));
    MOCK_METHOD(uint64_t, GetLargestFreeBlock, (), (const, override));
    MOCK_METHOD(uint64_t, GetAllocationCount, (), (const, override));
    MOCK_METHOD(uint64_t, GetDeallocationCount, (), (const, override));
    MOCK_METHOD(double, GetFragmentationRatio, (), (const, override));
    MOCK_METHOD(std::string, DumpMemoryMap, (), (const, override));
    
    void SetupDefaultBehavior() {
        ON_CALL(*this, GetTotalAllocated())
            .WillByDefault(::testing::Return(1024 * 1024 * 1024)); // 1GB
            
        ON_CALL(*this, GetTotalFree())
            .WillByDefault(::testing::Return(1024 * 1024 * 1024)); // 1GB
            
        ON_CALL(*this, GetLargestFreeBlock())
            .WillByDefault(::testing::Return(512 * 1024 * 1024)); // 512MB
            
        ON_CALL(*this, GetAllocationCount())
            .WillByDefault(::testing::Return(100));
            
        ON_CALL(*this, GetDeallocationCount())
            .WillByDefault(::testing::Return(50));
            
        ON_CALL(*this, GetFragmentationRatio())
            .WillByDefault(::testing::Return(0.1)); // 10% fragmentation
            
        ON_CALL(*this, DumpMemoryMap())
            .WillByDefault(::testing::Return("Mock memory map"));
    }
};

/**
 * @brief Factory for creating mock emulators with common configurations
 */
class MockEmulatorFactory {
public:
    static std::unique_ptr<MockPS4Emulator> CreateBasicMock() {
        auto mock = std::make_unique<MockPS4Emulator>();
        mock->SetupDefaultBehavior();
        mock->GetMockMemoryDiagnostics()->SetupDefaultBehavior();
        return mock;
    }
    
    static std::unique_ptr<MockPS4Emulator> CreateMemoryConstrainedMock() {
        auto mock = CreateBasicMock();
        
        // Override memory allocation to simulate low memory
        ON_CALL(*mock, AllocateMemory(::testing::_, ::testing::_))
            .WillByDefault(::testing::Return(0)); // Allocation fails
            
        return mock;
    }
    
    static std::unique_ptr<MockPS4Emulator> CreateFailingMock() {
        auto mock = std::make_unique<MockPS4Emulator>();
        
        // Set up failing behavior
        ON_CALL(*mock, Initialize())
            .WillByDefault(::testing::Return(false));
            
        ON_CALL(*mock, AllocateMemory(::testing::_, ::testing::_))
            .WillByDefault(::testing::Return(0));
            
        ON_CALL(*mock, FreeMemory(::testing::_))
            .WillByDefault(::testing::Return(false));
            
        return mock;
    }
};

} // namespace ps4::test
