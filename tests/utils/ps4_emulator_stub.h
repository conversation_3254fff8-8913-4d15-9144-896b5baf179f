/**
 * @file ps4_emulator_stub.h
 * @brief Minimal PS4Emulator stub for testing
 */

#pragma once

#include <cstdlib>
#include <cstddef>
#include <cstdint>

namespace ps4 {

// Forward declarations for stubs
class OrbisOS;

/**
 * @brief Minimal OrbisOS stub for testing
 */
class OrbisOS {
public:
    OrbisOS() = default;
    virtual ~OrbisOS() = default;

    // Memory management stubs
    uint64_t AllocateVirtualMemory(uint64_t size, uint64_t alignment, bool shared) {
        return reinterpret_cast<uint64_t>(malloc(static_cast<size_t>(size)));
    }

    void FreeVirtualMemory(uint64_t addr) {
        free(reinterpret_cast<void*>(addr));
    }

    bool ProtectMemory(uint64_t addr, uint64_t size, int protection) {
        return true;
    }

    // Process management stubs
    int SceKernelGetProcessId() {
        return 1;
    }
};

/**
 * @brief Minimal MemoryDiagnostics stub for testing
 */
class MemoryDiagnostics {
public:
    static MemoryDiagnostics& GetInstance() {
        static MemoryDiagnostics instance;
        return instance;
    }

    void UpdateMetrics() {}
};

/**
 * @brief Minimal PS4Emulator stub for testing
 */
class PS4Emulator {
public:
    PS4Emulator() = default;
    virtual ~PS4Emulator() = default;

    // Static instance for testing
    static PS4Emulator& GetInstance() {
        static PS4Emulator instance;
        return instance;
    }

    // Minimal interface for testing
    bool Initialize() { return true; }
    void Shutdown() {}
    bool IsInitialized() const { return true; }

    // OrbisOS interface
    OrbisOS& GetOrbisOS() {
        static OrbisOS orbis_os;
        return orbis_os;
    }
};

} // namespace ps4
