/**
 * @file test_handle_table.cpp
 * @brief Tests for HandleTable class
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "ps4/ps4_filesystem.h"
#include "utils/test_fixtures.h"
#include <thread>
#include <vector>
#include <set>

using namespace ps4;
using namespace ps4::test;

class HandleTableTest : public ::testing::Test {
protected:
    void SetUp() override {
        handle_table_ = std::make_unique<HandleTable>();
        
        // Create standard handles (stdin, stdout, stderr)
        handle_table_->CreateStdHandles();
    }
    
    void TearDown() override {
        handle_table_.reset();
    }
    
protected:
    std::unique_ptr<HandleTable> handle_table_;
};

// Basic handle creation and deletion tests
TEST_F(HandleTableTest, CreateHandleReturnsValidFd) {
    int fd = handle_table_->CreateHandle();
    EXPECT_GE(fd, 3); // Should be >= 3 since 0,1,2 are reserved for std handles
    
    // Verify handle exists
    HandleTable::File* file = handle_table_->GetFile(fd);
    EXPECT_NE(file, nullptr);
    EXPECT_EQ(file->fd, fd);
    EXPECT_FALSE(file->is_opened);
}

TEST_F(HandleTableTest, CreateMultipleHandles) {
    std::vector<int> fds;
    
    // Create multiple handles
    for (int i = 0; i < 10; ++i) {
        int fd = handle_table_->CreateHandle();
        EXPECT_GE(fd, 3);
        fds.push_back(fd);
    }
    
    // All handles should be unique
    std::set<int> unique_fds(fds.begin(), fds.end());
    EXPECT_EQ(unique_fds.size(), fds.size());
    
    // All handles should be accessible
    for (int fd : fds) {
        HandleTable::File* file = handle_table_->GetFile(fd);
        EXPECT_NE(file, nullptr);
        EXPECT_EQ(file->fd, fd);
    }
}

TEST_F(HandleTableTest, DeleteHandleRemovesFromTable) {
    int fd = handle_table_->CreateHandle();
    EXPECT_NE(handle_table_->GetFile(fd), nullptr);
    
    // Delete handle
    handle_table_->DeleteHandle(fd);
    
    // Handle should no longer exist
    EXPECT_EQ(handle_table_->GetFile(fd), nullptr);
}

TEST_F(HandleTableTest, DeleteNonExistentHandle) {
    // Deleting non-existent handle should not crash
    handle_table_->DeleteHandle(999);
    handle_table_->DeleteHandle(-1);
    
    // Should still be able to create new handles
    int fd = handle_table_->CreateHandle();
    EXPECT_GE(fd, 3);
}

// Standard handles tests
TEST_F(HandleTableTest, StandardHandlesExist) {
    // Standard handles should exist after CreateStdHandles()
    HandleTable::File* stdin_file = handle_table_->GetFile(0);
    HandleTable::File* stdout_file = handle_table_->GetFile(1);
    HandleTable::File* stderr_file = handle_table_->GetFile(2);
    
    EXPECT_NE(stdin_file, nullptr);
    EXPECT_NE(stdout_file, nullptr);
    EXPECT_NE(stderr_file, nullptr);
    
    EXPECT_EQ(stdin_file->fd, 0);
    EXPECT_EQ(stdout_file->fd, 1);
    EXPECT_EQ(stderr_file->fd, 2);
}

TEST_F(HandleTableTest, StandardHandlesHaveCorrectProperties) {
    HandleTable::File* stdin_file = handle_table_->GetFile(0);
    HandleTable::File* stdout_file = handle_table_->GetFile(1);
    HandleTable::File* stderr_file = handle_table_->GetFile(2);
    
    // Standard handles should be marked as opened
    EXPECT_TRUE(stdin_file->is_opened);
    EXPECT_TRUE(stdout_file->is_opened);
    EXPECT_TRUE(stderr_file->is_opened);
    
    // Standard handles should have appropriate names
    EXPECT_FALSE(stdin_file->guest_name.empty());
    EXPECT_FALSE(stdout_file->guest_name.empty());
    EXPECT_FALSE(stderr_file->guest_name.empty());
}

// File handle manipulation tests
TEST_F(HandleTableTest, SetFileProperties) {
    int fd = handle_table_->CreateHandle();
    HandleTable::File* file = handle_table_->GetFile(fd);
    ASSERT_NE(file, nullptr);
    
    // Set file properties
    file->is_opened = true;
    file->host_name = "/host/path/file.txt";
    file->guest_name = "/guest/path/file.txt";
    file->host_fd = 42;
    file->type = PS4FileType::Regular;
    
    // Verify properties were set
    EXPECT_TRUE(file->is_opened);
    EXPECT_EQ(file->host_name, "/host/path/file.txt");
    EXPECT_EQ(file->guest_name, "/guest/path/file.txt");
    EXPECT_EQ(file->host_fd, 42);
    EXPECT_EQ(file->type, PS4FileType::Regular);
}

TEST_F(HandleTableTest, FileTypeHandling) {
    int fd = handle_table_->CreateHandle();
    HandleTable::File* file = handle_table_->GetFile(fd);
    ASSERT_NE(file, nullptr);
    
    // Test different file types
    file->type = PS4FileType::Device;
    EXPECT_EQ(file->type, PS4FileType::Device);
    
    file->type = PS4FileType::Directory;
    EXPECT_EQ(file->type, PS4FileType::Directory);
    
    file->type = PS4FileType::Socket;
    EXPECT_EQ(file->type, PS4FileType::Socket);
}

// Handle reuse tests
TEST_F(HandleTableTest, HandleReuse) {
    // Create and delete a handle
    int fd1 = handle_table_->CreateHandle();
    handle_table_->DeleteHandle(fd1);
    
    // Create another handle - it might reuse the fd
    int fd2 = handle_table_->CreateHandle();
    EXPECT_GE(fd2, 3);
    
    // Both operations should work correctly
    HandleTable::File* file = handle_table_->GetFile(fd2);
    EXPECT_NE(file, nullptr);
    EXPECT_EQ(file->fd, fd2);
}

TEST_F(HandleTableTest, HandleSequentialAllocation) {
    std::vector<int> fds;
    
    // Create several handles
    for (int i = 0; i < 5; ++i) {
        fds.push_back(handle_table_->CreateHandle());
    }
    
    // Handles should generally be allocated sequentially (though not guaranteed)
    for (size_t i = 1; i < fds.size(); ++i) {
        EXPECT_GT(fds[i], fds[i-1]);
    }
}

// Error handling tests
TEST_F(HandleTableTest, GetFileWithInvalidFd) {
    // Invalid file descriptors should return nullptr
    EXPECT_EQ(handle_table_->GetFile(-1), nullptr);
    EXPECT_EQ(handle_table_->GetFile(999), nullptr);
    EXPECT_EQ(handle_table_->GetFile(INT_MAX), nullptr);
}

TEST_F(HandleTableTest, GetFileAfterDeletion) {
    int fd = handle_table_->CreateHandle();
    
    // Verify handle exists
    EXPECT_NE(handle_table_->GetFile(fd), nullptr);
    
    // Delete handle
    handle_table_->DeleteHandle(fd);
    
    // Should return nullptr after deletion
    EXPECT_EQ(handle_table_->GetFile(fd), nullptr);
}

// Thread safety tests
TEST_F(HandleTableTest, ConcurrentHandleCreation) {
    const int num_threads = 4;
    const int handles_per_thread = 100;
    std::vector<std::thread> threads;
    std::vector<std::vector<int>> thread_fds(num_threads);
    
    // Launch threads that create handles concurrently
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, t, handles_per_thread, &thread_fds]() {
            for (int i = 0; i < handles_per_thread; ++i) {
                int fd = handle_table_->CreateHandle();
                thread_fds[t].push_back(fd);
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Collect all file descriptors
    std::set<int> all_fds;
    for (const auto& fds : thread_fds) {
        for (int fd : fds) {
            all_fds.insert(fd);
        }
    }
    
    // All file descriptors should be unique
    size_t total_expected = num_threads * handles_per_thread;
    EXPECT_EQ(all_fds.size(), total_expected);
    
    // All handles should be valid
    for (int fd : all_fds) {
        EXPECT_NE(handle_table_->GetFile(fd), nullptr);
    }
}

TEST_F(HandleTableTest, ConcurrentHandleDeletion) {
    const int num_handles = 100;
    std::vector<int> fds;
    
    // Create handles
    for (int i = 0; i < num_handles; ++i) {
        fds.push_back(handle_table_->CreateHandle());
    }
    
    // Delete handles concurrently
    const int num_threads = 4;
    std::vector<std::thread> threads;
    
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, t, num_threads, &fds]() {
            for (size_t i = t; i < fds.size(); i += num_threads) {
                handle_table_->DeleteHandle(fds[i]);
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // All handles should be deleted
    for (int fd : fds) {
        EXPECT_EQ(handle_table_->GetFile(fd), nullptr);
    }
}

TEST_F(HandleTableTest, ConcurrentCreateAndDelete) {
    const int num_operations = 1000;
    const int num_threads = 4;
    std::vector<std::thread> threads;
    std::atomic<int> operations_completed{0};
    
    // Launch threads that create and delete handles concurrently
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, num_operations, &operations_completed]() {
            for (int i = 0; i < num_operations; ++i) {
                // Create handle
                int fd = handle_table_->CreateHandle();
                
                // Verify it exists
                HandleTable::File* file = handle_table_->GetFile(fd);
                if (file != nullptr) {
                    // Set some properties
                    file->is_opened = true;
                    file->guest_name = "/test/file_" + std::to_string(fd);
                    
                    // Delete handle
                    handle_table_->DeleteHandle(fd);
                }
                
                operations_completed++;
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    EXPECT_EQ(operations_completed.load(), num_threads * num_operations);
    
    // Handle table should still be functional
    int fd = handle_table_->CreateHandle();
    EXPECT_GE(fd, 3);
    EXPECT_NE(handle_table_->GetFile(fd), nullptr);
}

// Edge cases and stress tests
TEST_F(HandleTableTest, ManyHandles) {
    const int num_handles = 10000;
    std::vector<int> fds;
    
    // Create many handles
    for (int i = 0; i < num_handles; ++i) {
        int fd = handle_table_->CreateHandle();
        EXPECT_GE(fd, 3);
        fds.push_back(fd);
    }
    
    // Verify all handles are accessible
    for (int fd : fds) {
        HandleTable::File* file = handle_table_->GetFile(fd);
        EXPECT_NE(file, nullptr);
        EXPECT_EQ(file->fd, fd);
    }
    
    // Delete all handles
    for (int fd : fds) {
        handle_table_->DeleteHandle(fd);
        EXPECT_EQ(handle_table_->GetFile(fd), nullptr);
    }
}
