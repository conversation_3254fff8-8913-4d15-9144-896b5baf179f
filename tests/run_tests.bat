@echo off
REM PS4 Filesystem Test Runner Script for Windows

echo ========================================
echo PS4 Filesystem Test Suite
echo ========================================

REM Check if test executable exists
if not exist "Debug\ps4_filesystem_tests.exe" (
    echo Error: Test executable not found. Please build the tests first.
    echo Run: cmake --build . --target ps4_filesystem_tests
    pause
    exit /b 1
)

echo Running PS4 Filesystem Tests...
echo.

REM Run all tests
Debug\ps4_filesystem_tests.exe --gtest_output=xml:test_results.xml

set TEST_RESULT=%ERRORLEVEL%

echo.
echo ========================================

if %TEST_RESULT% equ 0 (
    echo All tests PASSED!
    echo Test results saved to: test_results.xml
) else (
    echo Some tests FAILED!
    echo Check the output above for details.
    echo Test results saved to: test_results.xml
)

echo ========================================

REM Stay in tests directory (we're already here)

pause
exit /b %TEST_RESULT%
