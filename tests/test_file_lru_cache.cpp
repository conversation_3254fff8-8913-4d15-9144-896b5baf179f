/**
 * @file test_file_lru_cache.cpp
 * @brief Comprehensive tests for FileLRUCache class
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "ps4/ps4_filesystem.h"
#include "utils/test_fixtures.h"
#include <thread>
#include <chrono>

using namespace ps4;
using namespace ps4::test;

class FileLRUCacheTest : public CacheTestFixture {
protected:
    void SetUp() override {
        CacheTestFixture::SetUp();
    }
};

// Basic functionality tests
TEST_F(FileLRUCacheTest, ConstructorSetsCorrectMaxSize) {
    EXPECT_EQ(GetCache()->GetMaxSize(), cache_size_);
    EXPECT_EQ(GetCache()->GetCurrentSize(), 0);
    EXPECT_EQ(GetCache()->GetEntryCount(), 0);
}

TEST_F(FileLRUCacheTest, PutAndGetBasicOperation) {
    std::string filename = "/test/file.bin";
    std::vector<uint8_t> data = GenerateTestData(1024);
    std::vector<uint8_t> original_data = data;
    
    // Put data into cache
    GetCache()->Put(filename, std::move(data));
    
    // Verify cache state
    EXPECT_EQ(GetCache()->GetEntryCount(), 1);
    EXPECT_GT(GetCache()->GetCurrentSize(), 0);
    EXPECT_TRUE(GetCache()->IsFileCached(filename));
    
    // Get data from cache
    std::vector<uint8_t> retrieved_data;
    EXPECT_TRUE(GetCache()->Get(filename, retrieved_data));
    EXPECT_EQ(retrieved_data, original_data);
    
    // Verify hit count increased
    EXPECT_EQ(GetCache()->GetHitCount(), 1);
    EXPECT_EQ(GetCache()->GetMissCount(), 0);
}

TEST_F(FileLRUCacheTest, GetNonExistentFileReturnsFalse) {
    std::vector<uint8_t> data;
    EXPECT_FALSE(GetCache()->Get("/nonexistent/file.bin", data));
    EXPECT_EQ(GetCache()->GetMissCount(), 1);
    EXPECT_EQ(GetCache()->GetHitCount(), 0);
}

TEST_F(FileLRUCacheTest, RemoveFileFromCache) {
    std::string filename = "/test/file.bin";
    std::vector<uint8_t> data = GenerateTestData(1024);
    
    // Add file to cache
    GetCache()->Put(filename, std::move(data));
    EXPECT_TRUE(GetCache()->IsFileCached(filename));
    
    // Remove file from cache
    GetCache()->Remove(filename);
    EXPECT_FALSE(GetCache()->IsFileCached(filename));
    EXPECT_EQ(GetCache()->GetEntryCount(), 0);
    
    // Verify file is no longer accessible
    std::vector<uint8_t> retrieved_data;
    EXPECT_FALSE(GetCache()->Get(filename, retrieved_data));
}

TEST_F(FileLRUCacheTest, ClearRemovesAllEntries) {
    // Add multiple files to cache
    for (int i = 0; i < 5; ++i) {
        std::string filename = "/test/file_" + std::to_string(i) + ".bin";
        std::vector<uint8_t> data = GenerateTestData(1024, static_cast<uint8_t>(i));
        GetCache()->Put(filename, std::move(data));
    }
    
    EXPECT_EQ(GetCache()->GetEntryCount(), 5);
    EXPECT_GT(GetCache()->GetCurrentSize(), 0);
    
    // Clear cache
    GetCache()->Clear();
    
    EXPECT_EQ(GetCache()->GetEntryCount(), 0);
    EXPECT_EQ(GetCache()->GetCurrentSize(), 0);
}

// LRU eviction tests
TEST_F(FileLRUCacheTest, LRUEvictionWhenCacheFull) {
    size_t file_size = cache_size_ / 4; // Each file is 1/4 of cache size
    
    // Add 4 files to fill cache
    std::vector<std::string> filenames;
    for (int i = 0; i < 4; ++i) {
        std::string filename = "/test/file_" + std::to_string(i) + ".bin";
        filenames.push_back(filename);
        std::vector<uint8_t> data = GenerateTestData(file_size, static_cast<uint8_t>(i));
        GetCache()->Put(filename, std::move(data));
    }
    
    EXPECT_EQ(GetCache()->GetEntryCount(), 4);
    
    // Add one more file that should trigger eviction
    std::string new_filename = "/test/new_file.bin";
    std::vector<uint8_t> new_data = GenerateTestData(file_size, 0xFF);
    GetCache()->Put(new_filename, std::move(new_data));
    
    // First file should be evicted (LRU)
    EXPECT_FALSE(GetCache()->IsFileCached(filenames[0]));
    EXPECT_TRUE(GetCache()->IsFileCached(new_filename));
    EXPECT_GT(GetCache()->GetEvictionCount(), 0);
}

TEST_F(FileLRUCacheTest, AccessUpdatesLRUOrder) {
    size_t file_size = cache_size_ / 4;
    
    // Add 4 files
    std::vector<std::string> filenames;
    for (int i = 0; i < 4; ++i) {
        std::string filename = "/test/file_" + std::to_string(i) + ".bin";
        filenames.push_back(filename);
        std::vector<uint8_t> data = GenerateTestData(file_size, static_cast<uint8_t>(i));
        GetCache()->Put(filename, std::move(data));
    }
    
    // Access the first file to make it most recently used
    std::vector<uint8_t> retrieved_data;
    EXPECT_TRUE(GetCache()->Get(filenames[0], retrieved_data));
    
    // Add a new file that should trigger eviction
    std::string new_filename = "/test/new_file.bin";
    std::vector<uint8_t> new_data = GenerateTestData(file_size, 0xFF);
    GetCache()->Put(new_filename, std::move(new_data));
    
    // First file should still be in cache (was accessed recently)
    // Second file should be evicted instead
    EXPECT_TRUE(GetCache()->IsFileCached(filenames[0]));
    EXPECT_FALSE(GetCache()->IsFileCached(filenames[1]));
}

// Cache size management tests
TEST_F(FileLRUCacheTest, SetMaxSizeTriggersEviction) {
    // Fill cache with data
    FillCache();
    size_t initial_count = GetCache()->GetEntryCount();
    EXPECT_GT(initial_count, 0);
    
    // Reduce cache size significantly
    uint64_t new_size = cache_size_ / 4;
    GetCache()->SetMaxSize(new_size);
    
    EXPECT_EQ(GetCache()->GetMaxSize(), new_size);
    EXPECT_LT(GetCache()->GetEntryCount(), initial_count);
    EXPECT_LE(GetCache()->GetCurrentSize(), new_size);
}

TEST_F(FileLRUCacheTest, InvalidateFileRemovesFromCache) {
    std::string filename = "/test/file.bin";
    std::vector<uint8_t> data = GenerateTestData(1024);
    
    GetCache()->Put(filename, std::move(data));
    EXPECT_TRUE(GetCache()->IsFileCached(filename));
    
    GetCache()->InvalidateFile(filename);
    EXPECT_FALSE(GetCache()->IsFileCached(filename));
}

TEST_F(FileLRUCacheTest, InvalidatePatternRemovesMatchingFiles) {
    // Add files with different patterns
    std::vector<std::string> test_files = {
        "/test/file1.bin",
        "/test/file2.bin", 
        "/other/file3.bin",
        "/test/subdir/file4.bin"
    };
    
    for (const auto& filename : test_files) {
        std::vector<uint8_t> data = GenerateTestData(1024);
        GetCache()->Put(filename, std::move(data));
    }
    
    EXPECT_EQ(GetCache()->GetEntryCount(), 4);
    
    // Invalidate files matching "/test/*" pattern
    GetCache()->InvalidatePattern("/test/");
    
    // Only /other/file3.bin should remain
    EXPECT_FALSE(GetCache()->IsFileCached("/test/file1.bin"));
    EXPECT_FALSE(GetCache()->IsFileCached("/test/file2.bin"));
    EXPECT_TRUE(GetCache()->IsFileCached("/other/file3.bin"));
    EXPECT_FALSE(GetCache()->IsFileCached("/test/subdir/file4.bin"));
}

// Statistics tests
TEST_F(FileLRUCacheTest, HitRatioCalculation) {
    std::string filename = "/test/file.bin";
    std::vector<uint8_t> data = GenerateTestData(1024);
    GetCache()->Put(filename, std::move(data));
    
    // Perform some hits and misses
    std::vector<uint8_t> retrieved_data;
    GetCache()->Get(filename, retrieved_data); // Hit
    GetCache()->Get(filename, retrieved_data); // Hit
    GetCache()->Get("/nonexistent.bin", retrieved_data); // Miss
    GetCache()->Get(filename, retrieved_data); // Hit
    
    EXPECT_EQ(GetCache()->GetHitCount(), 3);
    EXPECT_EQ(GetCache()->GetMissCount(), 1);
    EXPECT_DOUBLE_EQ(GetCache()->GetHitRatio(), 0.75); // 3/4 = 0.75
}

// Maintenance operations tests
TEST_F(FileLRUCacheTest, EvictOldEntries) {
    std::string filename = "/test/file.bin";
    std::vector<uint8_t> data = GenerateTestData(1024);
    GetCache()->Put(filename, std::move(data));
    
    EXPECT_TRUE(GetCache()->IsFileCached(filename));
    
    // Evict entries older than 0 seconds (should evict everything)
    GetCache()->EvictOldEntries(std::chrono::seconds(0));
    
    EXPECT_FALSE(GetCache()->IsFileCached(filename));
    EXPECT_EQ(GetCache()->GetEntryCount(), 0);
}

TEST_F(FileLRUCacheTest, CompactCacheReducesSize) {
    // Fill cache with data
    FillCache();
    uint64_t size_before = GetCache()->GetCurrentSize();
    
    // Compact cache
    GetCache()->CompactCache();
    
    // Size should be same or smaller after compaction
    EXPECT_LE(GetCache()->GetCurrentSize(), size_before);
}

// Thread safety tests
TEST_F(FileLRUCacheTest, ConcurrentAccessIsSafe) {
    const int num_threads = 4;
    const int operations_per_thread = 100;
    std::vector<std::thread> threads;
    
    // Launch threads that perform concurrent operations
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, t, operations_per_thread]() {
            for (int i = 0; i < operations_per_thread; ++i) {
                std::string filename = "/test/thread_" + std::to_string(t) + 
                                     "_file_" + std::to_string(i) + ".bin";
                std::vector<uint8_t> data = GenerateTestData(1024, static_cast<uint8_t>(t));
                
                // Put data
                GetCache()->Put(filename, std::move(data));
                
                // Try to get data
                std::vector<uint8_t> retrieved_data;
                GetCache()->Get(filename, retrieved_data);
                
                // Occasionally remove data
                if (i % 10 == 0) {
                    GetCache()->Remove(filename);
                }
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Cache should still be in a valid state
    EXPECT_GE(GetCache()->GetEntryCount(), 0);
    EXPECT_LE(GetCache()->GetCurrentSize(), GetCache()->GetMaxSize());
}

// Edge cases and error handling
TEST_F(FileLRUCacheTest, EmptyFilenameHandling) {
    std::vector<uint8_t> data = GenerateTestData(1024);
    
    // Should handle empty filename gracefully
    GetCache()->Put("", std::move(data));
    
    std::vector<uint8_t> retrieved_data;
    EXPECT_FALSE(GetCache()->Get("", retrieved_data));
}

TEST_F(FileLRUCacheTest, LargeFileHandling) {
    // Try to cache a file larger than the cache size
    std::string filename = "/test/large_file.bin";
    std::vector<uint8_t> large_data = GenerateTestData(cache_size_ * 2);
    
    GetCache()->Put(filename, std::move(large_data));
    
    // File should not be cached (too large)
    EXPECT_FALSE(GetCache()->IsFileCached(filename));
}

TEST_F(FileLRUCacheTest, ZeroSizeCacheHandling) {
    // Create cache with zero size
    FileLRUCache zero_cache(0);
    
    std::vector<uint8_t> data = GenerateTestData(1024);
    zero_cache.Put("/test/file.bin", std::move(data));
    
    // Nothing should be cached
    EXPECT_EQ(zero_cache.GetEntryCount(), 0);
    EXPECT_FALSE(zero_cache.IsFileCached("/test/file.bin"));
}
