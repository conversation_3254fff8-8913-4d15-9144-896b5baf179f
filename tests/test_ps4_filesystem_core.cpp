/**
 * @file test_ps4_filesystem_core.cpp
 * @brief Core functionality tests for PS4Filesystem class
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "ps4/ps4_filesystem.h"
#include "utils/test_fixtures.h"
#include "common/lock_ordering.h"
#include <fcntl.h>
#ifdef _WIN32
#include <io.h>
#else
#include <unistd.h>
#endif

using namespace ps4;
using namespace ps4::test;
using ::testing::_;
using ::testing::Return;
using ::testing::InSequence;

class PS4FilesystemCoreTest : public FilesystemTestFixture {
protected:
    void SetUp() override {
        FilesystemTestFixture::SetUp();
        
        // Set up mock expectations for basic operations
        EXPECT_CALL(*GetMockEmulator(), Initialize())
            .WillRepeatedly(Return(true));
        EXPECT_CALL(*GetMockEmulator(), GetProcessId())
            .WillRepeatedly(Return(1234));
    }
};

// Initialization and shutdown tests
TEST_F(PS4FilesystemCoreTest, InitializeSucceeds) {
    // Filesystem should already be initialized in SetUp
    EXPECT_TRUE(GetFilesystem() != nullptr);
    
    // Test re-initialization
    GetFilesystem()->Shutdown();
    EXPECT_TRUE(GetFilesystem()->Initialize());
}

TEST_F(PS4FilesystemCoreTest, ShutdownCleansUpResources) {
    // Create some files first
    std::string test_file = CreateTestFile("test.bin", GenerateTestData(1024));
    int fd = GetFilesystem()->OpenFile("/test.bin", O_RDONLY, 0644);
    EXPECT_GT(fd, 0);
    
    // Shutdown should clean up resources
    GetFilesystem()->Shutdown();
    
    // File descriptor should be invalid after shutdown
    char buffer[100];
    EXPECT_EQ(GetFilesystem()->ReadFile(fd, buffer, sizeof(buffer)), -1);
}

// File operations tests
TEST_F(PS4FilesystemCoreTest, OpenFileWithValidPath) {
    std::string test_file = CreateTestFile("test.bin", GenerateTestData(1024));
    
    // Mount the test directory
    std::wstring wide_path(GetTestDirectory().wstring());
    EXPECT_TRUE(GetFilesystem()->MountDirectory(wide_path));
    
    // Open file should succeed
    int fd = GetFilesystem()->OpenFile("/mnt/" + GetTestDirectory().filename().string() + "/test.bin", 
                                      O_RDONLY, 0644);
    EXPECT_GT(fd, 0);
    
    // Clean up
    EXPECT_EQ(GetFilesystem()->CloseFile(fd), 0);
}

TEST_F(PS4FilesystemCoreTest, OpenFileWithInvalidPath) {
    // Opening non-existent file should fail
    int fd = GetFilesystem()->OpenFile("/nonexistent/file.bin", O_RDONLY, 0644);
    EXPECT_EQ(fd, -1);
}

TEST_F(PS4FilesystemCoreTest, OpenFileWithInvalidFlags) {
    // Test with invalid path format
    int fd = GetFilesystem()->OpenFile("relative/path.bin", O_RDONLY, 0644);
    EXPECT_EQ(fd, -1);
    
    // Test with empty path
    fd = GetFilesystem()->OpenFile("", O_RDONLY, 0644);
    EXPECT_EQ(fd, -1);
}

TEST_F(PS4FilesystemCoreTest, ReadFileWithValidDescriptor) {
    std::vector<uint8_t> test_data = GenerateTestData(1024, 0xAB);
    std::string test_file = CreateTestFile("test.bin", test_data);
    
    // Mount and open file
    std::wstring wide_path(GetTestDirectory().wstring());
    EXPECT_TRUE(GetFilesystem()->MountDirectory(wide_path));
    
    int fd = GetFilesystem()->OpenFile("/mnt/" + GetTestDirectory().filename().string() + "/test.bin", 
                                      O_RDONLY, 0644);
    ASSERT_GT(fd, 0);
    
    // Read data
    std::vector<uint8_t> read_buffer(1024);
    ssize_t bytes_read = GetFilesystem()->ReadFile(fd, read_buffer.data(), read_buffer.size());
    
    EXPECT_EQ(bytes_read, 1024);
    EXPECT_EQ(read_buffer, test_data);
    
    // Clean up
    EXPECT_EQ(GetFilesystem()->CloseFile(fd), 0);
}

TEST_F(PS4FilesystemCoreTest, ReadFileWithInvalidDescriptor) {
    char buffer[100];
    ssize_t bytes_read = GetFilesystem()->ReadFile(-1, buffer, sizeof(buffer));
    EXPECT_EQ(bytes_read, -1);
    
    bytes_read = GetFilesystem()->ReadFile(999, buffer, sizeof(buffer));
    EXPECT_EQ(bytes_read, -1);
}

TEST_F(PS4FilesystemCoreTest, WriteFileWithValidDescriptor) {
    std::string test_file = CreateTestFile("test.bin", std::vector<uint8_t>(1024, 0));
    
    // Mount and open file for writing
    std::wstring wide_path(GetTestDirectory().wstring());
    EXPECT_TRUE(GetFilesystem()->MountDirectory(wide_path));
    
    int fd = GetFilesystem()->OpenFile("/mnt/" + GetTestDirectory().filename().string() + "/test.bin", 
                                      O_WRONLY, 0644);
    ASSERT_GT(fd, 0);
    
    // Write data
    std::vector<uint8_t> write_data = GenerateTestData(512, 0xCD);
    ssize_t bytes_written = GetFilesystem()->WriteFile(fd, write_data.data(), write_data.size());
    
    EXPECT_EQ(bytes_written, 512);
    
    // Clean up
    EXPECT_EQ(GetFilesystem()->CloseFile(fd), 0);
}

TEST_F(PS4FilesystemCoreTest, WriteFileWithInvalidDescriptor) {
    std::vector<uint8_t> data = GenerateTestData(100);
    ssize_t bytes_written = GetFilesystem()->WriteFile(-1, data.data(), data.size());
    EXPECT_EQ(bytes_written, -1);
}

TEST_F(PS4FilesystemCoreTest, SeekFileOperation) {
    std::vector<uint8_t> test_data = GenerateTestData(1024);
    std::string test_file = CreateTestFile("test.bin", test_data);
    
    // Mount and open file
    std::wstring wide_path(GetTestDirectory().wstring());
    EXPECT_TRUE(GetFilesystem()->MountDirectory(wide_path));
    
    int fd = GetFilesystem()->OpenFile("/mnt/" + GetTestDirectory().filename().string() + "/test.bin", 
                                      O_RDONLY, 0644);
    ASSERT_GT(fd, 0);
    
    // Test seeking to different positions
    EXPECT_EQ(GetFilesystem()->SeekFile(fd, 100, SEEK_SET), 100);
    EXPECT_EQ(GetFilesystem()->SeekFile(fd, 50, SEEK_CUR), 150);
    EXPECT_EQ(GetFilesystem()->SeekFile(fd, -10, SEEK_END), 1014);
    
    // Test invalid seeks
    EXPECT_EQ(GetFilesystem()->SeekFile(fd, -2000, SEEK_SET), -1);
    
    // Clean up
    EXPECT_EQ(GetFilesystem()->CloseFile(fd), 0);
}

TEST_F(PS4FilesystemCoreTest, CloseFileOperation) {
    std::string test_file = CreateTestFile("test.bin", GenerateTestData(1024));
    
    // Mount and open file
    std::wstring wide_path(GetTestDirectory().wstring());
    EXPECT_TRUE(GetFilesystem()->MountDirectory(wide_path));
    
    int fd = GetFilesystem()->OpenFile("/mnt/" + GetTestDirectory().filename().string() + "/test.bin", 
                                      O_RDONLY, 0644);
    ASSERT_GT(fd, 0);
    
    // Close file should succeed
    EXPECT_EQ(GetFilesystem()->CloseFile(fd), 0);
    
    // Using closed file descriptor should fail
    char buffer[100];
    EXPECT_EQ(GetFilesystem()->ReadFile(fd, buffer, sizeof(buffer)), -1);
    
    // Closing already closed file should fail
    EXPECT_EQ(GetFilesystem()->CloseFile(fd), -1);
}

// Directory operations tests
TEST_F(PS4FilesystemCoreTest, CreateDirectorySucceeds) {
    EXPECT_TRUE(GetFilesystem()->CreateDirectory("/test/new_directory", 0755));
    
    // Creating existing directory should still succeed
    EXPECT_TRUE(GetFilesystem()->CreateDirectory("/test/new_directory", 0755));
}

TEST_F(PS4FilesystemCoreTest, CreateDirectoryWithInvalidPath) {
    // Invalid path format
    EXPECT_FALSE(GetFilesystem()->CreateDirectory("relative/path", 0755));
    
    // Empty path
    EXPECT_FALSE(GetFilesystem()->CreateDirectory("", 0755));
}

TEST_F(PS4FilesystemCoreTest, RemoveDirectorySucceeds) {
    // Create directory first
    EXPECT_TRUE(GetFilesystem()->CreateDirectory("/test/temp_directory", 0755));
    
    // Remove directory should succeed
    EXPECT_TRUE(GetFilesystem()->RemoveDirectory("/test/temp_directory"));
    
    // Removing non-existent directory should fail
    EXPECT_FALSE(GetFilesystem()->RemoveDirectory("/test/nonexistent"));
}

TEST_F(PS4FilesystemCoreTest, MountDirectorySucceeds) {
    std::wstring wide_path(GetTestDirectory().wstring());
    EXPECT_TRUE(GetFilesystem()->MountDirectory(wide_path));
    
    // Mounting same directory again should succeed
    EXPECT_TRUE(GetFilesystem()->MountDirectory(wide_path));
}

// Memory management tests
TEST_F(PS4FilesystemCoreTest, AllocateVirtualMemory) {
    EXPECT_CALL(*GetMockEmulator(), AllocateMemory(_, _))
        .WillOnce(Return(0x10000000));
    
    uint64_t address = GetFilesystem()->AllocateVirtualMemory(4096, 4096, false);
    EXPECT_NE(address, 0);
}

TEST_F(PS4FilesystemCoreTest, FreeVirtualMemory) {
    EXPECT_CALL(*GetMockEmulator(), FreeMemory(_))
        .WillOnce(Return(true));
    
    EXPECT_TRUE(GetFilesystem()->FreeVirtualMemory(0x10000000));
}

TEST_F(PS4FilesystemCoreTest, ProtectMemory) {
    EXPECT_CALL(*GetMockEmulator(), ProtectMemory(_, _, _))
        .WillOnce(Return(true));
    
    EXPECT_TRUE(GetFilesystem()->ProtectMemory(0x10000000, 4096, 0x7));
}

// State management tests
TEST_F(PS4FilesystemCoreTest, DumpStateReturnsValidString) {
    std::string state = GetFilesystem()->DumpState();
    EXPECT_FALSE(state.empty());
    EXPECT_NE(state.find("PS4Filesystem"), std::string::npos);
}

TEST_F(PS4FilesystemCoreTest, SaveAndLoadState) {
    // Create some state
    std::string test_file = CreateTestFile("test.bin", GenerateTestData(1024));
    std::wstring wide_path(GetTestDirectory().wstring());
    GetFilesystem()->MountDirectory(wide_path);
    
    // Save state
    std::stringstream state_stream;
    GetFilesystem()->SaveState(state_stream);
    
    // Create new filesystem and load state
    auto new_filesystem = std::make_unique<PS4Filesystem>(*GetMockEmulator());
    new_filesystem->Initialize();
    
    state_stream.seekg(0);
    new_filesystem->LoadState(state_stream);
    
    // Verify state was loaded (this is a basic test - more detailed verification would be needed)
    std::string new_state = new_filesystem->DumpState();
    EXPECT_FALSE(new_state.empty());
}

// Error handling and edge cases
TEST_F(PS4FilesystemCoreTest, HandleNullPointers) {
    // Reading with null buffer should fail gracefully
    int fd = 3; // Assume valid fd
    EXPECT_EQ(GetFilesystem()->ReadFile(fd, nullptr, 100), -1);
    
    // Writing with null buffer should fail gracefully  
    EXPECT_EQ(GetFilesystem()->WriteFile(fd, nullptr, 100), -1);
}

TEST_F(PS4FilesystemCoreTest, HandleLargeOperations) {
    std::vector<uint8_t> large_data = GenerateTestData(10 * 1024 * 1024); // 10MB
    std::string test_file = CreateTestFile("large_test.bin", large_data);
    
    // Mount and open file
    std::wstring wide_path(GetTestDirectory().wstring());
    EXPECT_TRUE(GetFilesystem()->MountDirectory(wide_path));
    
    int fd = GetFilesystem()->OpenFile("/mnt/" + GetTestDirectory().filename().string() + "/large_test.bin", 
                                      O_RDONLY, 0644);
    ASSERT_GT(fd, 0);
    
    // Read large amount of data
    std::vector<uint8_t> read_buffer(large_data.size());
    ssize_t bytes_read = GetFilesystem()->ReadFile(fd, read_buffer.data(), read_buffer.size());
    
    EXPECT_EQ(bytes_read, static_cast<ssize_t>(large_data.size()));
    EXPECT_EQ(read_buffer, large_data);
    
    // Clean up
    EXPECT_EQ(GetFilesystem()->CloseFile(fd), 0);
}

// Lock ordering compliance tests
TEST_F(PS4FilesystemCoreTest, LockOrderingCompliance) {
    // This test verifies that the filesystem respects lock ordering
    // In debug builds, lock ordering violations would trigger assertions
    
    std::string test_file = CreateTestFile("test.bin", GenerateTestData(1024));
    std::wstring wide_path(GetTestDirectory().wstring());
    GetFilesystem()->MountDirectory(wide_path);
    
    // Perform operations that require locking
    int fd = GetFilesystem()->OpenFile("/mnt/" + GetTestDirectory().filename().string() + "/test.bin", 
                                      O_RDONLY, 0644);
    ASSERT_GT(fd, 0);
    
    char buffer[100];
    GetFilesystem()->ReadFile(fd, buffer, sizeof(buffer));
    GetFilesystem()->SeekFile(fd, 0, SEEK_SET);
    GetFilesystem()->CloseFile(fd);
    
    // If we reach here without assertions, lock ordering is correct
    SUCCEED();
}
