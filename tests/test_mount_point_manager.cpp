/**
 * @file test_mount_point_manager.cpp
 * @brief Tests for MountPointManager class
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "ps4/ps4_filesystem.h"
#include "utils/test_fixtures.h"
#include <filesystem>
#include <fstream>

using namespace ps4;
using namespace ps4::test;

class MountPointManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        manager_ = std::make_unique<MountPointManager>();
        
        // Create test directories
        test_host_dir_ = std::filesystem::temp_directory_path() / "mount_test_host";
        test_host_dir2_ = std::filesystem::temp_directory_path() / "mount_test_host2";
        
        std::filesystem::create_directories(test_host_dir_);
        std::filesystem::create_directories(test_host_dir2_);
        
        // Create test files in host directories
        CreateTestFile(test_host_dir_ / "file1.txt", "Test content 1");
        CreateTestFile(test_host_dir2_ / "file2.txt", "Test content 2");
    }
    
    void TearDown() override {
        if (manager_) {
            manager_->UnmountAll();
            manager_.reset();
        }
        
        // Clean up test directories
        try {
            std::filesystem::remove_all(test_host_dir_);
            std::filesystem::remove_all(test_host_dir2_);
        } catch (const std::exception&) {
            // Ignore cleanup errors
        }
    }
    
    void CreateTestFile(const std::filesystem::path& path, const std::string& content) {
        std::ofstream file(path);
        if (file.is_open()) {
            file << content;
            file.close();
        }
    }
    
protected:
    std::unique_ptr<MountPointManager> manager_;
    std::filesystem::path test_host_dir_;
    std::filesystem::path test_host_dir2_;
};

// Basic mount/unmount tests
TEST_F(MountPointManagerTest, MountValidDirectory) {
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/app0", false, false));
    
    // Verify mount was successful
    std::filesystem::path resolved = manager_->GetHostPath("/app0");
    EXPECT_EQ(resolved, test_host_dir_);
}

TEST_F(MountPointManagerTest, MountReadOnlyDirectory) {
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/app0", true, false));
    
    // Verify read-only flag is set
    bool is_read_only = false;
    std::filesystem::path resolved = manager_->GetHostPath("/app0", &is_read_only);
    EXPECT_EQ(resolved, test_host_dir_);
    EXPECT_TRUE(is_read_only);
}

TEST_F(MountPointManagerTest, MountPFSDirectory) {
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/pfs0", false, true));
    
    // Verify PFS mount
    std::filesystem::path resolved = manager_->GetHostPath("/pfs0");
    EXPECT_EQ(resolved, test_host_dir_);
}

TEST_F(MountPointManagerTest, MountNonExistentDirectory) {
    std::filesystem::path non_existent = std::filesystem::temp_directory_path() / "non_existent_dir";
    
    // Should still succeed (mount point manager doesn't validate existence)
    EXPECT_TRUE(manager_->Mount(non_existent, "/app0", false, false));
}

TEST_F(MountPointManagerTest, MountEmptyPaths) {
    // Empty host path should fail
    EXPECT_FALSE(manager_->Mount("", "/app0", false, false));
    
    // Empty guest path should fail
    EXPECT_FALSE(manager_->Mount(test_host_dir_, "", false, false));
}

TEST_F(MountPointManagerTest, MountDuplicateGuestPath) {
    // First mount should succeed
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/app0", false, false));
    
    // Second mount to same guest path should succeed (overwrites)
    EXPECT_TRUE(manager_->Mount(test_host_dir2_, "/app0", false, false));
    
    // Verify the second mount took effect
    std::filesystem::path resolved = manager_->GetHostPath("/app0");
    EXPECT_EQ(resolved, test_host_dir2_);
}

// Unmount tests
TEST_F(MountPointManagerTest, UnmountExistingMount) {
    // Mount first
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/app0", false, false));
    
    // Verify mount exists
    std::filesystem::path resolved = manager_->GetHostPath("/app0");
    EXPECT_EQ(resolved, test_host_dir_);
    
    // Unmount should succeed
    EXPECT_TRUE(manager_->Unmount("/app0"));
    
    // Verify mount is gone
    resolved = manager_->GetHostPath("/app0");
    EXPECT_TRUE(resolved.empty());
}

TEST_F(MountPointManagerTest, UnmountNonExistentMount) {
    // Unmounting non-existent mount should fail
    EXPECT_FALSE(manager_->Unmount("/nonexistent"));
}

TEST_F(MountPointManagerTest, UnmountEmptyPath) {
    // Unmounting empty path should fail
    EXPECT_FALSE(manager_->Unmount(""));
}

TEST_F(MountPointManagerTest, UnmountAllClearsAllMounts) {
    // Create multiple mounts
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/app0", false, false));
    EXPECT_TRUE(manager_->Mount(test_host_dir2_, "/savedata", false, false));
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/system", true, false));
    
    // Verify mounts exist
    EXPECT_FALSE(manager_->GetHostPath("/app0").empty());
    EXPECT_FALSE(manager_->GetHostPath("/savedata").empty());
    EXPECT_FALSE(manager_->GetHostPath("/system").empty());
    
    // Unmount all
    manager_->UnmountAll();
    
    // Verify all mounts are gone
    EXPECT_TRUE(manager_->GetHostPath("/app0").empty());
    EXPECT_TRUE(manager_->GetHostPath("/savedata").empty());
    EXPECT_TRUE(manager_->GetHostPath("/system").empty());
}

// Path resolution tests
TEST_F(MountPointManagerTest, GetHostPathForValidMount) {
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/app0", false, false));
    
    // Test exact match
    std::filesystem::path resolved = manager_->GetHostPath("/app0");
    EXPECT_EQ(resolved, test_host_dir_);
    
    // Test subpath resolution
    resolved = manager_->GetHostPath("/app0/file1.txt");
    EXPECT_EQ(resolved, test_host_dir_ / "file1.txt");
    
    // Test deeper subpath
    resolved = manager_->GetHostPath("/app0/subdir/file.bin");
    EXPECT_EQ(resolved, test_host_dir_ / "subdir" / "file.bin");
}

TEST_F(MountPointManagerTest, GetHostPathForInvalidMount) {
    // Non-existent mount should return empty path
    std::filesystem::path resolved = manager_->GetHostPath("/nonexistent");
    EXPECT_TRUE(resolved.empty());
    
    // Partial match should not work
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/app0", false, false));
    resolved = manager_->GetHostPath("/app");
    EXPECT_TRUE(resolved.empty());
}

TEST_F(MountPointManagerTest, GetHostPathWithReadOnlyFlag) {
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/app0", true, false));
    EXPECT_TRUE(manager_->Mount(test_host_dir2_, "/savedata", false, false));
    
    bool is_read_only = false;
    
    // Check read-only mount
    std::filesystem::path resolved = manager_->GetHostPath("/app0", &is_read_only);
    EXPECT_EQ(resolved, test_host_dir_);
    EXPECT_TRUE(is_read_only);
    
    // Check read-write mount
    is_read_only = true; // Reset to opposite value
    resolved = manager_->GetHostPath("/savedata", &is_read_only);
    EXPECT_EQ(resolved, test_host_dir2_);
    EXPECT_FALSE(is_read_only);
}

TEST_F(MountPointManagerTest, GetHostPathEmptyInput) {
    std::filesystem::path resolved = manager_->GetHostPath("");
    EXPECT_TRUE(resolved.empty());
}

// Multiple mount points tests
TEST_F(MountPointManagerTest, MultipleMountPoints) {
    // Create multiple mount points
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/app0", false, false));
    EXPECT_TRUE(manager_->Mount(test_host_dir2_, "/savedata", true, false));
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/system", true, true));
    
    // Verify all mounts work correctly
    EXPECT_EQ(manager_->GetHostPath("/app0"), test_host_dir_);
    EXPECT_EQ(manager_->GetHostPath("/savedata"), test_host_dir2_);
    EXPECT_EQ(manager_->GetHostPath("/system"), test_host_dir_);
    
    // Verify read-only flags
    bool is_read_only = false;
    manager_->GetHostPath("/app0", &is_read_only);
    EXPECT_FALSE(is_read_only);
    
    manager_->GetHostPath("/savedata", &is_read_only);
    EXPECT_TRUE(is_read_only);
    
    manager_->GetHostPath("/system", &is_read_only);
    EXPECT_TRUE(is_read_only);
}

TEST_F(MountPointManagerTest, OverlappingMountPaths) {
    // Mount overlapping paths
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/app", false, false));
    EXPECT_TRUE(manager_->Mount(test_host_dir2_, "/app0", false, false));
    
    // More specific mount should take precedence
    EXPECT_EQ(manager_->GetHostPath("/app0"), test_host_dir2_);
    EXPECT_EQ(manager_->GetHostPath("/app"), test_host_dir_);
    
    // Subpaths should resolve correctly
    EXPECT_EQ(manager_->GetHostPath("/app0/file.txt"), test_host_dir2_ / "file.txt");
    EXPECT_EQ(manager_->GetHostPath("/app/file.txt"), test_host_dir_ / "file.txt");
}

// Edge cases and error handling
TEST_F(MountPointManagerTest, PathNormalization) {
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/app0", false, false));
    
    // Test various path formats
    EXPECT_EQ(manager_->GetHostPath("/app0/"), test_host_dir_);
    EXPECT_EQ(manager_->GetHostPath("/app0//file.txt"), test_host_dir_ / "file.txt");
    EXPECT_EQ(manager_->GetHostPath("/app0/./file.txt"), test_host_dir_ / "file.txt");
}

TEST_F(MountPointManagerTest, CaseSensitivity) {
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/app0", false, false));
    
    // Mount points should be case sensitive
    EXPECT_TRUE(manager_->GetHostPath("/APP0").empty());
    EXPECT_TRUE(manager_->GetHostPath("/App0").empty());
    EXPECT_FALSE(manager_->GetHostPath("/app0").empty());
}

TEST_F(MountPointManagerTest, SpecialCharactersInPaths) {
    std::filesystem::path special_dir = std::filesystem::temp_directory_path() / "test with spaces";
    std::filesystem::create_directories(special_dir);
    
    // Should handle paths with spaces
    EXPECT_TRUE(manager_->Mount(special_dir, "/special", false, false));
    EXPECT_EQ(manager_->GetHostPath("/special"), special_dir);
    
    // Clean up
    std::filesystem::remove_all(special_dir);
}

// Thread safety tests (basic)
TEST_F(MountPointManagerTest, ConcurrentAccess) {
    const int num_threads = 4;
    const int operations_per_thread = 100;
    std::vector<std::thread> threads;
    
    // Launch threads that perform concurrent mount/unmount operations
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, t, operations_per_thread]() {
            for (int i = 0; i < operations_per_thread; ++i) {
                std::string mount_point = "/thread_" + std::to_string(t) + "_mount_" + std::to_string(i);
                
                // Mount
                manager_->Mount(test_host_dir_, mount_point, false, false);
                
                // Resolve path
                std::filesystem::path resolved = manager_->GetHostPath(mount_point);
                
                // Unmount
                manager_->Unmount(mount_point);
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Manager should still be in a valid state
    EXPECT_TRUE(manager_->Mount(test_host_dir_, "/test", false, false));
    EXPECT_EQ(manager_->GetHostPath("/test"), test_host_dir_);
}
