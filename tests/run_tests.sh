#!/bin/bash
# PS4 Filesystem Test Runner Script for Unix/Linux

echo "========================================"
echo "PS4 Filesystem Test Suite"
echo "========================================"

# Check if build directory exists
if [ ! -d "../build" ]; then
    echo "Error: Build directory not found. Please build the project first."
    echo "Run: cmake --build ../build --config Debug"
    exit 1
fi

# Change to build directory
cd ../build

# Check if test executable exists
if [ ! -f "tests/ps4_filesystem_tests" ]; then
    echo "Error: Test executable not found. Please build the tests first."
    echo "Run: cmake --build . --target ps4_filesystem_tests --config Debug"
    exit 1
fi

echo "Running PS4 Filesystem Tests..."
echo

# Run all tests
./tests/ps4_filesystem_tests --gtest_output=xml:test_results.xml

TEST_RESULT=$?

echo
echo "========================================"

if [ $TEST_RESULT -eq 0 ]; then
    echo "All tests PASSED!"
    echo "Test results saved to: test_results.xml"
else
    echo "Some tests FAILED!"
    echo "Check the output above for details."
    echo "Test results saved to: test_results.xml"
fi

echo "========================================"

# Return to tests directory
cd tests

exit $TEST_RESULT
