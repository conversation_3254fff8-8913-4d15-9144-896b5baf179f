# PS4 Filesystem Test Suite

This directory contains a comprehensive test suite for the PS4 filesystem implementation. The tests are designed to verify all aspects of the filesystem functionality, including file operations, caching, mount point management, and lock ordering compliance.

## Test Structure

### Test Files

- **`test_file_lru_cache.cpp`** - Tests for the FileLRUCache class
- **`test_mount_point_manager.cpp`** - Tests for the MountPointManager class  
- **`test_handle_table.cpp`** - Tests for the HandleTable class
- **`test_ps4_filesystem_core.cpp`** - Core PS4Filesystem functionality tests
- **`test_ps4_filesystem_integration.cpp`** - Integration tests for complex scenarios
- **`test_lock_ordering_compliance.cpp`** - Lock ordering compliance and thread safety tests

### Utility Files

- **`utils/test_fixtures.h/cpp`** - Common test fixtures and base classes
- **`utils/mock_emulator.h/cpp`** - Mock PS4 emulator for testing
- **`utils/test_helpers.h/cpp`** - Helper functions and utilities
- **`main_test.cpp`** - Main test entry point

### Test Data

- **`data/`** - Directory containing test data files
- **`run_tests.bat`** - Windows test runner script
- **`run_tests.sh`** - Unix/Linux test runner script

## Building and Running Tests

### Prerequisites

- Google Test framework (automatically downloaded via vcpkg)
- CMake 3.16 or later
- C++23 compatible compiler

### Building Tests

1. Configure the project with testing enabled:
   ```bash
   cmake -B build -DBUILD_TESTS=ON
   ```

2. Build the test executable:
   ```bash
   cmake --build build --target ps4_filesystem_tests
   ```

### Running Tests

#### Option 1: Using Test Runner Scripts

**Windows:**
```cmd
cd tests
run_tests.bat
```

**Unix/Linux:**
```bash
cd tests
chmod +x run_tests.sh
./run_tests.sh
```

#### Option 2: Direct Execution

```bash
cd build
./tests/ps4_filesystem_tests
```

#### Option 3: Using CTest

```bash
cd build
ctest --output-on-failure
```

### Running Specific Test Suites

You can run specific test suites using Google Test filters:

```bash
# Run only cache tests
./tests/ps4_filesystem_tests --gtest_filter="FileLRUCacheTest.*"

# Run only core filesystem tests
./tests/ps4_filesystem_tests --gtest_filter="PS4FilesystemCoreTest.*"

# Run only lock ordering tests
./tests/ps4_filesystem_tests --gtest_filter="LockOrderingComplianceTest.*"
```

## Test Coverage

### FileLRUCache Tests
- Basic cache operations (put, get, remove)
- LRU eviction policy
- Cache size management
- Statistics tracking
- Thread safety
- Edge cases and error handling

### MountPointManager Tests
- Mount/unmount operations
- Path resolution
- Read-only mount handling
- Multiple mount points
- Concurrent access
- Error conditions

### HandleTable Tests
- Handle creation and deletion
- Standard handle management
- File descriptor reuse
- Thread safety
- Large-scale operations

### PS4Filesystem Core Tests
- File operations (open, read, write, seek, close)
- Directory operations (create, remove, mount)
- Memory management
- State management (save, load, dump)
- Error handling
- Lock ordering compliance

### Integration Tests
- End-to-end file lifecycle
- Multi-file operations
- Cache integration
- Memory operations integration
- State management integration
- Concurrent operations

### Lock Ordering Compliance Tests
- Single-threaded lock ordering verification
- Concurrent access patterns
- Mixed operation scenarios
- Stress testing
- Deadlock prevention

## Test Configuration

### Debug Mode
Tests automatically enable additional debugging features when built in Debug mode:
- Lock ordering validation
- Memory leak detection (with appropriate sanitizers)
- Extended logging

### Performance Testing
Some tests include performance measurements and can be configured with different parameters:
- File sizes for performance tests
- Cache sizes for cache performance tests
- Thread counts for concurrency tests

## Mock Objects

The test suite uses Google Mock to create mock objects for:
- **MockPS4Emulator** - Simulates the PS4 emulator environment
- **MockMemoryDiagnostics** - Simulates memory diagnostic functionality

These mocks allow testing filesystem functionality in isolation without requiring a full emulator environment.

## Test Data

Test data is generated dynamically during test execution, but some static test files are provided in the `data/` directory for specific test scenarios.

## Continuous Integration

The test suite is designed to be run in CI environments:
- Generates XML test reports for CI integration
- Supports parallel test execution
- Provides clear pass/fail status codes
- Includes performance benchmarks

## Troubleshooting

### Common Issues

1. **Test executable not found**
   - Ensure tests are built: `cmake --build build --target ps4_filesystem_tests`

2. **Google Test not found**
   - Ensure vcpkg is properly configured
   - Check that `find_package(GTest REQUIRED)` succeeds

3. **Lock ordering violations**
   - These indicate potential deadlock issues in the code
   - Review the lock ordering documentation in `common/lock_ordering.h`

4. **Test timeouts**
   - Some tests may take longer on slower systems
   - Consider adjusting timeout values for performance tests

### Debug Output

To enable verbose test output:
```bash
./tests/ps4_filesystem_tests --gtest_verbose
```

To run tests with additional logging:
```bash
SPDLOG_LEVEL=debug ./tests/ps4_filesystem_tests
```

## Contributing

When adding new filesystem functionality:

1. Add corresponding tests to the appropriate test file
2. Update mock objects if new emulator interfaces are used
3. Add integration tests for complex features
4. Ensure lock ordering compliance for any new locking code
5. Update this README if new test categories are added

## Test Metrics

The test suite includes approximately:
- 100+ individual test cases
- Coverage of all public filesystem APIs
- Thread safety verification
- Performance benchmarks
- Error condition testing

For detailed test results and coverage reports, run the tests with appropriate flags and review the generated reports.
