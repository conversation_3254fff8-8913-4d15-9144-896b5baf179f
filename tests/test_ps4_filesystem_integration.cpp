/**
 * @file test_ps4_filesystem_integration.cpp
 * @brief Integration tests for PS4 filesystem functionality
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "ps4/ps4_filesystem.h"
#include "utils/test_fixtures.h"
#include "utils/test_helpers.h"
#include "../debug/vector_debug.h"
#include <fcntl.h>
#include <thread>
#include <future>

using namespace ps4;
using namespace ps4::test;
using ::testing::_;
using ::testing::Return;

class PS4FilesystemIntegrationTest : public IntegrationTestFixture {
protected:
    void SetUp() override {
        IntegrationTestFixture::SetUp();
        
        // Set up mock expectations
        EXPECT_CALL(*GetMockEmulator(), Initialize())
            .WillRepeatedly(Return(true));
        EXPECT_CALL(*GetMockEmulator(), GetProcessId())
            .WillRepeatedly(Return(1234));
        EXPECT_CALL(*GetMockEmulator(), AllocateMemory(_, _))
            .WillRepeatedly(Return(0x10000000));
        EXPECT_CALL(*GetMockEmulator(), FreeMemory(_))
            .WillRepeatedly(Return(true));
    }
};

// End-to-end file operations test
TEST_F(PS4FilesystemIntegrationTest, CompleteFileLifecycle) {
    // Create test data
    std::vector<uint8_t> original_data = GenerateTestData(2048, 0xAB);
    std::string host_file = CreateTestFile("lifecycle_test.bin", original_data);
    
    // Mount directory
    std::wstring wide_path(GetTestDirectory().wstring());
    ASSERT_TRUE(GetFilesystem()->MountDirectory(wide_path));
    
    std::string guest_path = "/mnt/" + GetTestDirectory().filename().string() + "/lifecycle_test.bin";
    
    // 1. Open file for reading
    int read_fd = GetFilesystem()->OpenFile(guest_path, O_RDONLY, 0644);
    ASSERT_GT(read_fd, 0);
    
    // 2. Read data and verify
    std::vector<uint8_t> read_buffer(original_data.size());
    ssize_t bytes_read = GetFilesystem()->ReadFile(read_fd, read_buffer.data(), read_buffer.size());
    EXPECT_EQ(bytes_read, static_cast<ssize_t>(original_data.size()));
    EXPECT_EQ(read_buffer, original_data);
    
    // 3. Test seeking
    EXPECT_EQ(GetFilesystem()->SeekFile(read_fd, 100, SEEK_SET), 100);
    
    // 4. Read from new position
    std::vector<uint8_t> partial_buffer(100);
    bytes_read = GetFilesystem()->ReadFile(read_fd, partial_buffer.data(), partial_buffer.size());
    EXPECT_EQ(bytes_read, 100);
    
    // Verify data matches expected portion
    std::vector<uint8_t> expected_partial(original_data.begin() + 100, original_data.begin() + 200);
    EXPECT_EQ(partial_buffer, expected_partial);
    
    // 5. Close read file
    EXPECT_EQ(GetFilesystem()->CloseFile(read_fd), 0);
    
    // 6. Open file for writing
    int write_fd = GetFilesystem()->OpenFile(guest_path, O_WRONLY, 0644);
    ASSERT_GT(write_fd, 0);
    
    // 7. Write new data
    std::vector<uint8_t> new_data = GenerateTestData(512, 0xCD);
    ssize_t bytes_written = GetFilesystem()->WriteFile(write_fd, new_data.data(), new_data.size());
    EXPECT_EQ(bytes_written, static_cast<ssize_t>(new_data.size()));
    
    // 8. Close write file
    EXPECT_EQ(GetFilesystem()->CloseFile(write_fd), 0);
    
    // 9. Verify changes were written
    int verify_fd = GetFilesystem()->OpenFile(guest_path, O_RDONLY, 0644);
    ASSERT_GT(verify_fd, 0);
    
    std::vector<uint8_t> verify_buffer(new_data.size());
    bytes_read = GetFilesystem()->ReadFile(verify_fd, verify_buffer.data(), verify_buffer.size());
    EXPECT_EQ(bytes_read, static_cast<ssize_t>(new_data.size()));
    EXPECT_EQ(verify_buffer, new_data);
    
    EXPECT_EQ(GetFilesystem()->CloseFile(verify_fd), 0);
}

// Multi-file operations test
TEST_F(PS4FilesystemIntegrationTest, MultipleFileOperations) {
    const int num_files = 10;
    std::vector<std::string> host_files;
    std::vector<std::string> guest_paths;
    std::vector<std::vector<uint8_t>> file_data;
    
    // Create multiple test files
    for (int i = 0; i < num_files; ++i) {
        std::string filename = "multi_test_" + std::to_string(i) + ".bin";
        std::vector<uint8_t> data = GenerateTestData(1024 + i * 100, static_cast<uint8_t>(i));
        
        host_files.push_back(CreateTestFile(filename, data));
        file_data.push_back(data);
    }
    
    // Mount directory
    std::wstring wide_path(GetTestDirectory().wstring());
    ASSERT_TRUE(GetFilesystem()->MountDirectory(wide_path));
    
    // Generate guest paths
    for (int i = 0; i < num_files; ++i) {
        guest_paths.push_back("/mnt/" + GetTestDirectory().filename().string() + "/multi_test_" + std::to_string(i) + ".bin");
    }
    
    // Open all files
    std::vector<int> file_descriptors;
    for (int i = 0; i < num_files; ++i) {
        // CRITICAL: Add bounds check for guest_paths vector access
        if (static_cast<size_t>(i) < guest_paths.size()) {
            int fd = GetFilesystem()->OpenFile(SAFE_VECTOR_ACCESS(guest_paths, i, "MultipleFileOperations guest_paths"), O_RDONLY, 0644);
            ASSERT_GT(fd, 0) << "Failed to open file " << i;
            file_descriptors.push_back(fd);
        } else {
            FAIL() << "Index " << i << " out of bounds for guest_paths (size=" << guest_paths.size() << ")";
        }
    }
    
    // Read from all files and verify data
    for (int i = 0; i < num_files; ++i) {
        // CRITICAL: Add bounds check for file_data and file_descriptors vector access
        if (static_cast<size_t>(i) < file_data.size() && static_cast<size_t>(i) < file_descriptors.size()) {
            auto &current_file_data = SAFE_VECTOR_ACCESS(file_data, i, "MultipleFileOperations file_data");
            int fd = SAFE_VECTOR_ACCESS(file_descriptors, i, "MultipleFileOperations file_descriptors");
            std::vector<uint8_t> read_buffer(current_file_data.size());
            ssize_t bytes_read = GetFilesystem()->ReadFile(fd, read_buffer.data(), read_buffer.size());

            EXPECT_EQ(bytes_read, static_cast<ssize_t>(current_file_data.size())) << "File " << i << " read size mismatch";
            EXPECT_EQ(read_buffer, current_file_data) << "File " << i << " data mismatch";
        } else {
            FAIL() << "Index " << i << " out of bounds for file_data (size=" << file_data.size() << ") or file_descriptors (size=" << file_descriptors.size() << ")";
        }
    }
    
    // Close all files
    for (int i = 0; i < num_files; ++i) {
        // CRITICAL: Add bounds check for file_descriptors vector access
        if (static_cast<size_t>(i) < file_descriptors.size()) {
            int fd = SAFE_VECTOR_ACCESS(file_descriptors, i, "MultipleFileOperations close file_descriptors");
            EXPECT_EQ(GetFilesystem()->CloseFile(fd), 0) << "Failed to close file " << i;
        } else {
            FAIL() << "Index " << i << " out of bounds for file_descriptors (size=" << file_descriptors.size() << ")";
        }
    }
}

// Directory operations integration test
TEST_F(PS4FilesystemIntegrationTest, DirectoryOperationsIntegration) {
    // Create directory structure
    ASSERT_TRUE(GetFilesystem()->CreateDirectory("/test/level1", 0755));
    ASSERT_TRUE(GetFilesystem()->CreateDirectory("/test/level1/level2", 0755));
    ASSERT_TRUE(GetFilesystem()->CreateDirectory("/test/level1/level2/level3", 0755));
    
    // Create files in different directories
    std::vector<uint8_t> test_data = GenerateTestData(512, 0xEF);
    
    // Note: For this test to work fully, we'd need WriteFile to work with virtual paths
    // For now, we'll test the directory creation and removal
    
    // Remove directories in reverse order
    EXPECT_TRUE(GetFilesystem()->RemoveDirectory("/test/level1/level2/level3"));
    EXPECT_TRUE(GetFilesystem()->RemoveDirectory("/test/level1/level2"));
    EXPECT_TRUE(GetFilesystem()->RemoveDirectory("/test/level1"));
}

// Cache integration test
TEST_F(PS4FilesystemIntegrationTest, CacheIntegration) {
    // Initialize cache
    GetFilesystem()->InitializeCache(1024 * 1024); // 1MB cache
    
    // Create test files
    std::vector<std::string> filenames;
    std::vector<std::vector<uint8_t>> file_data;
    
    for (int i = 0; i < 5; ++i) {
        std::string filename = "/cache_test/file_" + std::to_string(i) + ".bin";
        std::vector<uint8_t> data = GenerateTestData(1024, static_cast<uint8_t>(i + 0x10));
        
        filenames.push_back(filename);
        file_data.push_back(data);
        
        // Cache the file
        GetFilesystem()->CacheFile(filename, std::vector<uint8_t>(data));
    }
    
    // Verify all files are cached
    for (size_t i = 0; i < filenames.size(); ++i) {
        std::vector<uint8_t> cached_data;
        EXPECT_TRUE(GetFilesystem()->GetCachedFile(filenames[i], cached_data));
        EXPECT_EQ(cached_data, file_data[i]);
    }
    
    // Test cache statistics
    EXPECT_GT(GetFilesystem()->GetCacheUsage(), 0);
    EXPECT_GT(GetFilesystem()->GetCacheHitRatio(), 0.0);
    
    // Invalidate some files
    GetFilesystem()->InvalidateCachedFile(filenames[0]);
    GetFilesystem()->InvalidateCachedFile(filenames[2]);
    
    // Verify invalidated files are no longer cached
    std::vector<uint8_t> cached_data;
    EXPECT_FALSE(GetFilesystem()->GetCachedFile(filenames[0], cached_data));
    EXPECT_FALSE(GetFilesystem()->GetCachedFile(filenames[2], cached_data));
    
    // Verify other files are still cached
    EXPECT_TRUE(GetFilesystem()->GetCachedFile(filenames[1], cached_data));
    EXPECT_TRUE(GetFilesystem()->GetCachedFile(filenames[3], cached_data));
    
    // Clear entire cache
    GetFilesystem()->ClearCache();
    
    // Verify no files are cached
    for (const auto& filename : filenames) {
        EXPECT_FALSE(GetFilesystem()->GetCachedFile(filename, cached_data));
    }
}

// Memory operations integration test
TEST_F(PS4FilesystemIntegrationTest, MemoryOperationsIntegration) {
    // Test memory allocation and deallocation
    std::vector<uint64_t> allocated_addresses;
    
    // Allocate multiple memory blocks
    for (int i = 0; i < 5; ++i) {
        uint64_t size = 4096 * (i + 1); // Different sizes
        uint64_t addr = GetFilesystem()->AllocateVirtualMemory(size, 4096, false);
        EXPECT_NE(addr, 0) << "Failed to allocate memory block " << i;
        allocated_addresses.push_back(addr);
    }
    
    // Test memory protection
    for (uint64_t addr : allocated_addresses) {
        EXPECT_TRUE(GetFilesystem()->ProtectMemory(addr, 4096, 0x7)); // RWX
    }
    
    // Free all allocated memory
    for (uint64_t addr : allocated_addresses) {
        EXPECT_TRUE(GetFilesystem()->FreeVirtualMemory(addr));
    }
}

// State management integration test
TEST_F(PS4FilesystemIntegrationTest, StateManagementIntegration) {
    // Set up some state
    std::vector<uint8_t> test_data = GenerateTestData(1024);
    std::string host_file = CreateTestFile("state_test.bin", test_data);
    
    std::wstring wide_path(GetTestDirectory().wstring());
    GetFilesystem()->MountDirectory(wide_path);
    
    // Initialize cache and add some data
    GetFilesystem()->InitializeCache(1024 * 1024);
    GetFilesystem()->CacheFile("/state/test.bin", std::vector<uint8_t>(test_data));
    
    // Create some directories
    GetFilesystem()->CreateDirectory("/state/test_dir", 0755);
    
    // Save state
    std::stringstream state_stream;
    GetFilesystem()->SaveState(state_stream);
    
    // Verify state was saved
    std::string saved_state = state_stream.str();
    EXPECT_FALSE(saved_state.empty());
    
    // Create new filesystem and load state
    auto new_filesystem = std::make_unique<PS4Filesystem>(*GetMockEmulator());
    ASSERT_TRUE(new_filesystem->Initialize());
    
    state_stream.seekg(0);
    new_filesystem->LoadState(state_stream);
    
    // Verify state was loaded (basic verification)
    std::string loaded_state = new_filesystem->DumpState();
    EXPECT_FALSE(loaded_state.empty());
    
    new_filesystem->Shutdown();
}

// Concurrent operations integration test
TEST_F(PS4FilesystemIntegrationTest, ConcurrentOperationsIntegration) {
    const int num_threads = 4;
    const int operations_per_thread = 50;
    
    // Create test files for concurrent access
    std::vector<std::string> host_files;
    std::vector<std::string> guest_paths;
    
    for (int i = 0; i < num_threads; ++i) {
        std::string filename = "concurrent_test_" + std::to_string(i) + ".bin";
        std::vector<uint8_t> data = GenerateTestData(1024, static_cast<uint8_t>(i + 0x20));
        host_files.push_back(CreateTestFile(filename, data));
    }
    
    // Mount directory
    std::wstring wide_path(GetTestDirectory().wstring());
    ASSERT_TRUE(GetFilesystem()->MountDirectory(wide_path));
    
    for (int i = 0; i < num_threads; ++i) {
        guest_paths.push_back("/mnt/" + GetTestDirectory().filename().string() + "/concurrent_test_" + std::to_string(i) + ".bin");
    }
    
    // Initialize cache for concurrent testing
    GetFilesystem()->InitializeCache(2 * 1024 * 1024);
    
    std::vector<std::thread> threads;
    std::atomic<int> successful_operations{0};
    std::atomic<bool> error_occurred{false};
    
    // Launch threads performing various operations
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, t, operations_per_thread, &guest_paths, &successful_operations, &error_occurred]() {
            try {
                for (int i = 0; i < operations_per_thread; ++i) {
                    int operation = i % 4;
                    
                    switch (operation) {
                        case 0: {
                            // File read operation
                            int fd = GetFilesystem()->OpenFile(guest_paths[t], O_RDONLY, 0644);
                            if (fd > 0) {
                                std::vector<uint8_t> buffer(512);
                                GetFilesystem()->ReadFile(fd, buffer.data(), buffer.size());
                                GetFilesystem()->CloseFile(fd);
                                successful_operations++;
                            }
                            break;
                        }
                        case 1: {
                            // Cache operation
                            std::string cache_file = "/concurrent/cache_" + std::to_string(t) + "_" + std::to_string(i);
                            std::vector<uint8_t> cache_data = GenerateTestData(256, static_cast<uint8_t>(t + i));
                            GetFilesystem()->CacheFile(cache_file, std::move(cache_data));
                            successful_operations++;
                            break;
                        }
                        case 2: {
                            // Directory operation
                            std::string dirname = "/concurrent/dir_" + std::to_string(t) + "_" + std::to_string(i);
                            GetFilesystem()->CreateDirectory(dirname, 0755);
                            GetFilesystem()->RemoveDirectory(dirname);
                            successful_operations++;
                            break;
                        }
                        case 3: {
                            // State operation
                            GetFilesystem()->DumpState();
                            successful_operations++;
                            break;
                        }
                    }
                    
                    // Small delay to increase contention
                    if (i % 10 == 0) {
                        std::this_thread::sleep_for(std::chrono::microseconds(100));
                    }
                }
            } catch (const std::exception& e) {
                error_occurred = true;
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Verify results
    EXPECT_FALSE(error_occurred.load()) << "Error occurred during concurrent operations";
    EXPECT_GT(successful_operations.load(), 0) << "No operations completed successfully";
    
    // Verify filesystem is still in a valid state
    std::string final_state = GetFilesystem()->DumpState();
    EXPECT_FALSE(final_state.empty());
}
