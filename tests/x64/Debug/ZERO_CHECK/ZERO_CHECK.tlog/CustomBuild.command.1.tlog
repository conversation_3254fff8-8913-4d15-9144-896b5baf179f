^D:\SSS\SRC\TESTS\CMAKEFILES\F0C53117B1397FC02237892E6AF23F67\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/sss/src/tests -BD:/sss/src/tests --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/sss/src/tests/ps4_filesystem_tests.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
