@echo off
echo Creating PS4 emulator directory structure...

REM Create main directories
if not exist "ps4_root" mkdir "ps4_root"
if not exist "ps4_root\app0" mkdir "ps4_root\app0"
if not exist "ps4_root\system" mkdir "ps4_root\system"
if not exist "ps4_root\dev" mkdir "ps4_root\dev"
if not exist "ps4_root\savedata" mkdir "ps4_root\savedata"
if not exist "ps4_root\trophy" mkdir "ps4_root\trophy"
if not exist "ps4_root\installed_packages" mkdir "ps4_root\installed_packages"

REM Create system files
echo {} > "ps4_root\system\packages.json"

REM Create some basic device files
echo. > "ps4_root\dev\null"
echo. > "ps4_root\dev\zero"

echo PS4 directory structure created successfully!
echo.
echo Created directories:
echo - ps4_root\app0 (for game files)
echo - ps4_root\system (for system files)
echo - ps4_root\dev (for device files)
echo - ps4_root\savedata (for save data)
echo - ps4_root\trophy (for trophy data)
echo - ps4_root\installed_packages (for PKG installations)
echo.
echo Created files:
echo - ps4_root\system\packages.json (empty package database)
echo - ps4_root\dev\null and ps4_root\dev\zero (basic device files)
echo.
pause
