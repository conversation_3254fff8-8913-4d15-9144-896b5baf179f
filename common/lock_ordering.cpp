// Copyright 2025 <Copyright Owner>

#include "lock_ordering.h"
#include "../debug/vector_debug.h"
#include <algorithm>
#include <cassert>
#include <spdlog/spdlog.h>
#include <vector>

namespace ps4 {

thread_local std::vector<std::pair<LockLevel, const char*>> LockOrderChecker::s_heldLocks;

void LockOrderChecker::AcquireLock(LockLevel level, const char* name) {
#ifdef _DEBUG
    s_heldLocks.emplace_back(level, name);
    spdlog::trace("Lock acquired: {} (level {})", name, static_cast<int>(level));
#endif
}

void LockOrderChecker::ReleaseLock(LockLevel level, const char* name) {
#ifdef _DEBUG
    // Enhanced bounds checking for vector operations
    VALIDATE_VECTOR(s_heldLocks, "LockOrderChecker::ReleaseLock s_heldLocks");

    // Find and remove the lock from our held locks
    auto it = std::find_if(s_heldLocks.rbegin(), s_heldLocks.rend(),
                          [level, name](const auto& pair) {
                              return pair.first == level &&
                                     std::string(pair.second) == std::string(name);
                          });

    if (it != s_heldLocks.rend()) {
        // Convert reverse iterator to forward iterator and erase
        // Additional bounds checking before erase operation
        auto forward_it = std::next(it).base();
        if (forward_it != s_heldLocks.end()) {
            s_heldLocks.erase(forward_it);
            spdlog::trace("Lock released: {} (level {})", name, static_cast<int>(level));
        } else {
            spdlog::error("LOCK ORDER ERROR: Iterator out of bounds during lock release");
            assert(false && "Iterator bounds violation during lock release");
        }
    } else {
        spdlog::error("LOCK ORDER ERROR: Attempted to release lock {} that was not held", name);
        assert(false && "Lock order violation: releasing unheld lock");
    }
#endif
}

void LockOrderChecker::CheckOrder(LockLevel level, const char* name) {
#ifdef _DEBUG
    // Check if we're trying to acquire a lock at a level lower than any currently held lock
    for (const auto& heldLock : s_heldLocks) {
        if (static_cast<int>(level) < static_cast<int>(heldLock.first)) {
            spdlog::error("DEADLOCK RISK: Attempting to acquire lock {} (level {}) "
                         "while holding lock {} (level {}). This violates lock ordering!",
                         name, static_cast<int>(level),
                         heldLock.second, static_cast<int>(heldLock.first));
            
            // Log all currently held locks for debugging
            spdlog::error("Currently held locks:");
            for (const auto& lock : s_heldLocks) {
                spdlog::error("  - {} (level {})", lock.second, static_cast<int>(lock.first));
            }
            
            assert(false && "Lock order violation detected - potential deadlock!");
        }
    }
    
    // Check for duplicate locks (except recursive mutexes)
    for (const auto& heldLock : s_heldLocks) {
        if (heldLock.first == level && std::string(heldLock.second) == std::string(name)) {
            // Only allow duplicate locks for InterruptHandler (recursive mutex)
            if (level != LockLevel::INTERRUPT) {
                spdlog::error("LOCK ERROR: Attempting to acquire lock {} twice "
                             "(non-recursive mutex)", name);
                assert(false && "Attempted to acquire non-recursive lock twice");
            }
        }
    }
#endif
}

} // namespace ps4
