#include "gnm_state.h"
#include "../debug/vector_debug.h"
#include <chrono>
#include <iomanip>
#include <spdlog/spdlog.h> // For logging
#include <sstream>
#include <stdexcept>
#include <string>
#include <algorithm> // For std::fill

namespace ps4 {

GNMRegisterState::GNMRegisterState() : m_stats{} {
  // Acquire an exclusive lock during construction to ensure thread safety
  std::unique_lock<std::shared_mutex> lock(m_regMutex);

  // CRITICAL FIX: Ensure all statistics are properly initialized to zero
  m_stats = {}; // Default-constructs all members to zero

  // Initialize all register arrays to zero
  for (auto &stageRegs : m_shaderRegisters) {
    std::fill(stageRegs.begin(), stageRegs.end(), 0);
  }
  std::fill(m_contextRegisters.begin(), m_contextRegisters.end(), 0);
  std::fill(m_configRegisters.begin(), m_configRegisters.end(), 0);
  std::fill(m_userRegisters.begin(), m_userRegisters.end(), 0);

  spdlog::info("GNMRegisterState constructed with zero-initialized registers and stats.");
}

GNMRegisterState::~GNMRegisterState() {
  Shutdown(); // Ensure resources are cleaned up on destruction
  spdlog::info("GNMRegisterState destroyed.");
}

bool GNMRegisterState::Initialize() {
  auto start = std::chrono::steady_clock::now();
  // Acquire an exclusive lock for initialization to prevent race conditions
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    InitRegisterNames(); // Populate human-readable register names
    m_stats = {};        // Reset statistics
    m_registerCache.clear(); // Clear any existing cache

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    m_stats.accessCount++; // Count initialization as an access/operation
    spdlog::info("GNMRegisterState initialized, latency={}us", static_cast<long long>(latency));
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GNMRegisterState initialization failed: {}", e.what());
    // Re-throw as a specific exception type for caller to handle
    throw GNMException("Initialization failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  // Acquire an exclusive lock for shutdown
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    // Clear all internal maps and cache
    m_shaderRegNames.clear();
    m_contextRegNames.clear();
    m_configRegNames.clear();
    m_userRegNames.clear();
    m_registerCache.clear();
    m_registerChangeCallback = nullptr; // Clear the callback
    m_stats = {}; // Reset statistics

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    m_stats.accessCount++; // Count shutdown as an access/operation
    spdlog::info("GNMRegisterState shutdown, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GNMRegisterState shutdown failed: {}", e.what());
    // Do not re-throw in destructor or shutdown, just log
  }
}

void GNMRegisterState::SetRegisterChangeCallback(
    const RegisterChangeCallback &callback) {
  // Acquire an exclusive lock to set the callback safely
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  m_registerChangeCallback = callback;
  spdlog::debug("GNMRegisterState: Register change callback set.");
}

uint32_t GNMRegisterState::GetShaderRegister(uint32_t stage,
                                             uint32_t offset) const {
  auto start = std::chrono::steady_clock::now();
  // Acquire a shared lock for read-only access
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    uint32_t value;
    // Create a unique cache key for shader registers (stage + offset)
    uint64_t cacheKey = (static_cast<uint64_t>(stage) << 32) | offset;

    if (GetCachedRegister(GNMRegisterType::SHADER_REG, stage, offset, value)) {
      // Cache hit, stats updated by GetCachedRegister
      spdlog::trace("GetShaderRegister: stage={}, offset=0x{:x}, value=0x{:x}, cached, latency={}us",
                    stage, offset, value,
                    std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::steady_clock::now() - start).count());
      return value;
    }

    // Cache miss, proceed to read from actual register array
    if (!ValidateRegister(GNMRegisterType::SHADER_REG, stage, offset, 0)) {
      m_stats.errorCount++;
      throw GNMException(
          "Invalid shader register access: stage=" + std::to_string(stage) +
          ", offset=0x" + std::to_string(offset));
    }
    // Additional bounds check for array access
    if (stage >= SHADER_STAGES) {
      m_stats.errorCount++;
      throw GNMException("Invalid shader stage: " + std::to_string(stage));
    }
    value = SAFE_ARRAY_ACCESS(m_shaderRegisters[stage], offset, MAX_SHADER_REGS, "GetShaderRegister");

    // Update cache (requires exclusive lock, so release shared and re-acquire exclusive)
    lock.unlock();
    std::unique_lock<std::shared_mutex> writeLock(m_regMutex);
    RegisterCacheEntry entry{GNMRegisterType::SHADER_REG, stage, offset, value};
    m_registerCache[cacheKey] = entry; // Insert/update cache entry

    m_stats.accessCount++; // Count this as a successful access
    m_stats.cacheMisses++; // This was a cache miss

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetShaderRegister: stage={}, offset=0x{:x}, value=0x{:x}, latency={}us",
                  stage, offset, value, latency);
    return value;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GetShaderRegister failed: {}", e.what());
    throw GNMException("GetShaderRegister failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::SetShaderRegister(uint32_t stage, uint32_t offset,
                                         uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  // Acquire an exclusive lock for write access
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    if (!ValidateRegister(GNMRegisterType::SHADER_REG, stage, offset, value)) {
      m_stats.errorCount++;
      throw GNMException(
          "Invalid shader register write: stage=" + std::to_string(stage) +
          ", offset=0x" + std::to_string(offset) + ", value=0x" +
          std::to_string(value));
    }
    // Additional bounds check for array access
    if (stage >= SHADER_STAGES) {
      m_stats.errorCount++;
      throw GNMException("Invalid shader stage: " + std::to_string(stage));
    }
    SAFE_ARRAY_ACCESS(m_shaderRegisters[stage], offset, MAX_SHADER_REGS, "SetShaderRegister") = value;

    // Update cache
    uint64_t cacheKey = (static_cast<uint64_t>(stage) << 32) | offset;
    RegisterCacheEntry entry{GNMRegisterType::SHADER_REG, stage, offset, value};
    m_registerCache[cacheKey] = entry; // Insert/update cache entry

    m_stats.accessCount++; // Count this as a successful access
    // This is a write, so it's not a cache hit/miss in the traditional sense for reads.
    // We can consider it a "cache update" or just count it as an access.

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;

    // Log with human-readable name if available
    auto it = m_shaderRegNames.find(offset);
    if (it != m_shaderRegNames.end()) {
      spdlog::trace("SetShaderRegister: stage={}, {} (0x{:x})=0x{:x}, latency={}us",
                    stage, it->second, offset, value, latency);
    } else {
      spdlog::trace("SetShaderRegister: stage={}, offset=0x{:x}, value=0x{:x}, latency={}us",
                    stage, offset, value, latency);
    }

    // CRITICAL FIX: Execute callback without holding mutex to prevent deadlock
    // Make a copy of the callback before releasing the lock
    RegisterChangeCallback callback = m_registerChangeCallback;
    lock.unlock(); // Release the lock before calling external callback

    // Notify PS4GPU of shader register change for rendering updates
    if (callback) {
      callback(GNMRegisterType::SHADER_REG, stage, offset, value);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("SetShaderRegister failed: {}", e.what());
    throw GNMException("SetShaderRegister failed: " + std::string(e.what()));
  }
}

uint32_t GNMRegisterState::GetContextRegister(uint32_t offset) const {
  auto start = std::chrono::steady_clock::now();
  // Acquire a shared lock for read-only access
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    uint32_t value;
    // For context/config/user registers, offset is the cache key
    uint64_t cacheKey = offset;

    if (GetCachedRegister(GNMRegisterType::CONTEXT_REG, 0, offset, value)) {
      // Cache hit, stats updated by GetCachedRegister
      spdlog::trace("GetContextRegister: offset=0x{:x}, value=0x{:x}, cached, latency={}us",
                    offset, value,
                    std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::steady_clock::now() - start).count());
      return value;
    }

    // Cache miss, read from actual register array
    if (!ValidateRegister(GNMRegisterType::CONTEXT_REG, 0, offset, 0)) {
      m_stats.errorCount++;
      throw GNMException("Invalid context register access: offset=0x" +
                         std::to_string(offset));
    }
    value = SAFE_ARRAY_ACCESS(m_contextRegisters, offset, MAX_CONTEXT_REGS, "GetContextRegister");

    // Update cache (requires exclusive lock)
    lock.unlock();
    std::unique_lock<std::shared_mutex> writeLock(m_regMutex);
    RegisterCacheEntry entry{GNMRegisterType::CONTEXT_REG, 0, offset, value};
    m_registerCache[cacheKey] = entry;

    m_stats.accessCount++;
    m_stats.cacheMisses++;

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetContextRegister: offset=0x{:x}, value=0x{:x}, latency={}us",
                  offset, value, latency);
    return value;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GetContextRegister failed: {}", e.what());
    throw GNMException("GetContextRegister failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::SetContextRegister(uint32_t offset, uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  // Acquire an exclusive lock for write access
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    if (!ValidateRegister(GNMRegisterType::CONTEXT_REG, 0, offset, value)) {
      m_stats.errorCount++;
      throw GNMException("Invalid context register write: offset=0x" +
                         std::to_string(offset) + ", value=0x" +
                         std::to_string(value));
    }
    SAFE_ARRAY_ACCESS(m_contextRegisters, offset, MAX_CONTEXT_REGS, "SetContextRegister") = value;

    // Update cache
    uint64_t cacheKey = offset;
    RegisterCacheEntry entry{GNMRegisterType::CONTEXT_REG, 0, offset, value};
    m_registerCache[cacheKey] = entry;

    m_stats.accessCount++;
    // This is a write, not a read hit/miss.

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;

    // Log with human-readable name if available
    auto it = m_contextRegNames.find(offset);
    if (it != m_contextRegNames.end()) {
      spdlog::trace("SetContextRegister: {} (0x{:x})=0x{:x}, latency={}us",
                    it->second, offset, value, latency);
    } else {
      spdlog::trace("SetContextRegister: offset=0x{:x}, value=0x{:x}, latency={}us",
                    offset, value, latency);
    }

    // CRITICAL FIX: Execute callback without holding mutex to prevent deadlock
    RegisterChangeCallback callback = m_registerChangeCallback;
    lock.unlock(); // Release the lock before calling external callback

    // Notify PS4GPU of context register change for rendering updates
    if (callback) {
      callback(GNMRegisterType::CONTEXT_REG, 0, offset, value);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("SetContextRegister failed: {}", e.what());
    throw GNMException("SetContextRegister failed: " + std::string(e.what()));
  }
}

uint32_t GNMRegisterState::GetConfigRegister(uint32_t offset) const {
  auto start = std::chrono::steady_clock::now();
  // Acquire a shared lock for read-only access
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    uint32_t value;
    uint64_t cacheKey = offset;

    if (GetCachedRegister(GNMRegisterType::CONFIG_REG, 0, offset, value)) {
      // Cache hit, stats updated by GetCachedRegister
      spdlog::trace("GetConfigRegister: offset=0x{:x}, value=0x{:x}, cached, latency={}us",
                    offset, value,
                    std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::steady_clock::now() - start).count());
      return value;
    }

    // Cache miss, read from actual register array
    if (!ValidateRegister(GNMRegisterType::CONFIG_REG, 0, offset, 0)) {
      m_stats.errorCount++;
      throw GNMException("Invalid config register access: offset=0x" +
                         std::to_string(offset));
    }
    value = SAFE_ARRAY_ACCESS(m_configRegisters, offset, MAX_CONFIG_REGS, "GetConfigRegister");

    // Update cache (requires exclusive lock)
    lock.unlock();
    std::unique_lock<std::shared_mutex> writeLock(m_regMutex);
    RegisterCacheEntry entry{GNMRegisterType::CONFIG_REG, 0, offset, value};
    m_registerCache[cacheKey] = entry;

    m_stats.accessCount++;
    m_stats.cacheMisses++;

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetConfigRegister: offset=0x{:x}, value=0x{:x}, latency={}us",
                  offset, value, latency);
    return value;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GetConfigRegister failed: {}", e.what());
    throw GNMException("GetConfigRegister failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::SetConfigRegister(uint32_t offset, uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  // Acquire an exclusive lock for write access
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    if (!ValidateRegister(GNMRegisterType::CONFIG_REG, 0, offset, value)) {
      m_stats.errorCount++;
      throw GNMException("Invalid config register write: offset=0x" +
                         std::to_string(offset) + ", value=0x" +
                         std::to_string(value));
    }
    SAFE_ARRAY_ACCESS(m_configRegisters, offset, MAX_CONFIG_REGS, "SetConfigRegister") = value;

    // Update cache
    uint64_t cacheKey = offset;
    RegisterCacheEntry entry{GNMRegisterType::CONFIG_REG, 0, offset, value};
    m_registerCache[cacheKey] = entry;

    m_stats.accessCount++;
    // This is a write, not a read hit/miss.

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;

    // Log with human-readable name if available
    auto it = m_configRegNames.find(offset);
    if (it != m_configRegNames.end()) {
      spdlog::trace("SetConfigRegister: {} (0x{:x})=0x{:x}, latency={}us",
                    it->second, offset, value, latency);
    } else {
      spdlog::trace("SetConfigRegister: offset=0x{:x}, value=0x{:x}, latency={}us",
                    offset, value, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("SetConfigRegister failed: {}", e.what());
    throw GNMException("SetConfigRegister failed: " + std::string(e.what()));
  }
}

uint32_t GNMRegisterState::GetUserRegister(uint32_t offset) const {
  auto start = std::chrono::steady_clock::now();
  // Acquire a shared lock for read-only access
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    uint32_t value;
    uint64_t cacheKey = offset;

    if (GetCachedRegister(GNMRegisterType::USER_REG, 0, offset, value)) {
      // Cache hit, stats updated by GetCachedRegister
      spdlog::trace("GetUserRegister: offset=0x{:x}, value=0x{:x}, cached, latency={}us",
                    offset, value,
                    std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::steady_clock::now() - start).count());
      return value;
    }

    // Cache miss, read from actual register array
    if (!ValidateRegister(GNMRegisterType::USER_REG, 0, offset, 0)) {
      m_stats.errorCount++;
      throw GNMException("Invalid user register access: offset=0x" +
                         std::to_string(offset));
    }
    value = SAFE_ARRAY_ACCESS(m_userRegisters, offset, MAX_USER_REGS, "GetUserRegister");

    // Update cache (requires exclusive lock)
    lock.unlock();
    std::unique_lock<std::shared_mutex> writeLock(m_regMutex);
    RegisterCacheEntry entry{GNMRegisterType::USER_REG, 0, offset, value};
    m_registerCache[cacheKey] = entry;

    m_stats.accessCount++;
    m_stats.cacheMisses++;

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetUserRegister: offset=0x{:x}, value=0x{:x}, latency={}us",
                  offset, value, latency);
    return value;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GetUserRegister failed: {}", e.what());
    throw GNMException("GetUserRegister failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::SetUserRegister(uint32_t offset, uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  // Acquire an exclusive lock for write access
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    if (!ValidateRegister(GNMRegisterType::USER_REG, 0, offset, value)) {
      m_stats.errorCount++;
      throw GNMException("Invalid user register write: offset=0x" +
                         std::to_string(offset) + ", value=0x" +
                         std::to_string(value));
    }
    SAFE_ARRAY_ACCESS(m_userRegisters, offset, MAX_USER_REGS, "SetUserRegister") = value;

    // Update cache
    uint64_t cacheKey = offset;
    RegisterCacheEntry entry{GNMRegisterType::USER_REG, 0, offset, value};
    m_registerCache[cacheKey] = entry;

    m_stats.accessCount++;
    // This is a write, not a read hit/miss.

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;

    // Log with human-readable name if available
    auto it = m_userRegNames.find(offset);
    if (it != m_userRegNames.end()) {
      spdlog::trace("SetUserRegister: {} (0x{:x})=0x{:x}, latency={}us",
                    it->second, offset, value, latency);
    } else {
      spdlog::trace("SetUserRegister: offset=0x{:x}, value=0x{:x}, latency={}us",
                    offset, value, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("SetUserRegister failed: {}", e.what());
    throw GNMException("SetUserRegister failed: " + std::string(e.what()));
  }
}

uint32_t GNMRegisterState::GetShaderBase(uint32_t stage) const {
  auto start = std::chrono::steady_clock::now();
  try {
    if (stage >= SHADER_STAGES) {
      m_stats.errorCount++;
      throw GNMException("Invalid shader stage: " + std::to_string(stage));
    }
    constexpr uint32_t SHADER_BASE_OFFSET = 0x0; // Assuming this is the offset for shader base address

    // CRITICAL FIX: Avoid recursive locking by accessing register directly
    // This function is const, so it needs a shared_lock.
    std::shared_lock<std::shared_mutex> lock(m_regMutex);
    uint32_t value = SAFE_ARRAY_ACCESS(m_shaderRegisters[stage], SHADER_BASE_OFFSET, MAX_SHADER_REGS, "GetShaderBaseAddress");

    m_stats.accessCount++; // Count this as an access
    // This is a direct read, not a cache hit/miss for the GetCachedRegister function.

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetShaderBase: stage={}, value=0x{:x}, latency={}us", stage,
                  value, latency);
    return value;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GetShaderBase failed: {}", e.what());
    throw GNMException("GetShaderBase failed: " + std::string(e.what()));
  }
}

uint32_t GNMRegisterState::GetRenderTarget(uint32_t index) const {
  auto start = std::chrono::steady_clock::now();
  try {
    // Assuming 8 render targets (RT0-RT7)
    if (index >= 8) {
      m_stats.errorCount++;
      throw GNMException("Invalid render target index: " +
                         std::to_string(index));
    }
    // Assuming render target addresses are stored contiguously starting from 0x100
    constexpr uint32_t RENDER_TARGET_BASE_OFFSET = 0x100;

    // CRITICAL FIX: Avoid recursive locking by accessing register directly
    std::shared_lock<std::shared_mutex> lock(m_regMutex);
    uint32_t offset = RENDER_TARGET_BASE_OFFSET + index;
    if (offset >= MAX_CONTEXT_REGS) { // Ensure offset is within bounds
      m_stats.errorCount++;
      throw GNMException("Render target offset out of bounds: " +
                         std::to_string(offset));
    }
    uint32_t value = SAFE_ARRAY_ACCESS(m_contextRegisters, offset, MAX_CONTEXT_REGS, "GetContextRegister");

    m_stats.accessCount++; // Count this as an access
    // This is a direct read, not a cache hit/miss for the GetCachedRegister function.

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetRenderTarget: index={}, value=0x{:x}, latency={}us",
                  index, value, latency);
    return value;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GetRenderTarget failed: {}", e.what());
    throw GNMException("GetRenderTarget failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::GetViewport(float &x, float &y, float &width,
                                   float &height, float &minDepth,
                                   float &maxDepth) const {
  auto start = std::chrono::steady_clock::now();
  // Acquire a shared lock for read-only access to multiple registers
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    // Assuming viewport parameters are stored as floats in specific context registers
    constexpr uint32_t VIEWPORT_X_OFFSET = 0x80;
    constexpr uint32_t VIEWPORT_Y_OFFSET = 0x81;
    constexpr uint32_t VIEWPORT_WIDTH_OFFSET = 0x82;
    constexpr uint32_t VIEWPORT_HEIGHT_OFFSET = 0x83;
    constexpr uint32_t VIEWPORT_MIN_DEPTH_OFFSET = 0x84;
    constexpr uint32_t VIEWPORT_MAX_DEPTH_OFFSET = 0x85;

    // Use reinterpret_cast to treat uint32_t as float, assuming IEEE 754 representation
    x = *reinterpret_cast<const float *>(&SAFE_ARRAY_ACCESS(m_contextRegisters, VIEWPORT_X_OFFSET, MAX_CONTEXT_REGS, "GetViewportState x"));
    y = *reinterpret_cast<const float *>(&SAFE_ARRAY_ACCESS(m_contextRegisters, VIEWPORT_Y_OFFSET, MAX_CONTEXT_REGS, "GetViewportState y"));
    width = *reinterpret_cast<const float *>(&SAFE_ARRAY_ACCESS(m_contextRegisters, VIEWPORT_WIDTH_OFFSET, MAX_CONTEXT_REGS, "GetViewportState width"));
    height = *reinterpret_cast<const float *>(&SAFE_ARRAY_ACCESS(m_contextRegisters, VIEWPORT_HEIGHT_OFFSET, MAX_CONTEXT_REGS, "GetViewportState height"));
    minDepth = *reinterpret_cast<const float *>(&SAFE_ARRAY_ACCESS(m_contextRegisters, VIEWPORT_MIN_DEPTH_OFFSET, MAX_CONTEXT_REGS, "GetViewportState minDepth"));
    maxDepth = *reinterpret_cast<const float *>(&SAFE_ARRAY_ACCESS(m_contextRegisters, VIEWPORT_MAX_DEPTH_OFFSET, MAX_CONTEXT_REGS, "GetViewportState maxDepth"));

    m_stats.accessCount += 6; // Accessed 6 registers
    // This is a direct read of multiple registers, not a cache hit/miss for a single entry.

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetViewport: x={}, y={}, width={}, height={}, minDepth={}, maxDepth={}, latency={}us",
                  x, y, width, height, minDepth, maxDepth, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GetViewport failed: {}", e.what());
    throw GNMException("GetViewport failed: " + std::string(e.what()));
  }
}

bool GNMRegisterState::GetCachedRegister(GNMRegisterType type, uint32_t stage,
                                         uint32_t offset,
                                         uint32_t &value) const {
  auto start = std::chrono::steady_clock::now();
  // This function is called from other Get*Register functions that already hold a shared_lock.
  // No additional lock is needed here to prevent recursive locking.
  try {
    uint64_t cacheKey;
    if (type == GNMRegisterType::SHADER_REG) {
      cacheKey = (static_cast<uint64_t>(stage) << 32) | offset;
    } else {
      cacheKey = offset; // For other types, offset is sufficient as key
    }

    auto it = m_registerCache.find(cacheKey);
    // Check if key exists AND if the cached entry matches the requested type, stage, and offset
    // This is important to avoid collisions if different register types happen to have the same offset.
    if (it != m_registerCache.end() && it->second.type == type &&
        it->second.stage == stage && it->second.offset == offset) {
      value = it->second.value;
      it->second.cacheHits++; // Increment hits for this specific entry
      m_stats.cacheHits++;    // Increment overall cache hits
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetCachedRegister: type={}, stage={}, offset=0x{:x}, value=0x{:x}, hit, latency={}us",
                    static_cast<int>(type), stage, offset, value, latency);
      return true;
    }
    m_stats.cacheMisses++; // Increment overall cache misses
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetCachedRegister: type={}, stage={}, offset=0x{:x}, miss, latency={}us",
                  static_cast<int>(type), stage, offset, latency);
    return false;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GetCachedRegister failed: {}", e.what());
    // Do not re-throw, as this is a query function. Return false and log error.
    return false;
  }
}

void GNMRegisterState::ClearRegisterCache() {
  auto start = std::chrono::steady_clock::now();
  // Acquire an exclusive lock to modify the cache
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    m_registerCache.clear();
    m_stats.accessCount++; // Clearing cache is an operation
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ClearRegisterCache: Cleared cache, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("ClearRegisterCache failed: {}", e.what());
  }
}

std::string GNMRegisterState::DumpState(bool nonZeroOnly) const {
  auto start = std::chrono::steady_clock::now();
  // Acquire a shared lock for read-only access to all register arrays and names
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    std::stringstream ss;
    ss << "GNM Register State:\n";

    // Dump Shader Registers
    for (uint32_t stage = 0; stage < SHADER_STAGES; stage++) {
      ss << "--- Shader Stage " << stage << " Registers ---\n";
      for (uint32_t i = 0; i < MAX_SHADER_REGS; i++) {
        uint32_t value = SAFE_ARRAY_ACCESS(m_shaderRegisters[stage], i, MAX_SHADER_REGS, "DumpRegisters shader"); // Safe access even with lock held
        if (!nonZeroOnly || value != 0) {
          auto it = m_shaderRegNames.find(i);
          if (it != m_shaderRegNames.end()) {
            ss << "  " << std::left << std::setw(20) << it->second;
          } else {
            ss << "  Reg[0x" << std::hex << std::setw(4) << std::setfill('0') << i << "]";
            ss << std::left << std::setw(10) << ""; // Pad for alignment
          }
          ss << " = 0x" << std::hex << std::setw(8) << std::setfill('0') << value << "\n";
        }
      }
    }

    // Dump Context Registers
    ss << "--- Context Registers ---\n";
    for (uint32_t i = 0; i < MAX_CONTEXT_REGS; i++) {
      uint32_t value = SAFE_ARRAY_ACCESS(m_contextRegisters, i, MAX_CONTEXT_REGS, "DumpRegisters context"); // Safe access even with lock held
      if (!nonZeroOnly || value != 0) {
        auto it = m_contextRegNames.find(i);
        if (it != m_contextRegNames.end()) {
          ss << "  " << std::left << std::setw(20) << it->second;
        } else {
          ss << "  Reg[0x" << std::hex << std::setw(4) << std::setfill('0') << i << "]";
          ss << std::left << std::setw(10) << "";
        }
        ss << " = 0x" << std::hex << std::setw(8) << std::setfill('0') << value << "\n";
      }
    }

    // Dump Config Registers
    ss << "--- Config Registers ---\n";
    for (uint32_t i = 0; i < MAX_CONFIG_REGS; i++) {
      uint32_t value = SAFE_ARRAY_ACCESS(m_configRegisters, i, MAX_CONFIG_REGS, "DumpRegisters config"); // Safe access even with lock held
      if (!nonZeroOnly || value != 0) {
        auto it = m_configRegNames.find(i);
        if (it != m_configRegNames.end()) {
          ss << "  " << std::left << std::setw(20) << it->second;
        } else {
          ss << "  Reg[0x" << std::hex << std::setw(4) << std::setfill('0') << i << "]";
          ss << std::left << std::setw(10) << "";
        }
        ss << " = 0x" << std::hex << std::setw(8) << std::setfill('0') << value << "\n";
      }
    }

    // Dump User Registers
    ss << "--- User Registers ---\n";
    for (uint32_t i = 0; i < MAX_USER_REGS; i++) {
      // CRITICAL: Add bounds check for user register access
      if (i >= sizeof(m_userRegisters)/sizeof(m_userRegisters[0])) {
        spdlog::error("DumpRegisters: User register index {} out of bounds (max={})",
                     i, sizeof(m_userRegisters)/sizeof(m_userRegisters[0]) - 1);
        break;
      }
      uint32_t value = SAFE_ARRAY_ACCESS(m_userRegisters, i, MAX_USER_REGS, "DumpRegisters user"); // Use safe access
      if (!nonZeroOnly || value != 0) {
        auto it = m_userRegNames.find(i);
        if (it != m_userRegNames.end()) {
          ss << "  " << std::left << std::setw(20) << it->second;
        } else {
          ss << "  Reg[0x" << std::hex << std::setw(4) << std::setfill('0') << i << "]";
          ss << std::left << std::setw(10) << "";
        }
        ss << " = 0x" << std::hex << std::setw(8) << std::setfill('0') << value << "\n";
      }
    }

    m_stats.accessCount++; // Count this as an access/operation

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("DumpState: nonZeroOnly={}, latency={}us", nonZeroOnly, latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("DumpState failed: {}", e.what());
    throw GNMException("DumpState failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::InitRegisterNames() {
  // This function is called from Initialize() which already holds an exclusive lock.
  // No additional lock is needed here.
  try {
    // Clear existing names to ensure a clean state
    m_shaderRegNames.clear();
    m_contextRegNames.clear();
    m_configRegNames.clear();
    m_userRegNames.clear();

    // Populate Shader Register Names (examples)
    m_shaderRegNames[0x0] = "SHADER_BASE_ADDR";
    m_shaderRegNames[0x1] = "SHADER_SIZE";
    m_shaderRegNames[0x2] = "SHADER_ENTRY_POINT";
    m_shaderRegNames[0x10] = "VS_OUT_POSITION";
    m_shaderRegNames[0x11] = "VS_OUT_COLOR";

    // Populate Context Register Names (examples)
    m_contextRegNames[0x80] = "VIEWPORT_X";
    m_contextRegNames[0x81] = "VIEWPORT_Y";
    m_contextRegNames[0x82] = "VIEWPORT_WIDTH";
    m_contextRegNames[0x83] = "VIEWPORT_HEIGHT";
    m_contextRegNames[0x84] = "VIEWPORT_MIN_DEPTH";
    m_contextRegNames[0x85] = "VIEWPORT_MAX_DEPTH";
    m_contextRegNames[0x100] = "RENDER_TARGET_0_ADDR";
    m_contextRegNames[0x101] = "RENDER_TARGET_1_ADDR";
    m_contextRegNames[0x102] = "RENDER_TARGET_2_ADDR";
    m_contextRegNames[0x103] = "RENDER_TARGET_3_ADDR";
    m_contextRegNames[0x104] = "RENDER_TARGET_4_ADDR";
    m_contextRegNames[0x105] = "RENDER_TARGET_5_ADDR";
    m_contextRegNames[0x106] = "RENDER_TARGET_6_ADDR";
    m_contextRegNames[0x107] = "RENDER_TARGET_7_ADDR";
    m_contextRegNames[0x200] = "TESS_MODE";
    m_contextRegNames[0x201] = "TESS_FACTOR_SCALE";
    m_contextRegNames[0x202] = "TESS_EDGE_FACTOR_SCALE";
    m_contextRegNames[0x203] = "TESS_CONTROL_POINT_COUNT";
    m_contextRegNames[0x204] = "TESS_DISTRIBUTION";
    m_contextRegNames[0x205] = "TESS_WINDING";
    m_contextRegNames[0x280] = "VTX_BINDING_0_DESC";
    m_contextRegNames[0x281] = "VTX_BINDING_1_DESC";
    m_contextRegNames[0x290] = "VTX_BINDING_0_STRIDE";
    m_contextRegNames[0x291] = "VTX_BINDING_1_STRIDE";
    m_contextRegNames[0x2A0] = "VTX_BINDING_0_FLAGS";
    m_contextRegNames[0x2A1] = "VTX_BINDING_1_FLAGS";
    m_contextRegNames[0x300] = "VTX_ATTR_0_FORMAT";
    m_contextRegNames[0x301] = "VTX_ATTR_1_FORMAT";
    m_contextRegNames[0x310] = "VTX_ATTR_0_BINDING";
    m_contextRegNames[0x311] = "VTX_ATTR_1_BINDING";
    m_contextRegNames[0x320] = "VTX_ATTR_0_OFFSET";
    m_contextRegNames[0x321] = "VTX_ATTR_1_OFFSET";


    // Populate Config Register Names (examples)
    m_configRegNames[0x0] = "CONFIG_REGISTER_0";
    m_configRegNames[0x1] = "CONFIG_REGISTER_1";

    // Populate User Register Names (examples)
    m_userRegNames[0x0] = "USER_REGISTER_0";
    m_userRegNames[0x1] = "USER_REGISTER_1";

    spdlog::debug("InitRegisterNames: Populated {} shader, {} context, {} config, {} user register names.",
                  m_shaderRegNames.size(), m_contextRegNames.size(),
                  m_configRegNames.size(), m_userRegNames.size());
  } catch (const std::exception &e) {
    spdlog::error("InitRegisterNames failed: {}", e.what());
    throw GNMException("InitRegisterNames failed: " + std::string(e.what()));
  }
}

GNMRegisterStateStats GNMRegisterState::GetStats() const {
  auto start = std::chrono::steady_clock::now();
  // Acquire a shared lock for read-only access to stats
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    GNMRegisterStateStats stats = m_stats; // Copy current stats
    // Getting stats is a read operation, not a register access.
    // We don't increment accessCount or cache hits/misses for this.
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    stats.totalLatencyUs += latency; // Add latency of this call to the returned stats
    spdlog::trace("GetStats: Retrieved stats, latency={}us", latency);
    return stats;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GetStats failed: {}", e.what());
    return GNMRegisterStateStats(); // Return default-initialized stats on error
  }
}

void GNMRegisterState::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  // Acquire a shared lock for read-only access to all internal state
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    uint32_t version = 1; // Serialization version
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));

    // Serialize register arrays
    for (const auto &stageRegs : m_shaderRegisters) {
      out.write(reinterpret_cast<const char *>(stageRegs.data()),
                stageRegs.size() * sizeof(uint32_t));
    }
    out.write(reinterpret_cast<const char *>(m_contextRegisters.data()),
              m_contextRegisters.size() * sizeof(uint32_t));
    out.write(reinterpret_cast<const char *>(m_configRegisters.data()),
              m_configRegisters.size() * sizeof(uint32_t));
    out.write(reinterpret_cast<const char *>(m_userRegisters.data()),
              m_userRegisters.size() * sizeof(uint32_t));

    // Helper lambda to write maps
    auto writeMap = [&](const auto& map) {
        uint64_t count = map.size();
        out.write(reinterpret_cast<const char *>(&count), sizeof(count));
        for (const auto &[offset, name] : map) {
            out.write(reinterpret_cast<const char *>(&offset), sizeof(offset));
            uint32_t nameLen = static_cast<uint32_t>(name.size());
            out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
            out.write(name.data(), nameLen);
        }
    };

    // Serialize register name maps
    writeMap(m_shaderRegNames);
    writeMap(m_contextRegNames);
    writeMap(m_configRegNames);
    writeMap(m_userRegNames);

    // Serialize register cache
    uint64_t cacheCount = m_registerCache.size();
    out.write(reinterpret_cast<const char *>(&cacheCount), sizeof(cacheCount));
    for (const auto &[key, entry] : m_registerCache) {
      out.write(reinterpret_cast<const char *>(&key), sizeof(key));
      out.write(reinterpret_cast<const char *>(&entry.type),
                sizeof(entry.type));
      out.write(reinterpret_cast<const char *>(&entry.stage),
                sizeof(entry.stage));
      out.write(reinterpret_cast<const char *>(&entry.offset),
                sizeof(entry.offset));
      out.write(reinterpret_cast<const char *>(&entry.value),
                sizeof(entry.value));
      out.write(reinterpret_cast<const char *>(&entry.cacheHits),
                sizeof(entry.cacheHits));
      out.write(reinterpret_cast<const char *>(&entry.cacheMisses),
                sizeof(entry.cacheMisses));
    }

    // Serialize overall stats
    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));

    if (!out.good()) {
      m_stats.errorCount++;
      throw GNMException("Failed to write register state to stream.");
    }

    // Saving state is an operation, not a register access.
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    // Don't modify m_stats here as it's const. The stats are already part of the saved state.
    spdlog::info("SaveState: Saved register state, shader_names={}, context_names={}, "
                 "config_names={}, user_names={}, cache_entries={}, latency={}us",
                 m_shaderRegNames.size(), m_contextRegNames.size(), m_configRegNames.size(),
                 m_userRegNames.size(), cacheCount, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("SaveState failed: {}", e.what());
    throw GNMException("SaveState failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  // Acquire an exclusive lock for modification
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    // Clear current state before loading new state
    for (auto &stageRegs : m_shaderRegisters) {
      std::fill(stageRegs.begin(), stageRegs.end(), 0);
    }
    std::fill(m_contextRegisters.begin(), m_contextRegisters.end(), 0);
    std::fill(m_configRegisters.begin(), m_configRegisters.end(), 0);
    std::fill(m_userRegisters.begin(), m_userRegisters.end(), 0);
    m_shaderRegNames.clear();
    m_contextRegNames.clear();
    m_configRegNames.clear();
    m_userRegNames.clear();
    m_registerCache.clear();
    m_stats = {}; // Reset stats before loading new ones

    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (!in.good()) {
        throw GNMException("Failed to read version from stream.");
    }
    if (version != 1) {
      m_stats.errorCount++;
      throw GNMException("Unsupported register state version: " +
                         std::to_string(version));
    }

    // Read register arrays
    for (auto &stageRegs : m_shaderRegisters) {
      in.read(reinterpret_cast<char *>(stageRegs.data()),
              stageRegs.size() * sizeof(uint32_t));
      if (!in.good()) {
        throw GNMException("Failed to read shader registers from stream.");
      }
    }
    in.read(reinterpret_cast<char *>(m_contextRegisters.data()),
            m_contextRegisters.size() * sizeof(uint32_t));
    if (!in.good()) {
      throw GNMException("Failed to read context registers from stream.");
    }
    in.read(reinterpret_cast<char *>(m_configRegisters.data()),
            m_configRegisters.size() * sizeof(uint32_t));
    if (!in.good()) {
      throw GNMException("Failed to read config registers from stream.");
    }
    in.read(reinterpret_cast<char *>(m_userRegisters.data()),
            m_userRegisters.size() * sizeof(uint32_t));
    if (!in.good()) {
      throw GNMException("Failed to read user registers from stream.");
    }

    // Helper lambda to read maps
    auto readMap = [&](auto& map) {
        uint64_t count;
        in.read(reinterpret_cast<char *>(&count), sizeof(count));
        if (!in.good()) throw GNMException("Failed to read map count.");
        // Add a sanity check for count to prevent excessive memory allocation or crashes
        if (count > 100000) { // Arbitrary large number, adjust as needed
            throw GNMException("Map count too large, potential data corruption.");
        }
        for (uint64_t i = 0; i < count && in.good(); ++i) {
            uint32_t offset;
            uint32_t nameLen;
            in.read(reinterpret_cast<char *>(&offset), sizeof(offset));
            in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
            if (!in.good()) throw GNMException("Failed to read map entry.");
            // Sanity check for name length
            if (nameLen > 256) { // Arbitrary max name length
                spdlog::warn("LoadState: Skipping map entry with excessive name length ({}).", nameLen);
                // Attempt to skip the data to continue reading, if possible
                in.seekg(nameLen, std::ios_base::cur);
                if (!in.good()) throw GNMException("Failed to skip excessive name data.");
                continue;
            }
            std::string name(nameLen, '\0');
            in.read(name.data(), nameLen);
            if (!in.good()) throw GNMException("Failed to read map name data.");
            map[offset] = std::move(name);
        }
    };

    // Read register name maps
    readMap(m_shaderRegNames);
    readMap(m_contextRegNames);
    readMap(m_configRegNames);
    readMap(m_userRegNames);

    // Read register cache
    uint64_t cacheCount;
    in.read(reinterpret_cast<char *>(&cacheCount), sizeof(cacheCount));
    if (!in.good()) throw GNMException("Failed to read cache count.");
    if (cacheCount > 1000000) { // Sanity check for cache size
        throw GNMException("Cache count too large, potential data corruption.");
    }
    for (uint64_t i = 0; i < cacheCount && in.good(); ++i) {
      uint64_t key;
      RegisterCacheEntry entry;
      in.read(reinterpret_cast<char *>(&key), sizeof(key));
      in.read(reinterpret_cast<char *>(&entry.type), sizeof(entry.type));
      in.read(reinterpret_cast<char *>(&entry.stage), sizeof(entry.stage));
      in.read(reinterpret_cast<char *>(&entry.offset), sizeof(entry.offset));
      in.read(reinterpret_cast<char *>(&entry.value), sizeof(entry.value));
      in.read(reinterpret_cast<char *>(&entry.cacheHits),
              sizeof(entry.cacheHits));
      in.read(reinterpret_cast<char *>(&entry.cacheMisses),
              sizeof(entry.cacheMisses));
      if (!in.good()) throw GNMException("Failed to read cache entry metadata.");
      m_registerCache[key] = entry;
    }

    // Read overall stats
    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));
    if (!in.good()) {
      m_stats.errorCount++;
      throw GNMException("Failed to read register state stats from stream.");
    }

    // Loading state is an operation
    m_stats.accessCount++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("LoadState: Loaded register state, shader_names={}, context_names={}, "
                 "config_names={}, user_names={}, cache_entries={}, latency={}us",
                 m_shaderRegNames.size(), m_contextRegNames.size(), m_configRegNames.size(),
                 m_userRegNames.size(), cacheCount, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("LoadState failed: {}", e.what());
    throw GNMException("LoadState failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::DeserializeFromStream(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  try {
    // This is simply an alias for LoadState with proper error handling and metrics
    LoadState(in);

    m_stats.accessCount++; // Count this as an access/operation
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("DeserializeFromStream: latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("DeserializeFromStream failed: {}", e.what());
    throw GNMException("DeserializeFromStream failed: " +
                       std::string(e.what()));
  }
}

std::vector<uint8_t> GNMRegisterState::Serialize(bool includeCache) const {
  auto start = std::chrono::steady_clock::now();
  // Acquire a shared lock for read-only access to all internal state
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    std::stringstream stream;

    // If includeCache is false, we would ideally have a separate SaveState_NoCache
    // For now, we'll just call the main SaveState and note the limitation.
    // A more robust solution would involve conditionally writing parts of the state.
    SaveState(stream);

    if (!includeCache) {
      spdlog::warn("Serialize: 'includeCache=false' is currently ignored. Full state including cache is always serialized.");
      // To truly exclude cache, SaveState would need a parameter or a separate function.
      // For example, you could re-implement SaveState to skip the cache section if a flag is set.
    }

    std::string str = stream.str();
    std::vector<uint8_t> result(str.begin(), str.end());

    m_stats.accessCount++; // Count this as an access/operation
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("Serialize: size={}, includeCache={}, latency={}us",
                  result.size(), includeCache, latency);
    return result;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("Serialize failed: {}", e.what());
    throw GNMException("Serialize failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::Deserialize(const std::vector<uint8_t> &data) {
  auto start = std::chrono::steady_clock::now();
  // Acquire an exclusive lock for modification
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    if (data.empty()) {
      throw GNMException("Cannot deserialize empty data.");
    }

    std::stringstream stream;
    // Write the byte vector data into the stringstream
    stream.write(reinterpret_cast<const char *>(data.data()), data.size());
    stream.seekg(0); // Reset stream position to the beginning

    lock.unlock(); // Release lock before calling LoadState (which will acquire its own lock)
    LoadState(stream); // Use the existing LoadState logic
    lock.lock(); // Re-acquire for stats update

    m_stats.accessCount++; // Count this as an access/operation
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("Deserialize: size={}, latency={}us", data.size(), latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("Deserialize failed: {}", e.what());
    throw GNMException("Deserialize failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::GetTessellationState(uint32_t &tessMode, float &tessFactorScale,
                                            float &tessEdgeFactorScale,
                                            uint32_t &tessControlPointCount,
                                            uint32_t &tessDistribution,
                                            uint32_t &tessWinding) const {
  auto start = std::chrono::steady_clock::now();
  // Acquire a shared lock for read-only access to multiple registers
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    // Assuming tessellation parameters are stored in specific context registers
    constexpr uint32_t TESS_MODE_OFFSET = 0x200;
    constexpr uint32_t TESS_FACTOR_SCALE_OFFSET = 0x201;
    constexpr uint32_t TESS_EDGE_FACTOR_SCALE_OFFSET = 0x202;
    constexpr uint32_t TESS_CONTROL_POINT_COUNT_OFFSET = 0x203;
    constexpr uint32_t TESS_DISTRIBUTION_OFFSET = 0x204;
    constexpr uint32_t TESS_WINDING_OFFSET = 0x205;
    // Safe register access (locks are already held)
    tessMode = SAFE_ARRAY_ACCESS(m_contextRegisters, TESS_MODE_OFFSET, MAX_CONTEXT_REGS, "GetTessellationState tessMode");
    tessFactorScale = *reinterpret_cast<const float *>(&SAFE_ARRAY_ACCESS(m_contextRegisters, TESS_FACTOR_SCALE_OFFSET, MAX_CONTEXT_REGS, "GetTessellationState tessFactorScale"));
    tessEdgeFactorScale = *reinterpret_cast<const float *>(&SAFE_ARRAY_ACCESS(m_contextRegisters, TESS_EDGE_FACTOR_SCALE_OFFSET, MAX_CONTEXT_REGS, "GetTessellationState tessEdgeFactorScale"));
    tessControlPointCount = SAFE_ARRAY_ACCESS(m_contextRegisters, TESS_CONTROL_POINT_COUNT_OFFSET, MAX_CONTEXT_REGS, "GetTessellationState tessControlPointCount");
    tessDistribution = SAFE_ARRAY_ACCESS(m_contextRegisters, TESS_DISTRIBUTION_OFFSET, MAX_CONTEXT_REGS, "GetTessellationState tessDistribution");
    tessWinding = SAFE_ARRAY_ACCESS(m_contextRegisters, TESS_WINDING_OFFSET, MAX_CONTEXT_REGS, "GetTessellationState tessWinding");

    m_stats.accessCount += 6; // Accessed 6 registers

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetTessellationState: mode={}, factorScale={}, edgeFactorScale={}, "
                  "controlPoints={}, distribution={}, winding={}, latency={}us",
                  tessMode, tessFactorScale, tessEdgeFactorScale, tessControlPointCount,
                  tessDistribution, tessWinding, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GetTessellationState failed: {}", e.what());
    throw GNMException("GetTessellationState failed: " + std::string(e.what()));
  }
}

std::vector<VertexBindingDesc>
GNMRegisterState::GetVertexInputBindings(uint32_t maxBindings) const {
  auto start = std::chrono::steady_clock::now();
  // Acquire a shared lock for read-only access to multiple registers
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    if (maxBindings > 32) { // Reasonable upper limit
      spdlog::warn("GetVertexInputBindings: maxBindings ({}) clamped to 32", maxBindings);
      maxBindings = 32;
    }

    std::vector<VertexBindingDesc> bindings;
    bindings.reserve(maxBindings);

    // Assuming vertex binding descriptors start at context register 0x280
    constexpr uint32_t VTX_BINDING_BASE_OFFSET = 0x280;
    constexpr uint32_t VTX_STRIDE_BASE_OFFSET = 0x290;
    constexpr uint32_t VTX_FLAGS_BASE_OFFSET = 0x2A0;
    for (uint32_t i = 0; i < maxBindings; i++) {
      // Check if the binding descriptor offset is within bounds
      uint32_t descOffset = VTX_BINDING_BASE_OFFSET + i;
      uint32_t strideOffset = VTX_STRIDE_BASE_OFFSET + i;
      uint32_t flagsOffset = VTX_FLAGS_BASE_OFFSET + i;
      if (descOffset >= MAX_CONTEXT_REGS || strideOffset >= MAX_CONTEXT_REGS || flagsOffset >= MAX_CONTEXT_REGS) {
        spdlog::warn("GetVertexInputBindings: Binding {} offset out of bounds, stopping enumeration", i);
        break;
      }

      uint32_t descValue = SAFE_ARRAY_ACCESS(m_contextRegisters, descOffset, MAX_CONTEXT_REGS, "GetVertexBufferState descValue");
      uint32_t strideValue = SAFE_ARRAY_ACCESS(m_contextRegisters, strideOffset, MAX_CONTEXT_REGS, "GetVertexBufferState strideValue");
      uint32_t flagsValue = SAFE_ARRAY_ACCESS(m_contextRegisters, flagsOffset, MAX_CONTEXT_REGS, "GetVertexBufferState flagsValue");
      // Skip entries that appear to be uninitialized (all zeros)
      if (descValue == 0 && strideValue == 0 && flagsValue == 0) {
        continue;
      }

      VertexBindingDesc binding;
      binding.binding = i;
      binding.stride = strideValue;
      binding.perInstance = (flagsValue & 0x1) != 0;
      binding.instanceStep = (flagsValue >> 1) & 0xFF; // Assuming 8-bit instance step

      bindings.push_back(binding);
    }

    m_stats.accessCount += maxBindings * 3; // Accessed 3 registers per binding

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetVertexInputBindings: found {} active bindings (max={}), latency={}us",
                  bindings.size(), maxBindings, latency);
    return bindings;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GetVertexInputBindings failed: {}", e.what());
    throw GNMException("GetVertexInputBindings failed: " + std::string(e.what()));
  }
}

std::vector<VertexAttributeDesc>
GNMRegisterState::GetVertexInputAttributes(uint32_t maxAttributes) const {
  auto start = std::chrono::steady_clock::now();
  // Acquire a shared lock for read-only access to multiple registers
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    if (maxAttributes > 32) { // Reasonable upper limit
      spdlog::warn("GetVertexInputAttributes: maxAttributes ({}) clamped to 32", maxAttributes);
      maxAttributes = 32;
    }

    std::vector<VertexAttributeDesc> attributes;
    attributes.reserve(maxAttributes);

    // Assuming vertex attribute descriptors start at context register 0x300
    constexpr uint32_t VTX_ATTR_FORMAT_BASE_OFFSET = 0x300;
    constexpr uint32_t VTX_ATTR_BINDING_BASE_OFFSET = 0x310;
    constexpr uint32_t VTX_ATTR_OFFSET_BASE_OFFSET = 0x320;
    for (uint32_t i = 0; i < maxAttributes; i++) {
      // Check if the attribute descriptor offset is within bounds
      uint32_t formatOffset = VTX_ATTR_FORMAT_BASE_OFFSET + i;
      uint32_t bindingOffset = VTX_ATTR_BINDING_BASE_OFFSET + i;
      uint32_t offsetOffset = VTX_ATTR_OFFSET_BASE_OFFSET + i;
      if (formatOffset >= MAX_CONTEXT_REGS || bindingOffset >= MAX_CONTEXT_REGS || offsetOffset >= MAX_CONTEXT_REGS) {
        spdlog::warn("GetVertexInputAttributes: Attribute {} offset out of bounds, stopping enumeration", i);
        break;
      }

      uint32_t formatValue = SAFE_ARRAY_ACCESS(m_contextRegisters, formatOffset, MAX_CONTEXT_REGS, "GetVertexAttributeState formatValue");
      uint32_t bindingValue = SAFE_ARRAY_ACCESS(m_contextRegisters, bindingOffset, MAX_CONTEXT_REGS, "GetVertexAttributeState bindingValue");
      uint32_t offsetValue = SAFE_ARRAY_ACCESS(m_contextRegisters, offsetOffset, MAX_CONTEXT_REGS, "GetVertexAttributeState offsetValue");
      // Skip entries that appear to be uninitialized
      if (formatValue == 0 && bindingValue == 0 && offsetValue == 0) {
        continue;
      }

      VertexAttributeDesc attribute;
      attribute.location = i;
      attribute.binding = bindingValue & 0xFF; // Assuming 8-bit binding index
      attribute.format = formatValue;
      attribute.offset = offsetValue;
      // Derive component count from format (simplified mapping)
      // This would normally require a proper format enum/lookup table
      switch (formatValue & 0xFF) {
      case 0x01: attribute.components = 1; break; // R32_FLOAT
      case 0x02: attribute.components = 2; break; // R32G32_FLOAT
      case 0x03: attribute.components = 3; break; // R32G32B32_FLOAT
      case 0x04: attribute.components = 4; break; // R32G32B32A32_FLOAT
      default: attribute.components = 4; break; // Default to 4 components
      }

      attributes.push_back(attribute);
    }

    m_stats.accessCount += maxAttributes * 3; // Accessed 3 registers per attribute

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetVertexInputAttributes: found {} active attributes (max={}), latency={}us",
                  attributes.size(), maxAttributes, latency);
    return attributes;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GetVertexInputAttributes failed: {}", e.what());
    throw GNMException("GetVertexInputAttributes failed: " + std::string(e.what()));
  }
}

uint64_t GNMRegisterState::GetResourceStateHash(bool includeShaderState,
                                                  bool includeRenderTargets,
                                                  bool includeInputState) const {
  auto start = std::chrono::steady_clock::now();
  // Acquire a shared lock for read-only access to multiple registers
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    uint64_t hash = 0;
    // Simple hash combine function (similar to boost::hash_combine)
    auto hash_combine = [&hash](uint64_t value) {
      hash ^= value + 0x9e3779b9 + (hash << 6) + (hash >> 2);
    };

    if (includeShaderState) {
      // Hash shader base addresses and key shader configuration registers
      for (uint32_t stage = 0; stage < SHADER_STAGES; stage++) {
        hash_combine(SAFE_ARRAY_ACCESS(m_shaderRegisters[stage], 0x0, MAX_SHADER_REGS, "ComputeStateHash SHADER_BASE_ADDR")); // SHADER_BASE_ADDR
        hash_combine(SAFE_ARRAY_ACCESS(m_shaderRegisters[stage], 0x1, MAX_SHADER_REGS, "ComputeStateHash SHADER_SIZE")); // SHADER_SIZE
        hash_combine(SAFE_ARRAY_ACCESS(m_shaderRegisters[stage], 0x2, MAX_SHADER_REGS, "ComputeStateHash SHADER_ENTRY_POINT")); // SHADER_ENTRY_POINT
      }
    }

    if (includeRenderTargets) {
      // Hash render target addresses and viewport parameters
      for (uint32_t i = 0; i < 8; i++) {
        uint32_t rtOffset = 0x100 + i; // RENDER_TARGET_N_ADDR
        if (rtOffset < MAX_CONTEXT_REGS) {
          hash_combine(SAFE_ARRAY_ACCESS(m_contextRegisters, rtOffset, MAX_CONTEXT_REGS, "ComputeStateHash render target"));
        }
      }

      // Hash viewport registers
      for (uint32_t i = 0x80; i <= 0x85; i++) { // VIEWPORT_* registers
        hash_combine(SAFE_ARRAY_ACCESS(m_contextRegisters, i, MAX_CONTEXT_REGS, "ComputeStateHash viewport"));
      }
    }

    if (includeInputState) {
      // Hash vertex input configuration
      for (uint32_t i = 0; i < 16; i++) { // Up to 16 bindings/attributes
        uint32_t bindingOffset = 0x280 + i;
        uint32_t strideOffset = 0x290 + i;
        uint32_t flagsOffset = 0x2A0 + i;
        uint32_t formatOffset = 0x300 + i;
        uint32_t attrBindingOffset = 0x310 + i;
        uint32_t attrOffsetOffset = 0x320 + i;

        if (bindingOffset < MAX_CONTEXT_REGS) hash_combine(SAFE_ARRAY_ACCESS(m_contextRegisters, bindingOffset, MAX_CONTEXT_REGS, "ComputeStateHash bindingOffset"));
        if (strideOffset < MAX_CONTEXT_REGS) hash_combine(SAFE_ARRAY_ACCESS(m_contextRegisters, strideOffset, MAX_CONTEXT_REGS, "ComputeStateHash strideOffset"));
        if (flagsOffset < MAX_CONTEXT_REGS) hash_combine(SAFE_ARRAY_ACCESS(m_contextRegisters, flagsOffset, MAX_CONTEXT_REGS, "ComputeStateHash flagsOffset"));
        if (formatOffset < MAX_CONTEXT_REGS) hash_combine(SAFE_ARRAY_ACCESS(m_contextRegisters, formatOffset, MAX_CONTEXT_REGS, "ComputeStateHash formatOffset"));
        if (attrBindingOffset < MAX_CONTEXT_REGS) hash_combine(SAFE_ARRAY_ACCESS(m_contextRegisters, attrBindingOffset, MAX_CONTEXT_REGS, "ComputeStateHash attrBindingOffset"));
        if (attrOffsetOffset < MAX_CONTEXT_REGS) hash_combine(SAFE_ARRAY_ACCESS(m_contextRegisters, attrOffsetOffset, MAX_CONTEXT_REGS, "ComputeStateHash attrOffsetOffset"));
      }

      // Hash tessellation state
      for (uint32_t i = 0x200; i <= 0x205; i++) { // TESS_* registers
        hash_combine(SAFE_ARRAY_ACCESS(m_contextRegisters, i, MAX_CONTEXT_REGS, "ComputeStateHash tessellation"));
      }
    }

    m_stats.accessCount++; // Count this as one complex access operation

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetResourceStateHash: hash=0x{:x}, includeShader={}, includeRT={}, "
                  "includeInput={}, latency={}us",
                  hash, includeShaderState, includeRenderTargets, includeInputState, latency);
    return hash;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("GetResourceStateHash failed: {}", e.what());
    throw GNMException("GetResourceStateHash failed: " + std::string(e.what()));
  }
}

} // namespace ps4