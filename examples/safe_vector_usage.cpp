#include "debug/vector_debug.h"
#include "../gui/imgui_safe.h"
#include <map>

void ExampleSafeVectorUsage() {
    std::vector<int> data = {1, 2, 3, 4, 5};

    // Always validate before operations
    VALIDATE_VECTOR(data, "ExampleSafeVectorUsage");

    // Safe access with context
    for (size_t i = 0; i < data.size(); ++i) {
        int value = SAFE_VECTOR_ACCESS(data, i, "ExampleSafeVectorUsage loop");
        spdlog::info("Value at index {}: {}", i, value);
    }

    // Critical access for performance-sensitive code
    if (!data.empty()) {
        int first = CRITICAL_VECTOR_ACCESS(data, 0, "ExampleSafeVectorUsage first element");
        spdlog::info("First element: {}", first);
    }

    // Safe ImVec2 usage
    ImVec2 vec(1.0f, 2.0f);
    float x = SAFE_IMVEC2_ACCESS(vec, 0, "ExampleSafeVectorUsage ImVec2 x");
    float y = SAFE_IMVEC2_ACCESS(vec, 1, "ExampleSafeVectorUsage ImVec2 y");

    // Safe map access
    std::map<int, std::string> map_data = {{1, "one"}, {2, "two"}};
    auto it = SafeMapAccess(map_data, 1, "ExampleSafeVectorUsage map access");
    if (it != map_data.end()) {
        spdlog::info("Found value: {}", it->second);
    }
}
