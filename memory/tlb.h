// Copyright 2025 <Copyright Owner>

#pragma once

#include <array>
#include <cstdint>
#include <iostream>
#include <list>
#include <shared_mutex>
#include <unordered_map>
#include <vector>

namespace ps4 {

struct TLBException : std::runtime_error {
  explicit TLBException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief Translation Lookaside Buffer (TLB) for virtual-to-physical address
 * translation caching.
 */
class TLB {
public:
  static constexpr size_t DEFAULT_PAGE_SIZE = 4096; ///< Default page size (4KB)
  static constexpr size_t DEFAULT_NUM_SETS = 16;    ///< Number of sets
  static constexpr size_t DEFAULT_WAYS = 4; ///< Associativity (ways per set)
  static constexpr size_t MAX_ENTRIES =
      DEFAULT_NUM_SETS * DEFAULT_WAYS; ///< Maximum TLB entries
  static constexpr size_t PAGE_SIZE = DEFAULT_PAGE_SIZE; ///< Page size constant

  /**
   * @brief Constructs the TLB with default settings.
   */
  TLB();

  /**
   * @brief Adds a TLB entry.
   * @param virtualAddr Virtual address (page-aligned).
   * @param physicalAddr Physical address.
   * @param flags Protection flags.
   * @param pageSize Page size (e.g., 4KB, 2MB, 1GB).
   * @param processId Process ID for tagged entries.
   * @throws TLBException on invalid page size or address.
   */
  void AddEntry(uint64_t virtualAddr, uint64_t physicalAddr, uint32_t flags,
                size_t pageSize = DEFAULT_PAGE_SIZE, uint64_t processId = 0);

  /**
   * @brief Looks up a virtual address.
   * @param virtualAddr Virtual address.
   * @param physicalAddr Output physical address.
   * @param pageSize Output page size.
   * @param processId Process ID for tagged lookup.
   * @return True if found, false otherwise.
   */
  bool Lookup(uint64_t virtualAddr, uint64_t &physicalAddr, size_t &pageSize,
              uint64_t processId = 0) const;

  /**
   * @brief Invalidates a TLB entry.
   * @param virtualAddr Virtual address.
   * @param processId Process ID for tagged invalidation.
   */
  void Invalidate(uint64_t virtualAddr, uint64_t processId = 0);

  /**
   * @brief Clears all TLB entries.
   */
  void Clear();

  /**
   * @brief Inserts a TLB entry (simplified interface).
   * @param virtualAddr Virtual address.
   * @param physAddr Physical address.
   * @param pageSize Page size.
   * @param processId Process ID for tagged entries.
   */
  void Insert(uint64_t virtualAddr, uint64_t physAddr,
              size_t pageSize = DEFAULT_PAGE_SIZE, uint64_t processId = 0);

  /**
   * @brief Cleans up stale TLB entries.
   */
  void CleanupStaleEntries();

  /**
   * @brief TLB statistics.
   */
  struct Stats {
    uint64_t hits = 0;           ///< Number of TLB hits
    uint64_t misses = 0;         ///< Number of TLB misses
    uint64_t evictions = 0;      ///< Number of TLB evictions
    uint64_t inserts = 0;        ///< Number of TLB insertions
    uint64_t totalLatencyUs = 0; ///< Total lookup latency in microseconds
    uint64_t pageSize4KB = 0;    ///< Number of 4KB page entries
    uint64_t pageSize2MB = 0;    ///< Number of 2MB page entries
    uint64_t pageSize1GB = 0;    ///< Number of 1GB page entries
  };

  /**
   * @brief Retrieves TLB statistics.
   * @return Current statistics.
   */
  Stats GetStats() const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_stats;
  }

  /**
   * @brief Gets the number of TLB hits.
   * @return Number of hits.
   */
  uint64_t GetHits() const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_stats.hits;
  }

  /**
   * @brief Gets the number of TLB misses.
   * @return Number of misses.
   */
  uint64_t GetMisses() const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_stats.misses;
  }

  /**
   * @brief Saves the TLB state to a stream.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the TLB state from a stream.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

private:
  /**
   * @brief TLB entry structure.
   */
  struct Entry {
    uint64_t virtualAddr = 0;            // Page-aligned virtual address
    uint64_t physicalAddr = 0;           ///< Physical address
    uint32_t flags = 0;                  ///< Protection flags
    size_t pageSize = DEFAULT_PAGE_SIZE; ///< Page size
    uint64_t processId = 0;              ///< Process ID for tagged entries
  };

  /**
   * @brief Gets the page mask for a given page size.
   * @param pageSize Page size.
   * @return Page mask.
   * @throws TLBException on invalid page size.
   */
  uint64_t GetPageMask(size_t pageSize) const;

  /**
   * @brief Evicts an entry if the TLB is full.
   */
  void EvictIfNeeded();

  /**
   * @brief Validates the internal consistency of TLB data structures.
   * @throws TLBException if inconsistencies are found.
   */
  void ValidateInternalState() const;

  std::vector<std::array<Entry, DEFAULT_WAYS>> sets; ///< Set-associative cache
  size_t numSets = DEFAULT_NUM_SETS;
  size_t ways = DEFAULT_WAYS;
  mutable std::shared_mutex m_mutex;         ///< Thread safety mutex
  mutable Stats m_stats;                     ///< TLB statistics
  std::vector<std::list<size_t>> lruIndices; ///< LRU indices per set

  // Additional members for alternate implementation
  std::unordered_map<uint64_t, Entry> m_entries; ///< TLB entries map
  std::list<uint64_t> m_lruList;                 ///< LRU list for eviction
};

} // namespace ps4