2018-08-07  <PERSON> <ross dot johnson at homemail dot com dot au>

	* GNUmakefile.in (DLL_VER): rename as PTW32_VER.
	* Makefile (DLL_VER): Likewise.
	* Bmakefile (DLL_VER): Likewise; does anyone use this anymore?
	* Makefile: Variable renaming: e.g. VCLIB to VCIMP for DLL import
	library pthreadVC2.lib and VCLIB now holds static library name
	libpthreadVC2.lib, and similar for the other cleanup method versions.

2018-07-22  <PERSON> <markpizzolato dash pthreads dash win32 at subscriptions dot pizzolato dot net>

	* Makefile: Change the various static test runs to actually
	compile with /MT or /MTd, i.e. No mixed /MT and /MD. Static
	builds no longer create a separate pthreads*.lib static library
	because errno does not work across that linkage.
	* sequence1.c: Use more reasonable number of threads to avoid
	resource limits.

2016-12-25  <PERSON> <ross dot johnson at homemail dot com dot au>

	* Change all license notices to the Apache License 2.0

2016-12-21  <PERSON> <ross dot johnson at homemail dot com dot au>

	* mutex6.c: fix random failures by using a polling loop to replace
	a single Sleep().
	* mutex6n.c: Likewise.
	* mutex6s.c: Likewise.
	* mutex7.c: Likewise.
	* mutex7n.c: Likewise.
	* mutex8.c: Likewise
	* mutex8n.c: Likewise.
	* semaphore5.c: don't fail on expected sem_destroy EBUSY.

2016-12-20  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* all (PTW32_*): rename to __PTW32_*.
	(ptw32_*): rename to __ptw32_*.
	(PtW32*): rename to __PtW32*.
	* GNUmakefile: removed; must now configure from GNUmakefile.in.

2016-12-18  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* test.c  (__PTW32_TEST_SNEAK_PEEK): #define this to prevent some
	tests from failing (specifically "make GCX-small-static") with
	undefined __ptw32_autostatic_anchor. Checked in ../implement.h.
	* GNUMakfile.in: Rename testsuite log to test-specific name and
	do not remove.
	* GNUMakfile.in: Add realclean target

2016-04-30  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* semaphore4t.c: No longer need to include ptw32_timespec.c and the
	defines that tried to make it work (but failed). There is now an
	exported routine that this test uses that can also be used by
	applications. It's a general routine that keeps all the platform
	conditional stuff inside the library.
	* Makefile: Remove _ptw32.h copied from parent directory.

2016-03-31  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* Makefile (_ptw32.h): Copy header to tests directory required to
	build tests, and apps.

2016-03-31  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* test.h: source errno.h

2016-03-31  Keith Marshall <MinGW32 project>

	* test.h: Patch for MinGW32 autoconf.
	* GNUmakefile.in: New for autoconf.

2016-03-28  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* condvar2.c: Use platform-aware pthread_win32_getabstime_np.
	* condvar2_1.c: Likewise.
	* condvar3.c: Likewise.
	* condvar3_1.c: Likewise.
	* condvar3_2.c: Likewise.
	* condvar3_3.c: Likewise.
	* condvar4.c: Likewise.
	* condvar5.c: Likewise.
	* condvar6.c: Likewise.
	* condvar7.c: Likewise.
	* condvar8.c: Likewise.
	* condvar9.c: Likewise.
	* join4.c: Likewise.
	* mutex8.c: Likewise.
	* mutex8e.c: Likewise.
	* mutex8n.c: Likewise.
	* mutex8r.c: Likewise.
	* reinit1.c: Likewise.
	* rwlock2_t.c: Likewise.
	* rwlock3_t.c: Likewise.
	* rwlock4_t.c: Likewise.
	* rwlock5_t.c: Likewise.
	* rwlock6_t.c: Likewise.
	* rwlock6_t2.c: Likewise.
	* semaphore4t.c: Likewise.
	* stress1.c: Likewise.
	* reinit1.c: Remove unused variable declarations.

2015-11-01	Mark Smith <masmith at fb dot com>

	* semaphore4t.c: Enhanced and additional testing of sub-millisecond
	timeouts.

2014-07-22  Scott Libert  <scott underscore libert at dell dot com>

	* semaphore3.c: Wait for threads to complete before exiting main().
	This test would sometimes fail because, behaviourally, it was not
	standard-compliant.

2013-12-10  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* cancel9.c (bytes, dwEvent): Removed to eliminate warning "set but
	not used".

2013-11-13  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* reinit1.c: New test - reinitialising the library.
	* common.mk: Add new test.
	* reorder.mk: Likewise.

2013-07-23  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* affinity*.c: Skipped under WINCE.

2013-07-02  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* threestage.c: New test/example code; not written specifically as
	a test; independently written and essentially unmodified pthreads
	example.

2013-06-19  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* exit6.c: New test (added some time ago but not committed).
	* name_np1.c: New test for pthread_[sg]etname_np().
	* name_np2.c: New test for pthread_attr_[sg]etname_np().
	* common.mk: Add new tests.
	* reorder.mk: Likewise.

2013-06-06  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* affinity6.c: New test for pthread_attr_[gs]etaffinity_np()
	* common.mk: Add new tests.
	* reorder.mk: Likewise.

2012-10-24  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* tsd3.c: New regression test for Stephane Clairet bug fix/report;
	this test confirms that keys are deleted correctly prior to threads
	exiting.
	* tsd2.c: Modified comments and indentation.
	* tsd1.c: Likewise.
	* common.mk (tsd3): New test added.
	* reorder.mk (tsd3): Likewise.

2012-10-16  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* GNUmakefile (EXTRAVERSION): Naming option for libraries to be
	consistent with MSVS Makefile; no longer automatically based on
	ARCH setting.

2012-10-10  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* cancel1.c: Fix comment typo.
	* Makefile: Adding functionality to support rapid turnaround of
	individual or small sets of tests; removed loadfree test.
	* GNUmakefile: Likewise.
	* Bmakefile: Removed loadfree test.
	* Wmakefile: Likewise.
	* common.mk: New makefile include file; split out common macros.
	* runorder.mk: New makefile include file; split out run order rules
	to allow selective inclusion.
	* loadfree.c: Removed from suite; not required; unnecessarily
	complicates the makefiles.

2012-10-04  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* join4.c: Modified for pthread_tryjoin_np tests.

2012-09-29  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* Makefile: Align target names with build makefile target names
	following changes there; remove the separate static tests list.
	* GNUmakefile: Likewise; consolidate rules.

2012-09-25  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* context1.c (anotherEnding): Should exit the thread, not return.

2012-09-23  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* Makefile: Add secondary target names to existing test targets so
	we can output how the library was built, e.g. "Running VC-inlined test".
	This is useful when making "all-tests" from the library build Makefile.
	* GNUmakefile: Similarly.

2012-09-22  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* GNUmakefile: Do what Makefile does and print the make target
	when running each test.

2012-09-22  Daniel Richard. G <danielg at teragram dot com>

	* Makefile: This reverts an earlier change of mine. In some
	versions of nmake, $(MAKE) is a full path, which utterly futzes
	up the "make help" output; regularized the test targets, so we
	have e.g. VSE-static now; regularized the parentheticals;
	removed a spurious use of $(DLL_VER); new test targets: VCX-debug
	{VSE,VCX}-static{,-debug}; fixed the {VC,VCE}-static-debug targets.
	* exception3.c: Narrowly exclude this test from the known-bad VS2005
	configuration (By the way: I downloaded and tested with what is
	presumably the most up-to-date MSVCR80.DLL. Same deal.); use
	pthread_win32_set_terminate_np() when appropriate, and verify that
	it has an effect different from calling set_terminate(); no need for
	that assert(); it's overly pedantic.
	* exception3_0.c: Same VS2005 exclusion.
	* once3.c: Put in a note so that users know what to do if the test
	hangs or exits abnormally (I figured this is better than narrowly
	disabling pthread_cancel() on MSVC6, since cancellation does seem to
	work correctly with /EHa, and we don't have any way of telling at
	compile time whether /EHs or /EHa is in use).
	* once4.c: Likewise.
	* semaphore1.c: s/PTW32_BROKEN_ERRNO/PTW32_USES_SEPARATE_CRT/

2012-09-22  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* pthread_win32_attach_detach_np.c (pthread_win32_detach_thread_np):
	Check for NULL __ptw32_selfThreadKey before call to TlsSetKey(). Need
	to find where this is being set to NULL before this point. Was
	consistently failing tests/seamphore3.c in all GC static builds and
	never seen in GC DLL builds. May also be responsible for
	inconsistent VCE fails on that test, although the fail mode was
	different - the latter hangs the test while the former segfaults.

2012-09-22  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* GNUmakefile (GC-static-debug): New make target.

2012-09-21  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* affinity1.c: New test for new process CPU affinity routines.
	* affinity2.c: New test for new process CPU affinity routines.
	* affinity3.c: New test for new process and thread CPU affinity routines.
	* affinity4.c: New test for new process and thread CPU affinity routines.
	* affinity5.c: New test for new process and thread CPU affinity routines.
	* Makefile: Add new tests.
	* GNUmakfile: Likewise.
	* Bmakfile: Likewise.
	* Wmakfile: Likewise.

2012-09-09  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* exception3.c: Rewrite to fix strategy.
	* exception3_0.c: New test.
	* exception2.c: Reduce sleep time to speed up test.
	* Makefile (exception3_0.*): Add new test.
	* GNUmakefile (exception3_0.*): Add new test.
	* Bmakefile (exception3_0.*): Add new test.
	* Wmakefile (exception3_0.*): Add new test.

2012-09-05  Daniel Richard. G <danielg at teragram dot com>

	* exception2.c: Fix result codes to avoid false negative failures.
	* cancel9.c: cosmetic change.
	* GNUmakefile (clean): delete manifest files.
	* Bmakefile: Likewise.
	* Wmakefile: Likewise.
	* Makefile: Likewise.

2012-09-04  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* Makefile (VCEFLAGS): Changed from /EHsc to /EHs which was causing
	tests/once3.c to hang, suspect exceptions not being thrown from
	extern C routines.
	* cancel2.c: Rewrite to use a barrier for thread control; remove
	asyncronous cancel type which was a massive bug in the test strategy
	and cusing SEGFAULTs (access violations).
	* eyal1.c: Remove unused variable 'i' to clear compiler warning.
	* context1.c: Remove call to pthread_exit() in alternate ending to
	quell error in GCE tests see when running x86 build on x64.

2012-09-03  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* Makefile (VCE-static): Add VC++ static build target
	(VCE-static-debug): Likewise.

2012-08-29  Daniel Richard. G <danielg at teragram dot com>

	* mutex6.c: Removed pointless exit(0) and never-reached comment.
	* mutex6r.c: Likewise.
	* mutex6rs.c: Likewise.
	* mutex6s.c: Likewise.
	* mutex6e.c: Likewise.
	* mutex6es.c: Likewise.
	* mutex7.c: Likewise.
	* mutex7e.c: Likewise.
	* mutex7n.c: Likewise.
	* mutex7r.c: Likewise.
	* mutex6n.c: Likewise; ensure operator precedence.
	* once2.c (sharedInt_t): replace static initialization of struct with memset.
	* once3.c: Likewise.
	* once4.c: Likewise.
	* cleanup0.c: Likewise.
	* cleanup1.c: Likewise.
	* cleanup2.c: Likewise.
	* cleanup3.c: Likewise.
	* openmp1.c: Fix casts.
	* exit1.c: Remove assert statement that is never reached and return 1.
	* exception2.c: Prevent displaying modal error dialog.

2012-08-19  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* join4.c: Test for new pthread_timedjoin_np routine
	* Makefile (join4): Added.
	* GNUmakefile (join4): Added.
	* Bmakefile (join4): Added.
	* Wmakefile (join4): Added.
	* test.h (sys/timeb.h): Add #include.
	* mutex8.c (sys/timeb.h): Remove #include.
	* mutex8e.c (sys/timeb.h): Remove #include.
	* mutex8n.c (sys/timeb.h): Remove #include.
	* mutex8r.c (sys/timeb.h): Remove #include.
	* benchtest1.c (sys/timeb.h): Remove #include.
	* benchtest2.c (sys/timeb.h): Remove #include.
	* benchtest3.c (sys/timeb.h): Remove #include.
	* benchtest4.c (sys/timeb.h): Remove #include.
	* benchtest5.c (sys/timeb.h): Remove #include.

2012-08-11  Daniel Richard. G <danielg at teragram dot com>

	* Makefile: Various improvements.
	* GNUmakefile: Likewise.

2011-07-20  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* cancel2.c (PTHREAD_CANCELED): Fix cast warning when compiling with g++.
	* cleanup0.c (PTHREAD_CANCELED): Likewise.
	* cleanup1.c (PTHREAD_CANCELED): Likewise.

2012-07-19  Daniel Richard. G <danielg at teragram dot com>

	* Makefile: Various fixes.
	* GNUmakefile: Likewise.

2011-07-03  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* create3.c: Removed; testing a condition that is not in the library's
	scope and was more trouble than it was worth.
	* cancel2.c: Ensure this test only runs for Structured or C++ EH.
	* exit2.c: Shorten Sleep() time.
	* exit3.c: Likewise.
	* cancel1.c: Likewise.
	* cancel3.c: Likewise.
	* exception3.c: Likewise; make terminate routine consistent for all
	build environments.

2011-07-02  Ross Johnson <ross dot johnson at homemail dot com dot au>

	* spin3.c: Unlock the unlocked spinlock now returns success.
	* rwlock3.c: Join the thread to ensure it's completed.
	* rwlock4.c: Likewise.
	* rwlock5.c: Likewise.
	* Makefile: Adjust prerequisites.
	* GNUmakefile: Likewise.
	* Bmakefile: Likewise.
	* Wmakefile: Likewise.

2011-07-02 Daniel Richard G. <skunk at iskunk dot org>

	* *.[ch]: Cleanups around timeb struct, mainly centralising
	macro definitions in test.h.
	* Makefile: Fix annoying nmake warning.

2011-06-30  Ross Johnson <ross.johnson at homemail.com.au>

	* sequence1.c: Fix loop overrun.

2011-05-11  Ross Johnson <ross.johnson at homemail.com.au>

	* GNUmakefile (GCE-debug): New target; expects pthreadGCE2d.dll.
	
2011-05-05  Ross Johnson <ross.johnson at homemail.com.au>

	* openmp1.c: Add missing test; used to comfirm that this
	library works with libgomp; if this test produces a segfault
	then try upgrading your version of libgomp/gcc; gcc version
	4.5.2 passes this test.

2011-03-26  Ross Johnson <ross.johnson at homemail.com.au>

	* sequence1.c: New test for new pthread_getsequence_np().

2011-03-24  Ross Johnson <ross.johnson at homemail.com.au>

	* mutex*.c: Include tests for robust mutexes wherever
	appropriate.
	* benchtest*.c: Include comparisons for robust mutexes.
	* robust1.c: New test for robust mutex handling.
	* robust2.c: Likewise.
	* robust3.c: Likewise.
	* robust4.c: Likewise.
	* robust5.c: Likewise.
	* GNUmakefile: Include new tests.
	* Makefile: Likewise.
	* Bmakefile: Likewise (not tested).
	* Wmakefile: Likewise (not tested).

2011-03-06  Ross Johnson <ross.johnson at homemail.com.au>

	* several (MINGW64): Cast and call fixups for 64 bit compatibility;
	clean build via x86_64-w64-mingw32 cross toolchain on Linux
	i686 targeting x86_64 win64.

2011-03-04  Ross Johnson <Ross dot Johnson at homemail dot com dot au>

	* condvar3_2.c: abstime.tv_sec operation warning fixed.
	* several: Use correct casting on pthread_join result arg
	and associated declaration and usage; assumed that 64 bit
	gcc gave some warnings for it.

2011-02-28  Ross Johnson <Ross dot Johnson at homemail dot com dot au>

	* test.h: Define FTIME to be _ftime64_s or _ftime64 or _ftime
	in that order of preference where supported.
	* several: Replace calls to _ftime with the FTIME macro.

2010-06-19  Ross Johnson <Ross dot Johnson at homemail dot com dot au>

	* Makefile (STATICRESULTS): Add all tests into suite for static
	library.
	* GNUmakefile (STATICTESTS): Likewise, except for openmp1.c which
	has a DLL dependency.

2010-02-04  Ross Johnson <Ross dot Johnson at homemail dot com dot au>

	* openmp1.c: New; for libgomp compatibility (OpenMP).
	* barrier5.c: Rewrite after changes to barriers.
	* barrier6.c: New.
	* benchtest6.c: New; timing barriers.
	* GNUMakefile: Update for new tests.
	* Makefile: Ditto.
	* BMakefile: Ditto.
	* once3.c: Improve cancellation testing.
	* stress1.c: Fix comment.

2007-01-04  Ross Johnson <Ross dot Johnson at homemail dot com dot au>

        * context1.c: Include context.h from library sources and remove
        x86 dependence in main().

2005-06-12  Ross Johnson  <<EMAIL>>

	* stress1.c (millisecondsFromNow): Remove limit 0 <= millisecs < 1000;
	now works for -INT_MAX <= millisecs <= INT_MAX; not needed for
	stress1.c but should be general anyway.

2005-05-18  Ross Johnson  <<EMAIL>>

	* reuse2.c (main): Must use a read with memory barrier semantics
	when polling 'done' to force the cache into coherence on MP systems.

2005-05-15  Ross Johnson  <<EMAIL>>

	* detach1.c: New test.
	* join1.c: Reduce sleep times.
	* join0.c: Remove MSVCRT conditional compile - join should always
	return the thread exit code.
	* join1.c: Likewise.
	* join2.c: Likewise.
	* join3.c: Likewise.

2005-04-18  Ross Johnson  <<EMAIL>>

	* condvar3.c: Remove locks from around signalling calls - should not
	be required for normal operation and only serve to mask deficiencies;
	ensure that CV destruction is not premature after removing guards.
	* condvar3_1.c: Likewise.
	* condvar3_2.c: Likewise.
	* condvar3_3.c: Likewise.
	* condvar4.c: Likewise.
	* condvar5.c: Likewise.
	* condvar6.c: Likewise.
	* condvar7.c: Likewise.
	* condvar8.c: Likewise.
	* condvar9.c: Likewise.

2005-04-11  Ross Johnson  <<EMAIL>>

        * once4.c: New test; tries to test priority adjustments
        in pthread_once(); set priority class to realtime so that
        any failures can be seen.

2005-04-06  Ross Johnson  <<EMAIL>>

	* cleanup0.c: Fix unguarded global variable accesses.
	* cleanup1.c: Likewise.
	* cleanup2.c: Likewise.
	* cleanup3.c: Likewise.
	* once2.c: Likewise.
	* once3.c: Likewise.

2005-04-01  Ross Johnson  <<EMAIL>>

	* GNUmakefile: Add target to test linking static link library.
	* Makefile: Likewise.
	* self1.c: Run process attach/detach routines when static linked.

2005-03-16  Ross Johnson  <<EMAIL>>

	* mutex5.c: Prevent optimiser from removing asserts.

2005-03-12  Ross Johnson  <<EMAIL>>

	* once3.c: New test.

2005-03-08  Ross Johnson  <<EMAIL>>

        * once2.c: New test.

2004-11-19  Ross Johnson  <<EMAIL>>

	* Bmakefile: New makefile for Borland.
	* Makefile (DLL_VER): Added.
	* GNUmakefile (DLL_VER): Added.
	* Wmakefile (DLL_VER): Added.

2004-10-29  Ross Johnson  <<EMAIL>>

	* semaphore4.c: New test.
	* semaphore4t.c: New test.
	* Debug.dsp (et al): Created MSVC Workspace project to aid debugging.
	* All: Many tests have been modified to work with the new pthread
	ID type; some other corrections were made after some library
	functions were semantically strengthened. For example,
	pthread_cond_destroy() no longer destroys a busy CV, which
	required minor redesigns of some tests, including some where
	the mutex associated with the CV was not locked during
	signaling and broadcasting.

2004-10-23  Ross Johnson  <<EMAIL>>

	* condvar3.c: Fixed mutex operations that were incorrectly
	placed in relation to their condition variable operations.
	The error became evident after sem_destroy() was rewritten
	and conditions for destroing the semaphore were tightened.
	As a result, pthread_cond_destroy() was not able to
	destroy the cv queueing sempahore.
	* condvar3_1.c: Likewise.
	* condvar3_2.c: Likewise.
	* condvar4.c: Likewise.
	* condvar5.c: Likewise.
	* condvar6.c: Likewise.
	* condvar7.c: Likewise.
	* condvar8.c: Likewise.
	* condvar9.c: Likewise.

2004-10-19  Ross Johnson  <<EMAIL>>

	* semaphore3.c: New test.

2004-10-14  Ross Johnson  <<EMAIL>>

	* rwlock7.c (main): Tidy up statistics reporting; randomise
	update accesses.
	* rwlock8.c: New test.

2004-09-08  Alexandre Girao  <<EMAIL>>

	* cancel7.c (main): Win98 wants a valid (non-NULL) location
	for the last arg of _beginthreadex().
	* cancel8.c (main): Likewise.
	* exit4.c (main): Likewise.
	* exit5.c (main): Likewise.

2004-08-26  Ross Johnson  <<EMAIL>>

	* create3.c: New test.

2004-06-21  Ross Johnson  <<EMAIL>>

	* mutex2r.c: New test.
	* mutex2e.c: New test.
	* mutex3r.c: New test.
	* mutex3e.c: New test.
	* mutex6s.c: New test.
	* mutex6rs.c: New test.
	* mutex6es.c: New test.

2004-05-21  Ross Johnson  <<EMAIL>>

	* join3.c: New test.

2004-05-16  Ross Johnson  <<EMAIL>>

	* condvar2.c (WIN32_WINNT): Define to avoid redefinition warning
	from inclusion of implement.h.
	* convar2_1.c: Likewise.
	* condvar3_1.c: Likewise.
	* condvar3_2.c: Likewise.
	* context1.c: Likewise.
	* sizes.c: Likewise.
	* Makefile: Don't define _WIN32_WINNT on compiler command line.
	* GNUmakefile: Likewise.
	* priority1.c (main): Add column to output for actual win32
	priority.

2004-05-16  Ross Johnson  <<EMAIL>>

	* cancel9.c: New test.
	* cancel3.c: Remove inappropriate conditional compilation;
	GNU C version of test suite no longer quietly skips this test.
	* cancel5.c: Likewise.
	* GNUmakefile: Can now build individual test app using default
	C version of library using 'make clean testname.c'.
	* Makefile: Likewise for VC using 'nmake clean test testname.c'.

2003-10-14  Ross Johnson  <<EMAIL>>

	* Wmakefile: New makefile for Watcom testing.

2003-09-18  Ross Johnson  <<EMAIL>>

	* benchtest.h: Move old mutex code into benchlib.c.
	* benchlib.c: New statically linked module to ensure that
	bench apps don't inline the code and therefore have an unfair
	advantage over the pthreads lib routines. Made little or no
	difference.
	* benchtest1.c: Minor change to avoid compiler warnings.
	* benchtest5.c: Likewise.
	* benchtest2.c: Fix misinformation in output report.
	* README.BENCH: Add comments on results.

2003-09-14  Ross Johnson  <<EMAIL>>

	* priority1.c: Reworked to comply with modified priority
	management and provide additional output.
	* priority2.c: Likewise.
	* inherit1.c: Likewise.

2003-09-03  Ross Johnson  <<EMAIL>>

	* exit4.c: New test.
	* exit5.c: New test.
	* cancel7.c: New test.
	* cancel8.c: New test.

2003-08-13  Ross Johnson  <<EMAIL>>

	* reuse1.c: New test.
	* reuse1.c: New test.
	* valid1.c: New test.
	* valid2.c: New test.
	* kill1.c: New test.
 	* create2.c: Now included in test regime.

2003-07-19  Ross Johnson  <<EMAIL>>

	* eyal1.c (waste_time): Make threads do more work to ensure that
	all threads get to do some work.
	* semaphore1.c: Make it clear that certain errors are expected.
	* exception2.c (non_MSVC code sections): Change to include
	C++ standard include file, i.e. change <new.h> to <exception>.
	* exception3.c (non_MSVC code sections): Likewise; qualify std::
	namespace entities where necessary.
	* GNUmakefile: modified to work in the MsysDTK (newer MinGW)
	environment; define CC as gcc or g++ as appropriate because
	using gcc -x c++ doesn't link with required c++ libs by default,
	but g++ does.

2002-12-11  Ross Johnson  <<EMAIL>>

	* mutex7e.c: Assert EBUSY return instead of EDEADLK.

2002-06-03  Ross Johnson  <<EMAIL>>

	* semaphore2.c: New test.

2002-03-02  Ross Johnson  <<EMAIL>>

	* Makefile (CFLAGS): Changed /MT to /MD to link with
	the correct library MSVCRT.LIB. Otherwise errno doesn't
	work.

2002-02-28  Ross Johnson  <<EMAIL>>

	* exception3.c: Correct recent change.

	* semaphore1.c: New test.

	* Makefile: Add rule to generate pre-processor output.

2002-02-28  Ross Johnson  <<EMAIL>>

	* exception3.c (terminateFunction): For MSVC++, call
	exit() rather than pthread_exit(). Add comments to explain
	why.
	   * Notes from the MSVC++ manual:
	   *       1) A term_func() should call exit(), otherwise
	   *          abort() will be called on return to the caller.
	   *          abort() raises SIGABRT. The default signal handler
	   *          for all signals terminates the calling program with
	   *          exit code 3.
	   *       2) A term_func() must not throw an exception. Therefore
	   *          term_func() should not call pthread_exit() if an
	   *          an exception-using version of pthreads-win32 library
	   *          is being used (i.e. either pthreadVCE or pthreadVSE).


2002-02-23  Ross Johnson  <<EMAIL>>

	* rwlock2_t.c: New test.
	* rwlock3_t.c: New test.
	* rwlock4_t.c: New test.
	* rwlock5_t.c: New test.
	* rwlock6_t.c: New test.
	* rwlock6_t2.c: New test.
	* rwlock6.c (main): Swap thread and result variables
	to correspond to actual thread functions.
	* rwlock1.c: Change test description comment to correspond
	to the actual test.

	* condvar1_2.c: Loop over the test many times in the hope
	of detecting any intermittent deadlocks. This is to
	test a fixed problem in pthread_cond_destroy.c.

	* spin4.c: Remove unused variable.

2002-02-17  Ross Johnson  <<EMAIL>>

	* condvar1_1.c: New test.
	* condvar1_2.c: New test.

2002-02-07  Ross Johnson  <<EMAIL>>

	* delay1.c: New test.
	* delay2.c: New test.
	* exit4.c: New test.

2002-02-02  Ross Johnson  <<EMAIL>>

	* mutex8: New test.
	* mutex8n: New test.
	* mutex8e: New test.
	* mutex8r: New test.
	* cancel6a: New test.
	* cancel6d: New test.
	* cleanup0.c: Add pragmas for inline optimisation control.
	* cleanup1.c: Add pragmas for inline optimisation control.
	* cleanup2.c: Add pragmas for inline optimisation control.
	* cleanup3.c: Add pragmas for inline optimisation control.
	* condvar7.c: Add pragmas for inline optimisation control.
	* condvar8.c: Add pragmas for inline optimisation control.
	* condvar9.c: Add pragmas for inline optimisation control.

2002-01-30  Ross Johnson  <<EMAIL>>

	* cleanup1.c (): Must be declared __cdecl when compiled
	as C++ AND testing the standard C library version.

2002-01-16  Ross Johnson  <<EMAIL>>

	* spin4.c (main): Fix renamed function call.

2002-01-14  Ross Johnson  <<EMAIL>>

	* exception3.c (main): Shorten wait time.

2002-01-09  Ross Johnson  <<EMAIL>>

	* mutex7.c: New test.
	* mutex7n.c: New test.
	* mutex7e.c: New test.
	* mutex7r.c: New test.
	* mutex6.c: Modified to avoid leaving the locked mutex
	around on exit.

2001-10-25  Ross Johnson  <<EMAIL>>

	* condvar2.c: Remove reference to cv->nWaitersUnblocked.
	* condvar2_1.c: Likewise; lower NUMTHREADS from 60 to 30.
	* condvar3_1.c: Likewise.
	* condvar3_2.c: Likewise.
	* count1.c: lower NUMTHREADS from 60 to 30.
	* inherit1.c: Determine valid priority values and then
	assert values returned by POSIX routines are the same.
	* priority1.c: Likewise.
	* priority2.c: Likewise.
	
2001-07-12  Ross Johnson  <<EMAIL>>

	* barrier5.c: Assert that precisely one thread receives
	PTHREAD_BARRIER_SERIAL_THREAD at each barrier.

2001-07-09  Ross Johnson  <<EMAIL>>

	* barrier3.c: Fixed.
	* barrier4.c: Fixed.
	* barrier5.c: New; proves that all threads in the group
	reaching the barrier wait and then resume together. Repeats the test
	using groups of 1 to 16 threads. Each group of threads must negotiate
	a large number of barriers (10000).
	* spin4.c: Fixed.
	* test.h (error_string): Modified the success (0) value.

2001-07-07  Ross Johnson  <<EMAIL>>

	* spin3.c: Changed test and fixed.
	* spin4.c: Fixed.
	* barrier3.c: Fixed.
	* barrier4.c: Fixed.

2001-07-05  Ross Johnson  <<EMAIL>>

	* spin1.c: New; testing spinlocks.
	* spin2.c: New; testing spinlocks.
	* spin3.c: New; testing spinlocks.
	* spin4.c: New; testing spinlocks.
	* barrier1.c: New; testing barriers.
	* barrier2.c: New; testing barriers.
	* barrier3.c: New; testing barriers.
	* barrier4.c: New; testing barriers.
	* GNUmakefile: Add new tests.
	* Makefile: Add new tests.

2001-07-01  Ross Johnson  <<EMAIL>>

	* benchtest3.c: New; timing mutexes.
	* benchtest4.c: New; time mutexes.
	* condvar3_1.c: Fixed bug - Alexander Terekhov
	* condvar3_3.c: New test.

2001-06-25  Ross Johnson  <<EMAIL>>

	* priority1.c: New test.
	* priority2.c: New test.
	* inherit1.c: New test.
	* benchtest1.c: New; timing mutexes.
	* benchtest2.c: New; timing mutexes.
	* mutex4.c: Modified to test all mutex types.

2001-06-8  Ross Johnson  <<EMAIL>>

	* mutex5.c: Insert inert change to quell compiler warnings.
	* condvar3_2.c: Remove unused variable.
	
2001-06-3  Ross Johnson  <<EMAIL>>

	* condvar2_1.c: New test.
	* condvar3_1.c: New test.
	* condvar3_2.c: New test.

2001-05-30  Ross Johnson  <<EMAIL>>

	* mutex1n.c: New test.
	* mutex1e.c: New test.
	* mutex1r.c: New test.
	* mutex4.c: Now locks and unlocks a mutex.
	* mutex5.c: New test.
	* mutex6.c: New test.
	* mutex6n.c: New test.
	* mutex6e.c: New test.
	* mutex6r.c: New test.
	* Makefile: Added new tests; reorganised.
	* GNUmakefile: Likewise.
	* rwlock6.c: Fix to properly prove read-while-write locking
	and single writer locking.

2001-05-29  Ross Johnson  <<EMAIL>>

	* Makefile: Reorganisation.
	* GNUmakefile: Likewise.
	- Thomas Pfaff <<EMAIL>>

	* exception1.c: Add stdio.h include to define fprintf and stderr
	in non-exception C version of main().
	* exception2.c: Likewise.
	* exception3.c: Likewise.

	* Makefile (rwlock7): Add new test.
	* GNUmakefile (rwlock7): Add new test.
	* rwlock7.c: New test.
	* rwlock6.c: Changed to test that writer has priority.

	* eyal1.c (main): Unlock each mutex_start lock before destroying
	it.

2000-12-29  Ross Johnson  <<EMAIL>>

	* GNUmakefile: Add mutex4 test; ensure libpthreadw32.a is
	removed for "clean" target.
	* Makefile: Add mutex4 test.

	* exception3.c: Remove SEH code; automatically pass the test
	under SEH (which is an N/A environment).

	* mutex4.c: New test.

	* eyal1.c (do_work_unit): Add a dummy "if" to force the
	optimiser to retain code; reduce thread work loads.

	* condvar8.c (main): Add an additional "assert" for debugging;
	increase pthread_cond_signal timeout.

2000-12-28  Ross Johnson  <<EMAIL>>

	* eyal1.c: Increase thread work loads.
	* exception2.c: New test.
	* exception3.c: New test.
	* Makefile: Add new tests exception2.c and exception3.c.
	* GNUmakefile: Likewise.

2000-12-11  Ross Johnson  <<EMAIL>>

	* cleanup3.c: Remove unused variable.
	* cleanup2.c: Likewise.
	* exception1.c: Throw an exception rather than use
	a deliberate zero divide so that catch(...) will
	handle it under Mingw32. Mingw32 now builds the
	library correctly to pass all tests - see Thomas
	Pfaff's detailed instructions re needed changes
	to Mingw32 in the Pthreads-Win32 FAQ.

2000-09-08  Ross Johnson  <<EMAIL>>

	* cancel5.c: New; tests calling pthread_cancel()
	from the main thread without first creating a
	POSIX thread struct for the non-POSIX main thread
	- this forces pthread_cancel() to create one via
	pthread_self().
	* Makefile (cancel5): Add new test.
	* GNUmakefile (cancel5): Likewise.

2000-08-17  Ross Johnson  <<EMAIL>>

	* create2.c: New; Test that pthread_t contains
	the W32 HANDLE before it calls the thread routine
	proper.

2000-08-13  Ross Johnson  <<EMAIL>>

	* condvar3.c: Minor change to eliminate compiler
	warning.

	* condvar4.c: ditto.

	* condvar5.c: ditto.

	* condvar6.c: ditto.

	* condvar7.c: ditto.

	* condvar8.c: ditto.

	* condvar9.c: ditto.

	* exit1.c: Function needed return statement.

	* cleanup1.c: Remove unnecessary printf arg.

	* cleanup2.c: Fix cast.

	* rwlock6.c: Fix casts.

	* exception1.c (__PtW32CatchAll): Had the wrong name;
	fix casts.

	* cancel3.c: Remove unused waitLock variable.

	* GNUmakefile: Change library/dll naming; add new tests;
	general minor changes.

	* Makefile: Change library/dll naming; add targets for
	testing each of the two VC++ EH scheme versions;
	default target now issues help message; compile warnings
	now interpreted as errors to stop the make; add new
	tests; restructure to remove prerequisites needed
	otherwise.

	* README: Updated.


2000-08-10  Ross Johnson  <<EMAIL>>

	* eyal1.c (main): Change implicit cast to explicit
	cast when passing "print_server" function pointer;
	G++ no longer allows implicit func parameter casts.

	* cleanup1.c: Remove unused "waitLock".
	(main): Fix implicit parameter cast.

	* cancel2.c (main): Fix implicit parameter cast.

	* cancel4.c (main): Fix implicit parameter cast.

	* cancel3.c (main): Fix implicit parameter cast.

	* GNUmakefile: Renamed from Makefile; Add missing
	cancel1 and cancel2 test targets.

	* Makefile: Converted for use with MS nmake.

2000-08-06  Ross Johnson  <<EMAIL>>

	* ccl.bat: Add /nologo to remove extraneous output.

	* exception1.c (exceptionedThread): Init 'dummy';
	put expression into if condition to prevent optimising away;
	remove unused variable.

	* cancel4.c (mythread): Cast return value to avoid warnings.

	* cancel2.c (mythread): Missing #endif.

	* condvar9.c (mythread): Cast return value to avoid warnings.

	* condvar8.c (mythread): Cast return value to avoid warnings.

	* condvar7.c (mythread): Cast return value to avoid warnings.

	* cleanup3.c (mythread): Cast return value to avoid warnings.

	* cleanup2.c (mythread): Cast return value to avoid warnings.

	* cleanup1.c (mythread): Cast return value to avoid warnings.

	* condvar5.c (mythread): Cast return value to avoid warnings.

	* condvar3.c (mythread): Cast return value to avoid warnings.

	* condvar6.c (mythread): Cast return value to avoid warnings.

	* condvar4.c (mythread): Cast return value to avoid warnings.

2000-08-05  Ross Johnson  <<EMAIL>>

	* cancel2.c: Use __PtW32CatchAll macro if defined.

	* exception1.c: Use __PtW32CatchAll macro if defined.

2000-08-02  Ross Johnson  <<EMAIL>>

	* tsd1.c: Fix typecasts of &result [g++ is now very fussy].
	
	* test.h (assert): Return 0's explicitly to allay
	g++ errors.
	
	* join2.c: Add explicit typecasts.
	
	* join1.c: Add explicit typecasts.
	
	* join0.c: Add explicit typecasts.
	
	* eyal1.c: Add explicit typecasts.
	
	* count1.c (main): Add type cast to remove g++ parse warning
	[gcc-2.95.2 seems to have tightened up on this].

	* Makefile (GLANG): Use c++ explicitly.
	Remove MSVC sections (was commented out).
	Add target to generate cpp output.

2000-07-25  Ross Johnson  <<EMAIL>>

	* runtest.bat: modified to work under W98.
	
	* runall.bat: Add new tests; modified to work under W98.
	It was ok under NT.

	* Makefile: Add new tests.

	* exception1.c: New; Test passing exceptions back to the
	application and retaining library internal exceptions.

	* join0.c: New; Test a single join.

2000-01-06  Ross Johnson  <<EMAIL>>

	* cleanup1.c: New; Test cleanup handler executes (when thread is
	canceled).

	* cleanup2.c: New; Test cleanup handler executes (when thread is
	not canceled).

	* cleanup3.c: New; Test cleanup handler does not execute
	(when thread is not canceled).

2000-01-04  Ross Johnson  <<EMAIL>>

	* cancel4.c: New; Test cancellation does not occur in deferred
	cancellation threads with no cancellation points.

	* cancel3.c: New; Test asynchronous cancellation.

	* context1.c: New; Test context switching method for async
	cancellation.

1999-11-23  Ross Johnson  <<EMAIL>>

	* test.h: Add header includes; include local header versions rather
	than system versions; rearrange the assert macro defines.

1999-11-07  Ross Johnson  <<EMAIL>>

	* loadfree.c: New. Test loading and freeing the library (DLL).

1999-10-30  Ross Johnson  <<EMAIL>>

	* cancel1.c: New. Test pthread_setcancelstate and
	pthread_setcanceltype functions.
	* eyal1.c (waste_time): Change calculation to avoid FP exception
	on Aplhas
	- Rich Peters <<EMAIL>>

Oct 14 1999  Ross Johnson  <<EMAIL>>

	* condvar7.c: New. Test broadcast after waiting thread is canceled.
	* condvar8.c: New. Test multiple broadcasts.
	* condvar9.c: New. Test multiple broadcasts with thread
	cancellation.
	
Sep 16 1999  Ross Johnson  <<EMAIL>>

	* rwlock6.c: New test.

Sep 15 1999  Ross Johnson  <<EMAIL>>

	* rwlock1.c: New test.
	* rwlock2.c: New test.
	* rwlock3.c: New test.
	* rwlock4.c: New test.
	* rwlock5.c: New test.

Aug 22 1999  Ross Johnson  <<EMAIL>>

	* runall.bat (join2): Add test.

Aug 19 1999  Ross Johnson  <<EMAIL>>

	* join2.c: New test.

Wed Aug 12 1999  Ross Johnson  <<EMAIL>>

	* Makefile (LIBS): Add -L.

Mon May 31 10:25:01 1999  Ross Johnson  <<EMAIL>>

	* Makefile (GLANG): Add GCC language option.

Sat May 29 23:29:04 1999  Ross Johnson  <<EMAIL>>

	* runall.bat (condvar5): Add new test.

	* runall.bat (condvar6): Add new test.

	* Makefile (condvar5) : Add new test.
	
	* Makefile (condvar6) : Add new test.
	
	* condvar5.c: New test for pthread_cond_broadcast().

	* condvar6.c: New test for pthread_cond_broadcast().

Sun Apr  4 12:04:28 1999  Ross Johnson  <<EMAIL>>

	* tsd1.c (mythread): Change Sleep(0) to sched_yield().
	(sched.h): Include.

	* condvar3.c (mythread): Remove redundant Sleep().

	* runtest.bat: Re-organised to make more informative.

Fri Mar 19 1999  Ross Johnson  <<EMAIL>>

	* *.bat: redirect unwanted output to nul:

	* runall.bat: new.

	* cancel1.c: new. Not part of suite yet.
	
Mon Mar 15 00:17:55 1999  Ross Johnson  <<EMAIL>>

	* mutex1.c: only test mutex init and destroy; add assertions.

	* count1.c: raise number of spawned threads to 60 (appears to
	be the limit under Win98).

Sun Mar 14 21:31:02 1999  Ross Johnson  <<EMAIL>>

	* test.h (assert): add assertion trace option.
	Use:
	"#define ASSERT_TRACE 1" to turn it on,
	"#define ASSERT_TRACE 0" to turn it off (default).

	* condvar3.c (main): add more assertions.

	* condvar4.c (main): add more assertions.

	* condvar1.c (main): add more assertions.

Fri Mar 12 08:34:15 1999  Ross Johnson  <<EMAIL>>

	* condvar4.c (cvthing): switch the order of the INITIALIZERs.

	* eyal1.c (main): Fix trylock loop; was not waiting for thread to lock
	the "started" mutex.

Wed Mar 10 10:41:52 1999  Ross Johnson  <<EMAIL>>

	* tryentercs.c: Apply typo patch from bje.

	* tryentercs2.c: Ditto.

Sun Mar  7 10:41:52 1999  Ross Johnson  <<EMAIL>>

	* Makefile (condvar3, condvar4): Add tests.

	* condvar4.c (General): Reduce to simple test case; prerequisite
	is condvar3.c; add description.

	* condvar3.c (General): Reduce to simple test case; prerequisite
	is condvar2.c; add description.

	* condvar2.c (General): Reduce to simple test case; prerequisite
	is condvar1.c; add description.

	* condvar1.c (General): Reduce to simple test case; add
	description.

	* Template.c (Comments): Add generic test detail.

1999-02-23  Ross Johnson  <<EMAIL>>

        * Template.c: Revamp.

        * condvar1.c: Add.

        * condvar2.c: Add.

        * Makefile: Add condvar1 condvar2 tests.

        * exit1.c, exit2.c, exit3.c: Cosmetic changes.

1999-02-23  Ross Johnson  <<EMAIL>>

	* Makefile: Some refinement.

	* *.c: More exhaustive checking through assertions; clean up;
	add some more tests.

	* Makefile: Now actually runs the tests.

	* tests.h: Define our own assert macro. The Mingw32
	version pops up a dialog but we want to run non-interactively.

	* equal1.c: use assert a little more directly so that it
	prints the actual call statement.

	* exit1.c: Modify to return 0 on success, 1 on failure.

1999-02-22  Ross Johnson  <<EMAIL>>

	* self2.c: Bring up to date.

	* self3.c: Ditto.

1999-02-21  Ben Elliston  <<EMAIL>>

	* README: Update.

	* Makefile: New file. Run all tests automatically. Primitive tests
	are run first; more complex tests are run last.

	* count1.c: New test. Validate the thread count.

	* exit2.c: Perform a simpler test.
	
	* exit3.c: New test. Replaces exit2.c, since exit2.c needs to
	perform simpler checking first.

	* create1.c: Update to use the new testsuite exiting convention.
	
	* equal1.c: Likewise.

	* mutex1.c: Likewise.

	* mutex2.c: Likewise.

	* once1.c: Likewise.

	* self2.c: Likewise.

	* self3.c: Likewise.

	* tsd1.c: Likewise.

1999-02-20  Ross Johnson  <<EMAIL>>

	* mutex2.c: Test static mutex initialisation.

	* test.h: New. Declares a table mapping error numbers to
	error names.

1999-01-17  Ross Johnson  <<EMAIL>>

	* runtest: New script to build and run a test in the tests directory.

Wed Dec 30 11:22:44 1998  Ross Johnson  <<EMAIL>>

	* tsd1.c: Re-written. See comments at start of file.
	* Template.c: New. Contains skeleton code and comment template
	intended to fully document the test.

Fri Oct 16 17:59:49 1998  Ross Johnson  <<EMAIL>>

	* tsd1.c (destroy_key): Add function. Change diagnostics.

Thu Oct 15 17:42:37 1998  Ross Johnson  <<EMAIL>>

	* tsd1.c (mythread): Fix some casts and add some message
	output. Fix inverted conditional.

Mon Oct 12 02:12:29 1998  Ross Johnson  <<EMAIL>>

	* tsd1.c: New. Test TSD using 1 key and 2 threads.

1998-09-13  Ben Elliston  <<EMAIL>>

	* eyal1.c: New file; contributed by Eyal Lebedinsky
	<<EMAIL>>.

1998-09-12  Ben Elliston  <<EMAIL>>

	* exit2.c (func): Return a value.
	(main): Call the right thread entry function.

1998-07-22  Ben Elliston  <<EMAIL>>

	* exit2.c (main): Fix size of pthread_t array.

1998-07-10  Ben Elliston  <<EMAIL>>

	* exit2.c: New file; test pthread_exit() harder.

	* exit1.c: New file; test pthread_exit().
