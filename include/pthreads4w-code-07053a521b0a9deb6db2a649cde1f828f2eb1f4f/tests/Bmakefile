# Makefile for the pthreads test suite.
# If all of the .pass files can be created, the test suite has passed.
#
# --------------------------------------------------------------------------
#
#      Pthreads-win32 - POSIX Threads Library for Win32
#      Copyright(C) 1998 <PERSON>
#      Copyright(C) 1999,2012 Pthreads-win32 contributors
# 
#      Contact Email: <EMAIL>
# 
#      The current list of contributors is contained
#      in the file CONTRIBUTORS included with the source
#      code distribution. The list can also be seen at the
#      following World Wide Web location:
#      https://sourceforge.net/projects/pthreads4w/contributors.html
# 
#      This library is free software; you can redistribute it and/or
#      modify it under the terms of the GNU Lesser General Public
#      License as published by the Free Software Foundation; either
#      version 2 of the License, or (at your option) any later version.
# 
#      This library is distributed in the hope that it will be useful,
#      but WITHOUT ANY WARRANTY; without even the implied warranty of
#      MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
#      Lesser General Public License for more details.
# 
#      You should have received a copy of the GNU Lesser General Public
#      License along with this library in the file COPYING.LIB;
#      if not, write to the Free Software Foundation, Inc.,
#      59 Temple Place - Suite 330, Boston, MA 02111-1307, USA
#

PTW32_VER	= 3

CP	= copy
RM	= erase
CAT	= type
MKDIR	= mkdir
TOUCH	= echo Passed >
ECHO	= @echo

# The next path is relative to $BUILD_DIR
QAPC	= # ..\QueueUserAPCEx\User\quserex.dll

CPHDR	= pthread.h semaphore.h sched.h

OPTIM	= -O2

XXLIBS	= cw32mti.lib ws2_32.lib

# C++ Exceptions
BCEFLAGS	= -P -D__PtW32NoCatchWarn -D__PTW32_CLEANUP_CXX
BCELIB	= pthreadBCE$(PTW32_VER).lib
BCEDLL	= pthreadBCE$(PTW32_VER).dll
# C cleanup code
BCFLAGS	= -D__PTW32_CLEANUP_C
BCLIB	= pthreadBC$(PTW32_VER).lib
BCDLL	= pthreadBC$(PTW32_VER).dll
# C++ Exceptions in application - using VC version of pthreads dll
BCXFLAGS	= -D__PTW32_CLEANUP_C

# Defaults
CPLIB	= $(BCLIB)
CPDLL	= $(BCDLL)

CFLAGS= -q $(OPTIM) -w -tWC -tWM -4 -w-aus -w-asc -w-par
LFLAGS= 
INCLUDES=-I.
BUILD_DIR=..

COPYFILES	= $(CPHDR) $(CPLIB) $(CPDLL) $(QAPC)

EHFLAGS	=

# If a test case returns a non-zero exit code to the shell, make will
# stop.

PASSES=   \
	  errno1.pass  \
	  self1.pass  mutex5.pass  \
	  mutex1.pass  mutex1n.pass  mutex1e.pass  mutex1r.pass  \
	  semaphore1.pass  semaphore2.pass  semaphore3.pass  \
	  mutex2.pass  mutex3.pass  \
	  mutex2r.pass  mutex2e.pass  mutex3r.pass  mutex3e.pass  \
	  condvar1.pass  condvar1_1.pass  condvar1_2.pass  condvar2.pass  condvar2_1.pass  \
	  exit1.pass  create1.pass  create2.pass  reuse1.pass  reuse2.pass  equal1.pass  \
	  sequence1.pass  kill1.pass  valid1.pass  valid2.pass  \
	  exit2.pass  exit3.pass  exit4.pass  exit5.pass  \
	  join0.pass  join1.pass  detach1.pass  join2.pass join3.pass join4.pass  \
	  mutex4.pass  mutex6.pass  mutex6n.pass  mutex6e.pass  mutex6r.pass  \
	  mutex6s.pass  mutex6es.pass  mutex6rs.pass  \
	  mutex7.pass  mutex7n.pass  mutex7e.pass  mutex7r.pass  \
	  mutex8.pass  mutex8n.pass  mutex8e.pass  mutex8r.pass  \
	  robust1.pass  robust2.pass  robust3.pass  robust4.pass  robust5.pass  \
	  count1.pass  \
	  once1.pass  once2.pass  once3.pass  once4.pass  \
	  self2.pass  \
	  cancel1.pass  cancel2.pass  \
	  semaphore4.pass  semaphore4t.pass  semaphore5.pass  \
	  barrier1.pass  barrier2.pass  barrier3.pass  barrier4.pass  barrier5.pass barrier6.pass \
	  tsd1.pass  tsd2.pass  delay1.pass  delay2.pass  eyal1.pass  \
	  condvar3.pass  condvar3_1.pass  condvar3_2.pass  condvar3_3.pass  \
	  condvar4.pass  condvar5.pass  condvar6.pass  \
	  condvar7.pass  condvar8.pass  condvar9.pass  \
	  rwlock1.pass  rwlock2.pass  rwlock3.pass  rwlock4.pass  \
	  rwlock5.pass  rwlock6.pass  rwlock7.pass  rwlock8.pass  \
	  rwlock2_t.pass  rwlock3_t.pass  rwlock4_t.pass  rwlock5_t.pass  rwlock6_t.pass  rwlock6_t2.pass  \
	  context1.pass  \
	  cancel3.pass  cancel4.pass  cancel5.pass  cancel6a.pass  cancel6d.pass  \
	  cancel7.pass  cancel8.pass  \
	  cleanup0.pass  cleanup1.pass  cleanup2.pass  cleanup3.pass  \
	  priority1.pass priority2.pass inherit1.pass  \
	  spin1.pass  spin2.pass  spin3.pass  spin4.pass  \
	  exception1.pass  exception2.pass  exception3_0.pass  exception3.pass  \
	  cancel9.pass  \
	  affinity1.pass  affinity2.pass  affinity3.pass  affinity4.pass  affinity5.pass  \
	  stress1.pass

BENCHRESULTS = \
	  benchtest1.bench benchtest2.bench benchtest3.bench benchtest4.bench benchtest5.bench

help:
	@ $(ECHO) Run one of the following command lines:
	@ $(ECHO) make clean BC    (to test using BC dll with VC (no EH) applications)
	@ $(ECHO) make clean BCX   (to test using BC dll with VC++ (EH) applications)
	@ $(ECHO) make clean BCE   (to test using the BCE dll with VC++ EH applications)
	@ $(ECHO) make clean BC-bench    (to benchtest using BC dll with C bench app)
	@ $(ECHO) make clean BCX-bench   (to benchtest using BC dll with C++ bench app)
	@ $(ECHO) make clean BCE-bench   (to benchtest using BCE dll with C++ bench app)

all:
	@ make clean BC
	@ make clean BCX
	@ make clean BCE
	@ make clean BC-bench

# This allows an individual test application to be made using the default lib.
# e.g. make clean test cancel3.exe
test: $(CPLIB) $(CPDLL) $(CPHDR) $(QAPC)

tests: $(CPLIB) $(CPDLL) $(CPHDR) $(QAPC) sizes.pass $(PASSES)
	@ $(ECHO) ALL TESTS PASSED! Congratulations!

benchtests: $(CPLIB) $(CPDLL) $(CPHDR) $(BENCHRESULTS)
	@ $(ECHO) ALL BENCH TESTS DONE.

sizes.pass: sizes.exe
	@ $(ECHO) ... Running $(TEST) test: $*.exe
	@ .\$*.exe > SIZES.$(TEST)
	@ $(CAT) SIZES.$(TEST)
	@ $(ECHO) ...... Passed
	@ $(TOUCH) $*.pass

BCE:
	@ make -f Bmakefile TEST="$@" CPLIB="$(BCELIB)" CPDLL="$(BCEDLL)" EHFLAGS="$(BCEFLAGS)" tests

BC:
	@ make -f Bmakefile TEST="$@" CPLIB="$(BCLIB)" CPDLL="$(BCDLL)" EHFLAGS="$(BCFLAGS)" tests

BCX:
	@ make -f Bmakefile TEST="$@" CPLIB="$(BCLIB)" CPDLL="$(BCDLL)" EHFLAGS="$(BCXFLAGS)" tests

BCE-bench:
	@ make -f Bmakefile TEST="$@" CPLIB="$(BCELIB)" CPDLL="$(BCEDLL)" EHFLAGS="$(BCEFLAGS)" XXLIBS="benchlib.o" benchtests

BC-bench:
	@ make -f Bmakefile TEST="$@" CPLIB="$(BCLIB)" CPDLL="$(BCDLL)" EHFLAGS="$(BCFLAGS)" XXLIBS="benchlib.o" benchtests

BCX-bench:
	@ make -f Bmakefile TEST="$@" CPLIB="$(BCLIB)" CPDLL="$(BCDLL)" EHFLAGS="$(BCXFLAGS)" XXLIBS="benchlib.o" benchtests

.exe.pass:
	@ $(ECHO) ... Running $(TEST) test: $<
	@ .\$<
	@ $(ECHO) ...... Passed
	@ $(TOUCH) $@

.exe.bench:
	@ $(ECHO) ... Running $(TEST) benchtest: $<
	@ .\$<
	@ $(ECHO) ...... Done
	@ $(TOUCH) $@

.c.exe:
	@ $(ECHO) $(CC) $(EHFLAGS) $(CFLAGS) $(INCLUDES) $< -e$@ $(LFLAGS) $(CPLIB) $(XXLIBS)
	@ $(CC) $(EHFLAGS) $(CFLAGS) $(INCLUDES) $< -e$@ $(LFLAGS) $(CPLIB) $(XXLIBS)

.c.o:
	@ $(ECHO) $(CC) $(EHFLAGS) -c $(CFLAGS) $(INCLUDES) $< -o$@
	@ $(CC) $(EHFLAGS) $(CFLAGS) -c $(INCLUDES) $< -o$@


.c.i:
	@ $(CC) /P $(EHFLAGS) $(CFLAGS) $(INCLUDES) $<

$(COPYFILES):
	@ $(ECHO) Copying $(BUILD_DIR)\$@
	@ $(CP) $(BUILD_DIR)\$@ .

pthread.dll: $(CPDLL)
	@ $(CP) $(CPDLL) pthread.dll
	@ $(CP) $(CPLIB) pthread.lib

clean:
	- $(RM) *.dll
	- $(RM) *.lib
	- $(RM) pthread.h
	- $(RM) semaphore.h
	- $(RM) sched.h
	- $(RM) *.e
	- $(RM) *.i
	- $(RM) *.obj
	- $(RM) *.tds
	- $(RM) *.pdb
	- $(RM) *.o
	- $(RM) *.asm
	- $(RM) *.exe
	- $(RM) *.manifest
	- $(RM) *.pass
	- $(RM) *.bench
	- $(RM) *.log

benchtest1.bench:
benchtest2.bench:
benchtest3.bench:
benchtest4.bench:
benchtest5.bench:

affinity1.pass:
affinity2.pass: affinity1.pass
affinity3.pass: affinity2.pass
affinity4.pass: affinity3.pass
affinity5.pass: affinity4.pass
barrier1.pass: semaphore4.pass
barrier2.pass: barrier1.pass
barrier3.pass: barrier2.pass
barrier4.pass: barrier3.pass
barrier5.pass: barrier4.pass
barrier6.pass: barrier5.pass
cancel1.pass: create1.pass
cancel2.pass: cancel1.pass
cancel3.pass: context1.pass
cancel4.pass: cancel3.pass
cancel5.pass: cancel3.pass
cancel6a.pass: cancel3.pass
cancel6d.pass: cancel3.pass
cancel7.pass: kill1.pass
cancel8.pass: cancel7.pass
cancel9.pass: cancel8.pass
cleanup0.pass: cancel5.pass
cleanup1.pass: cleanup0.pass
cleanup2.pass: cleanup1.pass
cleanup3.pass: cleanup2.pass
condvar1.pass:
condvar1_1.pass: condvar1.pass
condvar1_2.pass: join2.pass
condvar2.pass: condvar1.pass
condvar2_1.pass: condvar2.pass join2.pass
condvar3.pass: create1.pass condvar2.pass
condvar3_1.pass: condvar3.pass join2.pass
condvar3_2.pass: condvar3_1.pass
condvar3_3.pass: condvar3_2.pass
condvar4.pass: create1.pass
condvar5.pass: condvar4.pass
condvar6.pass: condvar5.pass
condvar7.pass: condvar6.pass cleanup1.pass
condvar8.pass: condvar7.pass
condvar9.pass: condvar8.pass
context1.pass: cancel1.pass
count1.pass: join1.pass
create1.pass: mutex2.pass
create2.pass: create1.pass
delay1.pass:
delay2.pass: delay1.pass
detach1.pass: join0.pass
equal1.pass: create1.pass
errno1.pass: mutex3.pass
exception1.pass: cancel4.pass
exception2.pass: exception1.pass
exception3_0.pass: exception2.pass
exception3.pass: exception3_0.pass
exit1.pass:
exit2.pass: create1.pass
exit3.pass: create1.pass
exit4.pass:
exit5.pass: kill1.pass
eyal1.pass: tsd1.pass
inherit1.pass: join1.pass priority1.pass
join0.pass: create1.pass
join1.pass: create1.pass
join2.pass: create1.pass
join3.pass: join2.pass
join4.pass: join3.pass
kill1.pass: 
mutex1.pass: self1.pass
mutex1n.pass: mutex1.pass
mutex1e.pass: mutex1.pass
mutex1r.pass: mutex1.pass
mutex2.pass: mutex1.pass
mutex2r.pass: mutex2.pass
mutex2e.pass: mutex2.pass
mutex3.pass: create1.pass
mutex3r.pass: mutex3.pass
mutex3e.pass: mutex3.pass
mutex4.pass: mutex3.pass
mutex5.pass:
mutex6.pass: mutex4.pass
mutex6n.pass: mutex4.pass
mutex6e.pass: mutex4.pass
mutex6r.pass: mutex4.pass
mutex6s.pass: mutex6.pass
mutex6rs.pass: mutex6r.pass
mutex6es.pass: mutex6e.pass
mutex7.pass: mutex6.pass
mutex7n.pass: mutex6n.pass
mutex7e.pass: mutex6e.pass
mutex7r.pass: mutex6r.pass
mutex8.pass: mutex7.pass
mutex8n.pass: mutex7n.pass
mutex8e.pass: mutex7e.pass
mutex8r.pass: mutex7r.pass
once1.pass: create1.pass
once2.pass: once1.pass
once3.pass: once2.pass
once4.pass: once3.pass
priority1.pass: join1.pass
priority2.pass: priority1.pass barrier3.pass
reuse1.pass: create2.pass
reuse2.pass: reuse1.pass
robust1.pass: mutex8r.pass
robust2.pass: mutex8r.pass
robust3.pass: robust2.pass
robust4.pass: robust3.pass
robust5.pass: robust4.pass
rwlock1.pass: condvar6.pass
rwlock2.pass: rwlock1.pass
rwlock3.pass: rwlock2.pass join2.pass
rwlock4.pass: rwlock3.pass
rwlock5.pass: rwlock4.pass
rwlock6.pass: rwlock5.pass
rwlock7.pass: rwlock6.pass
rwlock8.pass: rwlock7.pass
rwlock2_t.pass: rwlock2.pass
rwlock3_t.pass: rwlock2_t.pass
rwlock4_t.pass: rwlock3_t.pass
rwlock5_t.pass: rwlock4_t.pass
rwlock6_t.pass: rwlock5_t.pass
rwlock6_t2.pass: rwlock6_t.pass
self1.pass:
self2.pass: create1.pass
semaphore1.pass:
semaphore2.pass:
semaphore3.pass: semaphore2.pass
semaphore4.pass: semaphore3.pass cancel1.pass
semaphore4t.pass: semaphore4.pass
semaphore5.pass: semaphore4.pass
sequence1.pass: reuse2.pass
sizes.pass:
spin1.pass:
spin2.pass: spin1.pass
spin3.pass: spin2.pass
spin4.pass: spin3.pass
stress1.pass:
tsd1.pass: barrier5.pass join1.pass
tsd2.pass: tsd1.pass
valid1.pass: join1.pass
valid2.pass: valid1.pass
