<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>SCHED_SETSCHEDULER(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>sched_setscheduler - set scheduling policy and parameters
(<B>REALTIME</B>) 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;sched.h&gt; </B>
</P>
<P><B>int sched_setscheduler(pid_t</B> <I>pid</I><B>, int</B> <I>policy</I><B>,
const struct sched_param *</B><I>param</I><B>); </B>
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>The <B>sched_setscheduler</B> function shall set the scheduling
policy and scheduling parameters of the process specified by <I>pid</I>
to <I>policy</I> and the parameters specified in the <B>sched_param</B>
structure pointed to by <I>param</I>, respectively. The value of the
<I>sched_priority</I> member in the <B>sched_param</B> structure
shall be any integer within the inclusive priority range for the
scheduling policy specified by <I>policy</I>. If the value of <I>pid</I>
is negative, the behavior of the <B>sched_setscheduler</B> function
is unspecified. 
</P>
<P>The possible values for the <I>policy</I> parameter are defined in
the <I>&lt;sched.h&gt;</I> header. 
</P>
<P><B>PThreads4W</B> only supports the <B>SCHED_OTHER</B> policy.
Any other value for <I>policy</I> will return failure with errno set
to <B>ENOSYS</B>. However, checks on <I>pid</I> and permissions are
performed first so that the other useful side effects of this routine
are retained.</P>
<P>If a process specified by <I>pid</I> exists, and if the calling
process has permission, the scheduling policy and scheduling
parameters shall be set for the process whose process ID is equal to
<I>pid</I>. 
</P>
<P>If <I>pid</I> is zero, the scheduling policy and scheduling
parameters shall be set for the calling process. 
</P>
<P>Implementations may require that the requesting process have
permission to set its own scheduling parameters or those of another
process. Additionally, implementation-defined restrictions may apply
as to the appropriate privileges required to set a process’ own
scheduling policy, or another process’ scheduling policy, to a
particular value. 
</P>
<P>The <B>sched_setscheduler</B> function shall be considered
successful if it succeeds in setting the scheduling policy and
scheduling parameters of the process specified by <I>pid</I> to the
values specified by <I>policy</I> and the structure pointed to by
<I>param</I>, respectively. 
</P>
<P>The effect of this function on individual threads is dependent on
the scheduling contention scope of the threads: 
</P>
<DL>
	<DT>* 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	For threads with system scheduling contention scope, these functions
	shall have no effect on their scheduling. 
	</DD><DT>
	* 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	For threads with process scheduling contention scope, the threads’
	scheduling policy and associated parameters shall not be affected.
	However, the scheduling of these threads with respect to threads in
	other processes may be dependent on the scheduling parameters of
	their process, which are governed using these functions. 
	</DD></DL>
<P>
This function is not atomic with respect to other threads in the
process. Threads may continue to execute while this function call is
in the process of changing the scheduling policy and associated
scheduling parameters for the underlying kernel-scheduled entities
used by the process contention scope threads. 
</P>
<H2><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P>Upon successful completion, the function shall return the former
scheduling policy of the specified process. If the <B>sched_setscheduler</B>
function fails to complete successfully, the policy and scheduling
parameters shall remain unchanged, and the function shall return a
value of -1 and set <I>errno</I> to indicate the error. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<P>The <B>sched_setscheduler</B> function shall fail if: 
</P>
<DL>
	<DT><B>EINVAL</B> 
	</DT><DD>
	The value of the <I>policy</I> parameter is invalid, or one or more
	of the parameters contained in <I>param</I> is outside the valid
	range for the specified scheduling policy. 
	</DD><DT>
	<B>EPERM</B> 
	</DT><DD>
	The requesting process does not have permission to set either or
	both of the scheduling parameters or the scheduling policy of the
	specified process. 
	</DD><DT>
	<B>ESRCH</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	No process can be found corresponding to that specified by <I>pid</I>.
		</DD></DL>
<P>
<I>The following sections are informative.</I> 
</P>
<H2><A HREF="#toc5" NAME="sect5">Examples</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc6" NAME="sect6">Application Usage</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc7" NAME="sect7">Rationale</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc8" NAME="sect8">Future Directions</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc9" NAME="sect9">See Also</A></H2>
<P><A HREF="sched_getscheduler.html"><B>sched_getscheduler</B>(3)</A>
<B>,</B> the Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001,
<I>&lt;sched.h&gt;</I> 
</P>
<H2><A HREF="#toc10" NAME="sect10">Copyright</A></H2>
<P>Portions of this text are reprinted and reproduced in electronic
form from IEEE Std 1003.1, 2003 Edition, Standard for Information
Technology -- Portable Operating System Interface (POSIX), The Open
Group Base Specifications Issue 6, Copyright (C) 2001-2003 by the
Institute of Electrical and Electronics Engineers, Inc and The Open
Group. In the event of any discrepancy between this version and the
original IEEE and The Open Group Standard, the original IEEE and The
Open Group Standard is the referee document. The original Standard
can be obtained online at <A HREF="http://www.opengroup.org/unix/online.html">http://www.opengroup.org/unix/online.html</A>
. 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Examples</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Application
	Usage</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">Rationale</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect8" NAME="toc8">Future
	Directions</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect9" NAME="toc9">See
	Also</A> 
	</P>
	<LI><P><A HREF="#sect10" NAME="toc10">Copyright</A> 
	</P>
</UL>
</BODY>
</HTML>
