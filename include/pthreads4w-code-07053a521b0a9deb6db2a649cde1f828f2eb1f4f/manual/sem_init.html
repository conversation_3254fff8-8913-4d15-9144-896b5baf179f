<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>SEM_INIT(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>sem_init, sem_wait, sem_trywait, sem_post, sem_getvalue,
sem_destroy - operations on semaphores 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;semaphore.h&gt;</B> 
</P>
<P><B>int sem_init(sem_t *</B><I>sem</I><B>, int </B><I>pshared</I><B>,
unsigned int </B><I>value</I><B>);</B> 
</P>
<P><B>int sem_wait(sem_t * </B><I>sem</I><B>);</B> 
</P>
<P><B>int sem_timedwait(sem_t * </B><I>sem</I>, <B>const struct
timespec *</B><I>abstime</I><B>);</B> 
</P>
<P><B>int sem_trywait(sem_t * </B><I>sem</I><B>);</B> 
</P>
<P><B>int sem_post(sem_t * </B><I>sem</I><B>);</B> 
</P>
<P><B>int sem_post_multiple(sem_t * </B><I>sem, </I><B>int</B>
<I>number</I><B>);</B> 
</P>
<P><B>int sem_getvalue(sem_t * </B><I>sem</I><B>, int * </B><I>sval</I><B>);</B>
</P>
<P><B>int sem_destroy(sem_t * </B><I>sem</I><B>);</B> 
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>Semaphores are counters for resources shared between threads. The
basic operations on semaphores are: increment the counter atomically,
and wait until the counter is non-null and decrement it atomically. 
</P>
<P><B>sem_init</B> initializes the semaphore object pointed to by
<I>sem</I>. The count associated with the semaphore is set initially
to <I>value</I>. The <I>pshared</I> argument indicates whether the
semaphore is local to the current process ( <I>pshared</I> is zero)
or is to be shared between several processes ( <I>pshared</I> is not
zero).</P>
<P><B>PThreads4W</B> currently does not support process-shared
semaphores, thus <B>sem_init</B> always returns with error <B>EPERM</B>
if <I>pshared</I> is not zero. 
</P>
<P><B>sem_wait</B> atomically decrements <I>sem</I>'s count if it is
greater than 0 and returns immediately or it suspends the calling
thread until it can resume following a call to <B>sem_post</B> or
<B>sem_post_multiple</B>.</P>
<P><B>sem_timedwait</B> atomically decrements <I>sem</I>'s count if
it is greater than 0 and returns immediately, or it suspends the
calling thread. If <I>abstime</I> time arrives before the thread can
resume following a call to <B>sem_post</B> or <B>sem_post_multiple</B>,
then <B>sem_timedwait</B> returns with a return code of -1 after
having set <B>errno</B> to <B>ETIMEDOUT</B>. If the call can return
without suspending then <I>abstime</I> is not checked.</P>
<P><B>sem_trywait</B> atomically decrements <I>sem</I>'s count if it
is greater than 0 and returns immediately, or it returns immediately
with a return code of -1 after having set <B>errno</B> to <B>EAGAIN</B>.
<B>sem_trywait</B> never blocks.</P>
<P><B>sem_post</B> either releases one thread if there are any
waiting on <I>sem</I>, or it atomically increments <I>sem</I>'s
count.</P>
<P><B>sem_post_multiple</B> either releases multiple threads if there
are any waiting on <I>sem</I> and/or it atomically increases <I>sem</I>'s
count. If there are currently <I>n</I> waiters, where <I>n</I> the
largest number less than or equal to <I>number</I>, then <I>n</I>
waiters are released and <I>sem</I>'s count is incremented by <I>number</I>
minus <I>n</I>.</P>
<P><B>sem_getvalue</B> stores in the location pointed to by <I>sval</I>
the current count of the semaphore <I>sem</I>. In the <B>PThreads4W</B>
implementation: if the value returned in <I>sval</I> is greater than
or equal to 0 it was the <I>sem</I>'s count at some point during the
call to <B>sem_getvalue</B>. If the value returned in <I>sval</I> is
less than 0 then it's absolute value represents the number of threads
waiting on <I>sem</I> at some point during the call to <B>sem_getvalue.
</B>POSIX does not require an implementation of <B>sem_getvalue</B>
to return a value in <I>sval</I> that is less than 0, but if it does
then it's absolute value must represent the number of waiters.</P>
<P><B>sem_destroy</B> destroys a semaphore object, freeing the
resources it might hold. No threads should be waiting on the
semaphore at the time <B>sem_destroy</B> is called.</P>
<H2><A HREF="#toc3" NAME="sect3">Cancellation</A></H2>
<P><B>sem_wait</B> and <B>sem_timedwait</B> are cancellation points. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Async-signal Safety</A></H2>
<P>These routines are not async-cancel safe.</P>
<H2><A HREF="#toc5" NAME="sect5">Return Value</A></H2>
<P>All semaphore functions return 0 on success, or -1 on error in
which case they write an error code in <B>errno</B>. 
</P>
<H2><A HREF="#toc6" NAME="sect6">Errors</A></H2>
<P>The <B>sem_init</B> function sets <B>errno</B> to the following
codes on error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		<I>value</I> exceeds the maximal counter value <B>SEM_VALUE_MAX</B>
				</DD><DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		<B>ENOSYS</B> 
		</DT></DL>
</DL>
<BLOCKQUOTE STYLE="margin-left: 3cm">
<I>pshared</I> is not zero 
</BLOCKQUOTE>
<P>The <B>sem_timedwait</B> function sets <B>errno</B> to the
following error code on error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>ETIMEDOUT</B>
				</DT></DL>
</DL>
<BLOCKQUOTE STYLE="margin-left: 3cm">
if <I>abstime</I> arrives before the waiting thread can resume
following a call to <B>sem_post</B> or <B>sem_post_multiple</B>. 
</BLOCKQUOTE>
<P>The <B>sem_trywait</B> function sets <B>errno</B> to the following
error code on error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EAGAIN</B> 
		</DT></DL>
</DL>
<BLOCKQUOTE STYLE="margin-left: 3cm">
if the semaphore count is currently 0 
</BLOCKQUOTE>
<P>The <B>sem_post</B> and <B>sem_post_multiple</B> functions set
<B>errno</B> to the following error code on error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>ERANGE</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		if after incrementing, the semaphore count would exceed
		<B>SEM_VALUE_MAX</B> (the semaphore count is left unchanged in this
		case) 
		</DD></DL>
</DL>
<P>
The <B>sem_destroy</B> function sets <B>errno</B> to the following
error code on error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EBUSY</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		if some threads are currently blocked waiting on the semaphore. 
		</DD></DL>
</DL>
<H2>
<A HREF="#toc7" NAME="sect7">Author</A></H2>
<P>Xavier Leroy &lt;<EMAIL>&gt; 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<H2><A HREF="#toc8" NAME="sect8">See Also</A></H2>
<P><A HREF="pthread_mutex_init.html"><B>pthread_mutex_init</B>(3)</A>
, <A HREF="pthread_cond_init.html"><B>pthread_cond_init</B>(3)</A> ,
<A HREF="pthread_cancel.html"><B>pthread_cancel</B>(3)</A> . 
</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Cancellation</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Async-signal
	Safety</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">Author</A>
		</P>
	<LI><P><A HREF="#sect8" NAME="toc8">See Also</A> 
	</P>
</UL>
</BODY>
</HTML>