<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_RWLOCK_INIT(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_rwlock_destroy, pthread_rwlock_init - destroy and
initialize a read-write lock object 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt; </B>
</P>
<P><B>pthread_wrlock_t </B><I>rwlock</I> <B>=
PTHREAD_RWLOCK_INITIALIZER;</B></P>
<P><B>int pthread_rwlock_destroy(pthread_rwlock_t *</B><I>rwlock</I><B>);
<BR>int pthread_rwlock_init(pthread_rwlock_t *restrict</B> <I>rwlock</I><B>,
const pthread_rwlockattr_t *restrict</B> <I>attr</I><B>); </B>
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>The <B>pthread_rwlock_destroy</B> function shall destroy the
read-write lock object referenced by <I>rwlock</I> and release any
resources used by the lock. The effect of subsequent use of the lock
is undefined until the lock is reinitialized by another call to
<B>pthread_rwlock_init</B>. An implementation may cause
<B>pthread_rwlock_destroy</B> to set the object referenced by <I>rwlock</I>
to an invalid value. Results are undefined if <B>pthread_rwlock_destroy</B>
is called when any thread holds <I>rwlock</I>. Attempting to destroy
an uninitialized read-write lock results in undefined behavior. 
</P>
<P>The <B>pthread_rwlock_init</B> function shall allocate any
resources required to use the read-write lock referenced by <I>rwlock</I>
and initializes the lock to an unlocked state with attributes
referenced by <I>attr</I>. If <I>attr</I> is NULL, the default
read-write lock attributes shall be used; the effect is the same as
passing the address of a default read-write lock attributes object.
Once initialized, the lock can be used any number of times without
being reinitialized. Results are undefined if <B>pthread_rwlock_init</B>
is called specifying an already initialized read-write lock. Results
are undefined if a read-write lock is used without first being
initialized. 
</P>
<P>If the <B>pthread_rwlock_init</B> function fails, <I>rwlock</I>
shall not be initialized and the contents of <I>rwlock</I> are
undefined. 
</P>
<P><B>PThreads4W</B> supports statically initialized <I>rwlock</I>
objects using <B>PTHREAD_RWLOCK_INITIALIZER</B>. <SPAN STYLE="font-weight: medium">
An application should still call <B>pthread_rwlock_destroy</B> at
some point to ensure that any resources consumed by the read/write
lock are released.</SPAN></P>
<P>Only the object referenced by <I>rwlock</I> may be used for
performing synchronization. The result of referring to copies of that
object in calls to <B>pthread_rwlock_destroy</B> ,
<B>pthread_rwlock_rdlock</B> , <B>pthread_rwlock_timedrdlock</B> ,
<B>pthread_rwlock_timedwrlock</B> , <B>pthread_rwlock_tryrdlock</B> ,
<B>pthread_rwlock_trywrlock</B> , <B>pthread_rwlock_unlock</B> , or
<B>pthread_rwlock_wrlock</B> is undefined. 
</P>
<P><B>PThreads4W</B> defines <B>_POSIX_READER_WRITER_LOCKS</B> in
pthread.h as 200112L to indicate that the reader/writer routines are
implemented and may be used.</P>
<H2><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P>If successful, the <B>pthread_rwlock_destroy</B> and
<B>pthread_rwlock_init</B> functions shall return zero; otherwise, an
error number shall be returned to indicate the error. 
</P>
<P>The [EBUSY] and [EINVAL] error checks, if implemented, act as if
they were performed immediately at the beginning of processing for
the function and caused an error return prior to modifying the state
of the read-write lock specified by <I>rwlock</I>. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<P>The <B>pthread_rwlock_destroy</B> function may fail if: 
</P>
<DL>
	<DT><B>EBUSY</B> 
	</DT><DD>
	The implementation has detected an attempt to destroy the object
	referenced by <I>rwlock</I> while it is locked. 
	</DD><DT>
	<B>EINVAL</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	The value specified by <I>rwlock</I> is invalid. 
	</DD></DL>
<P>
The <B>pthread_rwlock_init</B> function shall fail if: 
</P>
<DL>
	<DT><B>EAGAIN</B> 
	</DT><DD>
	The system lacked the necessary resources (other than memory) to
	initialize another read-write lock. 
	</DD><DT>
	<B>ENOMEM</B> 
	</DT><DD>
	Insufficient memory exists to initialize the read-write lock. 
	</DD><DD STYLE="margin-left: 0cm; margin-bottom: 0.5cm">
	<BR><BR>
	</DD></DL>
<P>
The <B>pthread_rwlock_init</B> function may fail if: 
</P>
<DL>
	<DT><B>EINVAL</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	The value specified by <I>attr</I> is invalid. 
	</DD></DL>
<P>
These functions shall not return an error code of [EINTR]. 
</P>
<P><I>The following sections are informative.</I> 
</P>
<H2><A HREF="#toc5" NAME="sect5">Examples</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc6" NAME="sect6">Application Usage</A></H2>
<P>Applications using these and related read-write lock functions may
be subject to priority inversion, as discussed in the Base
Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001, Section 3.285,
Priority Inversion. 
</P>
<H2><A HREF="#toc7" NAME="sect7">Rationale</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc8" NAME="sect8">Future Directions</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc9" NAME="sect9">See Also</A></H2>
<P><A HREF="pthread_rwlock_rdlock.html"><B>pthread_rwlock_rdlock</B>(3)</A>
<B>,</B> <A HREF="pthread_rwlock_timedrdlock.html"><B>pthread_rwlock_timedrdlock</B>(3)</A>
<B>,</B> <A HREF="pthread_rwlock_timedwrlock.html"><B>pthread_rwlock_timedwrlock</B>(3)</A>
<B>,</B> <A HREF="pthread_rwlock_rdlock.html"><B>pthread_rwlock_tryrdlock</B>(3)</A>
<B>,</B> <A HREF="pthread_rwlock_wrlock.html"><B>pthread_rwlock_trywrlock</B>(3)</A>
<B>,</B> <A HREF="pthread_rwlock_unlock.html"><B>pthread_rwlock_unlock</B>(3)</A>
<B>,</B> <A HREF="pthread_rwlock_wrlock.html"><B>pthread_rwlock_wrlock</B>(3)</A>
<B>,</B> the Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001,
<I>&lt;pthread.h&gt;</I> 
</P>
<H2><A HREF="#toc10" NAME="sect10">Copyright</A></H2>
<P>Portions of this text are reprinted and reproduced in electronic
form from IEEE Std 1003.1, 2003 Edition, Standard for Information
Technology -- Portable Operating System Interface (POSIX), The Open
Group Base Specifications Issue 6, Copyright (C) 2001-2003 by the
Institute of Electrical and Electronics Engineers, Inc and The Open
Group. In the event of any discrepancy between this version and the
original IEEE and The Open Group Standard, the original IEEE and The
Open Group Standard is the referee document. The original Standard
can be obtained online at <A HREF="http://www.opengroup.org/unix/online.html">http://www.opengroup.org/unix/online.html</A>
. 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Examples</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Application
	Usage</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">Rationale</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect8" NAME="toc8">Future
	Directions</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect9" NAME="toc9">See
	Also</A> 
	</P>
	<LI><P><A HREF="#sect10" NAME="toc10">Copyright</A> 
	</P>
</UL>
</BODY>
</HTML>
