<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=windows-1252">
	<TITLE>PTHREAD_CREATE(3) manual page</TITLE>
	<META NAME="GENERATOR" CONTENT="OpenOffice 4.1.1  (Win32)">
	<META NAME="CREATED" CONTENT="0;0">
	<META NAME="CHANGEDBY" CONTENT="<PERSON> Johnson">
	<META NAME="CHANGED" CONTENT="20160229;20085251">
	<STYLE TYPE="text/css">
	<!--
		H4.cjk { font-family: "SimSun" }
		H4.ctl { font-family: "Mangal" }
		H2.cjk { font-family: "SimSun" }
		H2.ctl { font-family: "Mangal" }
	-->
	</STYLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4 CLASS="western">POSIX Threads for Windows &ndash; REFERENCE -
<A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2 CLASS="western"><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_create - create a new thread 
</P>
<H2 CLASS="western"><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>int pthread_create(pthread_t * </B><I>thread</I><B>,
pthread_attr_t * </B><I>attr</I><B>, void * (*</B><I>start_routine</I><B>)(void
*), void * </B><I>arg</I><B>);</B> 
</P>
<H2 CLASS="western"><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P><B>pthread_create</B> creates a new thread of control that
executes concurrently with the calling thread. The new thread applies
the function <I>start_routine</I> passing it <I>arg</I> as first
argument. The new thread terminates either explicitly, by calling
<A HREF="pthread_exit.html"><B>pthread_exit</B>(3)</A> , or
implicitly, by returning from the <I>start_routine</I> function. The
latter case is equivalent to calling <A HREF="pthread_exit.html"><B>pthread_exit</B>(3)</A>
with the result returned by <I>start_routine</I> as exit code. 
</P>
<P>The initial signal state of the new thread is inherited from it's
creating thread and there are no pending signals. <B>PThreads4W</B>
does not yet implement signals.</P>
<P>The initial CPU affinity of the new thread is inherited from it's
creating thread. A threads CPU affinity can be obtained through
<A HREF="pthread_setaffinity_np.html"><B>pthread_getaffinity_np(3)</B></A>
and may be changed through <A HREF="pthread_setaffinity_np.html"><B>pthread_setaffinity_np(3)</B></A>.
Unless changed, all threads inherit the CPU affinity of the parent
process. See <A HREF="sched_setaffinity.html"><B>sched_getaffinity(3)</B></A>
and <A HREF="sched_setaffinity.html"><B>sched_setaffinity(3)</B></A>.</P>
<P>The <I>attr</I> argument specifies thread attributes to be applied
to the new thread. See <A HREF="pthread_attr_init.html"><B>pthread_attr_init</B>(3)</A>
for a complete list of thread attributes. The <I>attr</I> argument
can also be <B>NULL</B>, in which case default attributes are used:
the created thread is joinable (not detached) and has default (non
real-time) scheduling policy. 
</P>
<H2 CLASS="western"><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P>On success, the identifier of the newly created thread is stored
in the location pointed by the <I>thread</I> argument, and a 0 is
returned. On error, a non-zero error code is returned. 
</P>
<H2 CLASS="western"><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<DL>
	<DL>
		<DT STYLE="margin-left: 0.25cm"><B>EAGAIN</B></DT><DL>
			<DL>
				<DT STYLE="margin-left: -0.5cm; margin-bottom: 0.5cm">
				Not enough system resources to create a process for the new
				thread, or<BR>more than <B>PTHREAD_THREADS_MAX</B> threads are
				already active. 
				</DT></DL>
		</DL>
	</DL>
</DL>
<H2 CLASS="western">
<A HREF="#toc5" NAME="sect5">Author</A></H2>
<P>Xavier Leroy &lt;<EMAIL>&gt; 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<H2 CLASS="western"><A HREF="#toc6" NAME="sect6">See Also</A></H2>
<P><A HREF="pthread_exit.html"><B>pthread_exit</B>(3)</A> ,
<A HREF="pthread_join.html"><B>pthread_join</B>(3)</A> ,
<A HREF="pthread_detach.html"><B>pthread_detach</B>(3)</A> ,
<A HREF="pthread_attr_init.html"><B>pthread_attr_init</B>(3)</A> ,
<A HREF="pthread_setaffinity_np.html"><B>pthread_getaffinity_np(3)</B></A>
, <A HREF="pthread_setaffinity_np.html"><B>pthread_setaffinity_np(3)</B></A>
, <A HREF="sched_setaffinity.html"><B>sched_getaffinity(3)</B></A> ,
<A HREF="sched_setaffinity.html"><B>sched_setaffinity(3)</B></A> . 
</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Author</A>
		</P>
	<LI><P><A HREF="#sect6" NAME="toc6">See Also</A> 
	</P>
</UL>
</BODY>
</HTML>