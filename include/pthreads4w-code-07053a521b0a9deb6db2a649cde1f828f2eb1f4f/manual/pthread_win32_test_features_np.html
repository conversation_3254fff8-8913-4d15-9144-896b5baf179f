<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_WIN32_TEST_FEATURES_NP(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P STYLE="font-weight: medium">pthread_win32_test_features_np –
find out what features were detected at process attach time.</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>BOOL pthread_win32_test_features_np(int</B> <I>mask</I><B>);</B></P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P><B>pthread_win32_test_features_np</B> allows an application to
check which run-time auto-detected features are available within the
library.</P>
<P>The possible features are:</P>
<P><B>PTW32_SYSTEM_INTERLOCKED_COMPARE_EXCHANGE</B></P>
<P STYLE="margin-left: 2cm">Return TRUE if the Win32 version of
InterlockedCompareExchange() is being used. On IA32 systems the
library can use optimised and inlinable assembler versions of
InterlockedExchange() and InterlockedCompareExchange().</P>
<P><B>PTW32_ALERTABLE_ASYNC_CANCEL</B></P>
<P STYLE="margin-left: 2cm">Return TRUE if the QueueUserAPCEx package
QUSEREX.DLL and the AlertDrv.sys driver was detected. This package
provides alertable (pre-emptive) asynchronous threads cancellation.
If this feature returns FALSE then the default async cancel scheme is
in use, which cannot cancel blocked threads.</P>
<H2><A HREF="#toc3" NAME="sect3">Cancellation</A></H2>
<P>None.</P>
<H2><A HREF="#toc4" NAME="sect4"><FONT COLOR="#000080">Return Value</FONT></A></H2>
<P><B>pthread_win32_test_features_np</B> returns TRUE (non-zero) if
the specified feature is present, and FALSE (0) otherwise.</P>
<H2><A HREF="#toc5" NAME="sect5">Errors</A></H2>
<P>None.</P>
<H2><A HREF="#toc6" NAME="sect6">Author</A></H2>
<P>Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Cancellation</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Errors</A>
		</P>
	<LI><P><A HREF="#sect6" NAME="toc6">Author</A> 
	</P>
</UL>
</BODY>
</HTML>