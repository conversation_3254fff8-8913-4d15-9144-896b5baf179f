<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_SETCONCURRENCY(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_getconcurrency, pthread_setconcurrency - get and set the
level of concurrency 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt; </B>
</P>
<P><B>int pthread_getconcurrency(void);</B> <BR><B>int
pthread_setconcurrency(int</B> <I>new_level</I><B>); </B>
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>Unbound threads in a process may or may not be required to be
simultaneously active. By default, the threads implementation ensures
that a sufficient number of threads are active so that the process
can continue to make progress. While this conserves system resources,
it may not produce the most effective level of concurrency. 
</P>
<P>The <B>pthread_setconcurrency</B> function allows an application
to inform the threads implementation of its desired concurrency
level, <I>new_level</I>. The actual level of concurrency provided by
the implementation as a result of this function call is unspecified. 
</P>
<P>If <I>new_level</I> is zero, it causes the implementation to
maintain the concurrency level at its discretion as if
<B>pthread_setconcurrency</B> had never been called. 
</P>
<P>The <B>pthread_getconcurrency</B> function shall return the value
set by a previous call to the <B>pthread_setconcurrency</B> function.
If the <B>pthread_setconcurrency</B> function was not previously
called, this function shall return zero to indicate that the
implementation is maintaining the concurrency level. 
</P>
<P>A call to <B>pthread_setconcurrency</B> shall inform the
implementation of its desired concurrency level. The implementation
shall use this as a hint, not a requirement. 
</P>
<P>If an implementation does not support multiplexing of user threads
on top of several kernel-scheduled entities, the
<B>pthread_setconcurrency</B> and <B>pthread_getconcurrency</B>
functions are provided for source code compatibility but they shall
have no effect when called. To maintain the function semantics, the
<I>new_level</I> parameter is saved when <B>pthread_setconcurrency</B>
is called so that a subsequent call to <B>pthread_getconcurrency</B>
shall return the same value. 
</P>
<P><B>PThreads4W</B> provides these routines for source code
compatibility only, as described in the previous paragraph.</P>
<H2><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P>If successful, the <B>pthread_setconcurrency</B> function shall
return zero; otherwise, an error number shall be returned to indicate
the error. 
</P>
<P>The <B>pthread_getconcurrency</B> function shall always return the
concurrency level set by a previous call to <B>pthread_setconcurrency</B>
. If the <B>pthread_setconcurrency</B> function has never been
called, <B>pthread_getconcurrency</B> shall return zero. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<P>The <B>pthread_setconcurrency</B> function shall fail if: 
</P>
<DL>
	<DT><B>EINVAL</B> 
	</DT><DD>
	The value specified by <I>new_level</I> is negative. 
	</DD><DT>
	<B>EAGAIN</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	The value specific by <I>new_level</I> would cause a system resource
	to be exceeded. 
	</DD></DL>
<P>
These functions shall not return an error code of [EINTR]. 
</P>
<P><I>The following sections are informative.</I> 
</P>
<H2><A HREF="#toc5" NAME="sect5">Examples</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc6" NAME="sect6">Application Usage</A></H2>
<P>Use of these functions changes the state of the underlying
concurrency upon which the application depends. Library developers
are advised to not use the <B>pthread_getconcurrency</B> and
<B>pthread_setconcurrency</B> functions since their use may conflict
with an applications use of these functions. 
</P>
<H2><A HREF="#toc7" NAME="sect7">Rationale</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc8" NAME="sect8">Future Directions</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc9" NAME="sect9">See Also</A></H2>
<P>The Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001,
<I>&lt;pthread.h&gt;</I> 
</P>
<H2><A HREF="#toc10" NAME="sect10">Copyright</A></H2>
<P>Portions of this text are reprinted and reproduced in electronic
form from IEEE Std 1003.1, 2003 Edition, Standard for Information
Technology -- Portable Operating System Interface (POSIX), The Open
Group Base Specifications Issue 6, Copyright (C) 2001-2003 by the
Institute of Electrical and Electronics Engineers, Inc and The Open
Group. In the event of any discrepancy between this version and the
original IEEE and The Open Group Standard, the original IEEE and The
Open Group Standard is the referee document. The original Standard
can be obtained online at <A HREF="http://www.opengroup.org/unix/online.html">http://www.opengroup.org/unix/online.html</A>
. 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Examples</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Application
	Usage</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">Rationale</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect8" NAME="toc8">Future
	Directions</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect9" NAME="toc9">See
	Also</A> 
	</P>
	<LI><P><A HREF="#sect10" NAME="toc10">Copyright</A> 
	</P>
</UL>
</BODY>
</HTML>
