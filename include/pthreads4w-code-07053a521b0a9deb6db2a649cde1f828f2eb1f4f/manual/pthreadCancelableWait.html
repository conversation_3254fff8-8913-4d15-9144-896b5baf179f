<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=windows-1252">
	<TITLE>PTHREADCANCELLABLEWAIT(3) manual page</TITLE>
	<META NAME="GENERATOR" CONTENT="OpenOffice 4.1.1  (Win32)">
	<META NAME="CREATED" CONTENT="0;0">
	<META NAME="CHANGEDBY" CONTENT="<PERSON> Johnson">
	<META NAME="CHANGED" CONTENT="20160229;19515927">
	<STYLE TYPE="text/css">
	<!--
		H4.cjk { font-family: "SimSun" }
		H4.ctl { font-family: "Mangal" }
		H2.cjk { font-family: "SimSun" }
		H2.ctl { font-family: "Mangal" }
	-->
	</STYLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4 CLASS="western">POSIX Threads for Windows &ndash; REFERENCE &ndash;
<A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2 CLASS="western"><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P STYLE="font-weight: normal">pthreadCancelableTimedWait,
pthreadCancelableWait &ndash; provide cancellation hooks for user
Win32 routines</P>
<H2 CLASS="western"><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>int pthreadCancelableTimedWait (HANDLE </B><I>waitHandle</I><B>,
DWORD </B><I>timeout</I><B>);</B></P>
<P><B>int pthreadCancelableWait (HANDLE </B><I>waitHandle</I><B>);</B></P>
<H2 CLASS="western"><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>These two functions provide hooks into the <A HREF="pthread_cancel.html"><B>pthread_cancel</B></A>()
mechanism that will allow you to wait on a Windows handle and make it
a cancellation point. Both functions block until either the given
Win32 <B>HANDLE</B> is signalled, or <A HREF="pthread_cancel.html"><B>pthread_cancel</B></A>()
has been called. They are implemented using <B>WaitForMultipleObjects</B>
on <I>waitHandle</I> and the manually reset Win32 event handle that
is the target of <A HREF="pthread_cancel.html"><B>pthread_cancel</B></A>().
These routines may be called from Win32 native threads but
<A HREF="pthread_cancel.html"><B>pthread_cancel</B></A>() will
require that thread's POSIX thread ID that the thread must retrieve
using <A HREF="pthread_self.html"><B>pthread_self</B></A>().</P>
<P><B>pthreadCancelableTimedWait</B> is the timed version that will
return with the code <B>ETIMEDOUT</B> if the interval <I>timeout</I>
milliseconds elapses before <I>waitHandle</I> is signalled.</P>
<H2 CLASS="western"><A HREF="#toc3" NAME="sect3">Cancellation</A></H2>
<P>These routines allow routines that block on Win32 HANDLEs to be
cancellable via <A HREF="pthread_cancel.html"><B>pthread_cancel</B></A>().</P>
<H2 CLASS="western"><A HREF="#toc4" NAME="sect4">Return Value</A></H2>
<P><BR><BR>
</P>
<H2 CLASS="western"><A HREF="#toc5" NAME="sect5">Errors</A></H2>
<P>The <B>pthreadCancelableTimedWait</B> function returns the
following error code on error: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>ETIMEDOUT</B>
				</DT>
</DL>
<P STYLE="margin-left: 2cm">
The interval <I>timeout</I> milliseconds elapsed before <I>waitHandle</I>
was signalled.</P>
<H2 CLASS="western"><A HREF="#toc6" NAME="sect6">Author</A></H2>
<P>Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<H2 CLASS="western"><A HREF="#toc7" NAME="sect7">See also</A></H2>
<P><A HREF="pthread_cancel.html"><B>pthread_cancel()</B></A>,
<A HREF="pthread_self.html"><B>pthread_self()</B></A></P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Cancellation</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Author</A>
		</P>
	<LI><P><A HREF="#sect7" NAME="toc7">See also</A> 
	</P>
</UL>
</BODY>
</HTML>