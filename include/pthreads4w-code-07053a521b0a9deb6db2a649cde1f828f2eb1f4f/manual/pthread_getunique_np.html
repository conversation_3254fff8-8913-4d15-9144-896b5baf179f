<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_GETUNIQUE_NP(3) manual page</TITLE>
	<STYLE TYPE="text/css">
	<!--
		H4.cjk { font-family: "AR PL UMing CN" }
		H4.ctl { font-family: "Lohit Devanagari" }
		H2.cjk { font-family: "AR PL UMing CN" }
		H2.ctl { font-family: "Lohit Devanagari" }
	-->
	</STYLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4 CLASS="western">POSIX Threads for Windows – REFERENCE -
<A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2 CLASS="western"><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P STYLE="font-weight: normal">pthread_getunique_np – get the
unique sequence number associated with a thread</P>
<H2 CLASS="western"><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>unsigned long long pthread_getunique_np(pthread_t</B> <I>thread</I><B>);</B></P>
<H2 CLASS="western"><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>Returns the <B>unique </B><SPAN STYLE="font-weight: normal">64 bit
sequence number</SPAN> assigned to <I>thread</I>.</P>
<P>In <B>PThreads4W:</B></P>
<UL>
	<LI><P>the value returned is not reused after the thread terminates
	so it is unique for the life of the process</P>
	<LI><P>Windows native threads may obtain their own POSIX thread
	sequence number by first retrieving their <B>pthread_t</B> handle
	via <B>pthread_self</B> to use as the <I>thread</I> argument.</P>
</UL>
<P>This function was added for source code compatibility with some
other POSIX threads implementations.</P>
<H2 CLASS="western"><A HREF="#toc3" NAME="sect3">Cancellation</A></H2>
<P>None.</P>
<H2 CLASS="western"><A HREF="#toc4" NAME="sect4">Return Value</A></H2>
<P><B>pthread_getunique_np</B> returns the unique sequence number for
<I>thread</I>.</P>
<H2 CLASS="western"><A HREF="#toc5" NAME="sect5">Errors</A></H2>
<P>None.</P>
<H2 CLASS="western"><A HREF="#toc6" NAME="sect6">Author</A></H2>
<P>Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0in"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0in"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0in"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0in"><A HREF="#sect3" NAME="toc3">Cancellation</A>
		</P>
	<LI><P STYLE="margin-bottom: 0in"><A HREF="#sect4" NAME="toc4">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0in"><A HREF="#sect5" NAME="toc5">Errors</A>
		</P>
	<LI><P><A HREF="#sect6" NAME="toc6">Author</A> 
	</P>
</UL>
</BODY>
</HTML>