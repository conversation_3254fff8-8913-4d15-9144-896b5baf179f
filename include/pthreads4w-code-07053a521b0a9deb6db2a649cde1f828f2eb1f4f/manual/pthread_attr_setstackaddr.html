<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=windows-1252">
	<TITLE>PTHREAD_ATTR_SETSTACKADDR(3) manual page</TITLE>
	<META NAME="GENERATOR" CONTENT="OpenOffice 4.1.1  (Win32)">
	<META NAME="CREATED" CONTENT="0;0">
	<META NAME="CHANGEDBY" CONTENT="Ross Johnson">
	<META NAME="CHANGED" CONTENT="20160229;19522374">
	<STYLE TYPE="text/css">
	<!--
		H4.cjk { font-family: "SimSun" }
		H4.ctl { font-family: "Mangal" }
		H2.cjk { font-family: "SimSun" }
		H2.ctl { font-family: "Mangal" }
	-->
	</STYLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4 CLASS="western">POSIX Threads for Windows &ndash; REFERENCE &ndash;
<A HREF="http://sourceforge.net/projects/pthreads4w/">PThreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2 CLASS="western"><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_attr_getstackaddr, pthread_attr_setstackaddr - get and set
the stackaddr attribute 
</P>
<H2 CLASS="western"><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt; </B>
</P>
<P><B>int pthread_attr_getstackaddr(const pthread_attr_t *restrict</B>
<I>attr</I><B>, void **restrict</B> <I>stackaddr</I><B>); <BR>int
pthread_attr_setstackaddr(pthread_attr_t *</B><I>attr</I><B>, void
*</B><I>stackaddr</I><B>); </B>
</P>
<H2 CLASS="western"><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>The <B>pthread_attr_getstackaddr</B> and <B>pthread_attr_setstackaddr</B>
functions, respectively, shall get and set the thread creation
<I>stackaddr</I> attribute in the <I>attr</I> object. 
</P>
<P>The <I>stackaddr</I> attribute specifies the location of storage
to be used for the created thread&rsquo;s stack. The size of the
storage shall be at least {PTHREAD_STACK_MIN}. 
</P>
<P><B>PThreads4W</B> defines <B>_POSIX_THREAD_ATTR_STACKADDR</B> in
pthread.h as -1 to indicate that these routines are implemented but
cannot used to set or get the stack address. These routines always
return the error ENOSYS when called.</P>
<H2 CLASS="western"><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P>Upon successful completion, <B>pthread_attr_getstackaddr</B> and
<B>pthread_attr_setstackaddr</B> shall return a value of 0;
otherwise, an error number shall be returned to indicate the error. 
</P>
<P>The <B>pthread_attr_getstackaddr</B> function stores the <I>stackaddr</I>
attribute value in <I>stackaddr</I> if successful. 
</P>
<H2 CLASS="western"><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<P>The <B>pthread_attr_setstackaddr</B> function always returns the
following error code: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>ENOSYS</B></DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		The function is not supported. 
		</DD>
</DL>
<P>
The <B>pthread_attr_getstackaddr</B> function always returns the
following error code: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>ENOSYS</B></DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		The function is not supported. 
		</DD>
</DL>
<P>
These functions shall not return an error code of [EINTR]. 
</P>
<P><I>The following sections are informative.</I> 
</P>
<H2 CLASS="western"><A HREF="#toc5" NAME="sect5">Examples</A></H2>
<P>None. 
</P>
<H2 CLASS="western"><A HREF="#toc6" NAME="sect6">Application Usage</A></H2>
<P>The specification of the <I>stackaddr</I> attribute presents
several ambiguities that make portable use of these interfaces
impossible. The description of the single address parameter as a
&quot;stack&quot; does not specify a particular relationship between
the address and the &quot;stack&quot; implied by that address. For
example, the address may be taken as the low memory address of a
buffer intended for use as a stack, or it may be taken as the address
to be used as the initial stack pointer register value for the new
thread. These two are not the same except for a machine on which the
stack grows &quot;up&quot; from low memory to high, and on which a
&quot;push&quot; operation first stores the value in memory and then
increments the stack pointer register. Further, on a machine where
the stack grows &quot;down&quot; from high memory to low,
interpretation of the address as the &quot;low memory&quot; address
requires a determination of the intended size of the stack.
IEEE&nbsp;Std&nbsp;1003.1-2001 has introduced the new interfaces
<A HREF="pthread_attr_setstack.html"><B>pthread_attr_setstack</B>(3)</A>
and <A HREF="pthread_attr_getstack.html"><B>pthread_attr_getstack</B>(3)</A>
to resolve these ambiguities. 
</P>
<H2 CLASS="western"><A HREF="#toc7" NAME="sect7">Rationale</A></H2>
<P>None. 
</P>
<H2 CLASS="western"><A HREF="#toc8" NAME="sect8">Future Directions</A></H2>
<P>None. 
</P>
<H2 CLASS="western"><A HREF="#toc9" NAME="sect9">See Also</A></H2>
<P><A HREF="pthread_attr_init.html"><B>pthread_attr_destroy</B>(3)</A>
, <A HREF="pthread_attr_init.html"><B>pthread_attr_getdetachstate</B>(3)</A>
, <A HREF="pthread_attr_getstack.html"><B>pthread_attr_getstack</B>(3)</A>
, <A HREF="pthread_attr_getstacksize.html"><B>pthread_attr_getstacksize</B>(3)</A>
, <A HREF="pthread_attr_setstack.html"><B>pthread_attr_setstack</B>(3)</A>
, <A HREF="pthread_create.html"><B>pthread_create</B>(3)</A> , the
Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001,
<I>&lt;limits.h&gt;</I>, <I>&lt;pthread.h&gt;</I> 
</P>
<H2 CLASS="western"><A HREF="#toc10" NAME="sect10">Copyright</A></H2>
<P>Portions of this text are reprinted and reproduced in electronic
form from IEEE Std 1003.1, 2003 Edition, Standard for Information
Technology -- Portable Operating System Interface (POSIX), The Open
Group Base Specifications Issue 6, Copyright (C) 2001-2003 by the
Institute of Electrical and Electronics Engineers, Inc and The Open
Group. In the event of any discrepancy between this version and the
original IEEE and The Open Group Standard, the original IEEE and The
Open Group Standard is the referee document. The original Standard
can be obtained online at <A HREF="http://www.opengroup.org/unix/online.html">http://www.opengroup.org/unix/online.html</A>
. 
</P>
<P>Modified by Ross Johnson for use with <A HREF="http://sourceforge.net/projects/pthreads4w/">PThreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Examples</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Application
	Usage</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">Rationale</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect8" NAME="toc8">Future
	Directions</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect9" NAME="toc9">See
	Also</A> 
	</P>
	<LI><P><A HREF="#sect10" NAME="toc10">Copyright</A> 
	</P>
</UL>
</BODY>
</HTML>