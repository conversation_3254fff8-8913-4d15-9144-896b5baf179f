<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_GETW32THREADHANDLE_NP(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P STYLE="font-weight: medium">pthread_getw32threadhandle_np – get
the Win32 thread handle associated with a thread</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>HANDLE pthread_getw32threadhandle_np(pthread_t</B> <I>thread</I><B>);</B></P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>Returns the Win32 native thread <B>HANDLE</B> that the POSIX
thread <I>thread</I> is running as.</P>
<P>Applications can use the Win32 handle to set Win32 specific
attributes of the thread.</P>
<H2><A HREF="#toc3" NAME="sect3">Cancellation</A></H2>
<P>None.</P>
<H2><A HREF="#toc4" NAME="sect4">Return Value</A></H2>
<P><B>pthread_getw32threadhandle_np</B> returns the Win32 native
thread <B>HANDLE</B> for the specified POSIX thread <I>thread</I>.</P>
<H2><A HREF="#toc5" NAME="sect5">Errors</A></H2>
<P>None.</P>
<H2><A HREF="#toc6" NAME="sect6">Author</A></H2>
<P>Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Cancellation</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Errors</A>
		</P>
	<LI><P><A HREF="#sect6" NAME="toc6">Author</A> 
	</P>
</UL>
</BODY>
</HTML>
