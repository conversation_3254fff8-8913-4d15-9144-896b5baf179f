<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by GNU Texinfo 7.2, https://www.gnu.org/software/texinfo/ -->
  <head>
    <meta charset="utf-8">
    <title>
      FFmpeg Formats Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      FFmpeg Formats Documentation
      </h1>


<a name="SEC_Top"></a>

<div class="region-contents" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="toc-numbered-mark">
  <li><a id="toc-Description" href="#Description">1 Description</a></li>
  <li><a id="toc-Format-Options" href="#Format-Options">2 Format Options</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Format-stream-specifiers" href="#Format-stream-specifiers-1">2.1 Format stream specifiers</a></li>
  </ul></li>
  <li><a id="toc-Demuxers" href="#Demuxers">3 Demuxers</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-aa" href="#aa">3.1 aa</a></li>
    <li><a id="toc-aac" href="#aac">3.2 aac</a></li>
    <li><a id="toc-apng" href="#apng">3.3 apng</a></li>
    <li><a id="toc-asf" href="#asf-1">3.4 asf</a></li>
    <li><a id="toc-concat" href="#concat-1">3.5 concat</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Syntax" href="#Syntax">3.5.1 Syntax</a></li>
      <li><a id="toc-Options" href="#Options">3.5.2 Options</a></li>
      <li><a id="toc-Examples" href="#Examples">3.5.3 Examples</a></li>
    </ul></li>
    <li><a id="toc-dash" href="#dash-1">3.6 dash</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-1" href="#Options-1">3.6.1 Options</a></li>
    </ul></li>
    <li><a id="toc-dvdvideo" href="#dvdvideo">3.7 dvdvideo</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Background" href="#Background">3.7.1 Background</a></li>
      <li><a id="toc-Options-2" href="#Options-2">3.7.2 Options</a></li>
      <li><a id="toc-Examples-1" href="#Examples-1">3.7.3 Examples</a></li>
    </ul></li>
    <li><a id="toc-ea" href="#ea">3.8 ea</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-3" href="#Options-3">3.8.1 Options</a></li>
    </ul></li>
    <li><a id="toc-imf" href="#imf">3.9 imf</a></li>
    <li><a id="toc-flv_002c-live_005fflv_002c-kux" href="#flv_002c-live_005fflv_002c-kux">3.10 flv, live_flv, kux</a></li>
    <li><a id="toc-gif" href="#gif-1">3.11 gif</a></li>
    <li><a id="toc-hls" href="#hls-1">3.12 hls</a></li>
    <li><a id="toc-image2" href="#image2-1">3.13 image2</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples-2" href="#Examples-2">3.13.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-libgme" href="#libgme">3.14 libgme</a></li>
    <li><a id="toc-libmodplug" href="#libmodplug">3.15 libmodplug</a></li>
    <li><a id="toc-libopenmpt" href="#libopenmpt">3.16 libopenmpt</a></li>
    <li><a id="toc-mov_002fmp4_002f3gp" href="#mov_002fmp4_002f3gp">3.17 mov/mp4/3gp</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-4" href="#Options-4">3.17.1 Options</a></li>
      <li><a id="toc-Audible-AAX" href="#Audible-AAX">3.17.2 Audible AAX</a></li>
    </ul></li>
    <li><a id="toc-mpegts" href="#mpegts">3.18 mpegts</a></li>
    <li><a id="toc-mpjpeg" href="#mpjpeg">3.19 mpjpeg</a></li>
    <li><a id="toc-rawvideo" href="#rawvideo">3.20 rawvideo</a></li>
    <li><a id="toc-rcwt" href="#rcwt">3.21 rcwt</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples-3" href="#Examples-3">3.21.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-sbg" href="#sbg">3.22 sbg</a></li>
    <li><a id="toc-tedcaptions" href="#tedcaptions">3.23 tedcaptions</a></li>
    <li><a id="toc-vapoursynth" href="#vapoursynth">3.24 vapoursynth</a></li>
    <li><a id="toc-w64" href="#w64">3.25 w64</a></li>
    <li><a id="toc-wav" href="#wav-1">3.26 wav</a></li>
  </ul></li>
  <li><a id="toc-Muxers" href="#Muxers">4 Muxers</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Raw-muxers" href="#Raw-muxers">4.1 Raw muxers</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples-4" href="#Examples-4">4.1.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-Raw-PCM-muxers" href="#Raw-PCM-muxers">4.2 Raw PCM muxers</a></li>
    <li><a id="toc-MPEG_002d1_002fMPEG_002d2-program-stream-muxers" href="#MPEG_002d1_002fMPEG_002d2-program-stream-muxers">4.3 MPEG-1/MPEG-2 program stream muxers</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-5" href="#Options-5">4.3.1 Options</a></li>
    </ul></li>
    <li><a id="toc-MOV_002fMPEG_002d4_002fISOMBFF-muxers" href="#MOV_002fMPEG_002d4_002fISOMBFF-muxers">4.4 MOV/MPEG-4/ISOMBFF muxers</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Fragmentation" href="#Fragmentation">4.4.1 Fragmentation</a></li>
      <li><a id="toc-Options-6" href="#Options-6">4.4.2 Options</a></li>
      <li><a id="toc-Examples-5" href="#Examples-5">4.4.3 Examples</a></li>
    </ul></li>
    <li><a id="toc-a64" href="#a64-1">4.5 a64</a></li>
    <li><a id="toc-ac4" href="#ac4">4.6 ac4</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-7" href="#Options-7">4.6.1 Options</a></li>
    </ul></li>
    <li><a id="toc-adts" href="#adts-1">4.7 adts</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-8" href="#Options-8">4.7.1 Options</a></li>
    </ul></li>
    <li><a id="toc-aea" href="#aea-1">4.8 aea</a></li>
    <li><a id="toc-aiff" href="#aiff-1">4.9 aiff</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-9" href="#Options-9">4.9.1 Options</a></li>
    </ul></li>
    <li><a id="toc-alp" href="#alp-1">4.10 alp</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-10" href="#Options-10">4.10.1 Options</a></li>
    </ul></li>
    <li><a id="toc-amr" href="#amr">4.11 amr</a></li>
    <li><a id="toc-amv" href="#amv">4.12 amv</a></li>
    <li><a id="toc-apm" href="#apm">4.13 apm</a></li>
    <li><a id="toc-apng-1" href="#apng-1">4.14 apng</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-11" href="#Options-11">4.14.1 Options</a></li>
      <li><a id="toc-Examples-6" href="#Examples-6">4.14.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-argo_005fasf" href="#argo_005fasf">4.15 argo_asf</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-12" href="#Options-12">4.15.1 Options</a></li>
    </ul></li>
    <li><a id="toc-argo_005fcvg" href="#argo_005fcvg">4.16 argo_cvg</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-13" href="#Options-13">4.16.1 Options</a></li>
    </ul></li>
    <li><a id="toc-asf_002c-asf_005fstream" href="#asf_002c-asf_005fstream">4.17 asf, asf_stream</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-14" href="#Options-14">4.17.1 Options</a></li>
    </ul></li>
    <li><a id="toc-ass" href="#ass">4.18 ass</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-15" href="#Options-15">4.18.1 Options</a></li>
    </ul></li>
    <li><a id="toc-ast" href="#ast">4.19 ast</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-16" href="#Options-16">4.19.1 Options</a></li>
    </ul></li>
    <li><a id="toc-au" href="#au">4.20 au</a></li>
    <li><a id="toc-avi" href="#avi-1">4.21 avi</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-17" href="#Options-17">4.21.1 Options</a></li>
    </ul></li>
    <li><a id="toc-avif" href="#avif">4.22 avif</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-18" href="#Options-18">4.22.1 Options</a></li>
    </ul></li>
    <li><a id="toc-avm2" href="#avm2">4.23 avm2</a></li>
    <li><a id="toc-bit" href="#bit">4.24 bit</a></li>
    <li><a id="toc-caf" href="#caf">4.25 caf</a></li>
    <li><a id="toc-codec2" href="#codec2">4.26 codec2</a></li>
    <li><a id="toc-chromaprint" href="#chromaprint-1">4.27 chromaprint</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-19" href="#Options-19">4.27.1 Options</a></li>
    </ul></li>
    <li><a id="toc-crc" href="#crc-1">4.28 crc</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples-7" href="#Examples-7">4.28.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-dash-1" href="#dash-2">4.29 dash</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-20" href="#Options-20">4.29.1 Options</a></li>
      <li><a id="toc-Example" href="#Example">4.29.2 Example</a></li>
    </ul></li>
    <li><a id="toc-daud" href="#daud">4.30 daud</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Example-1" href="#Example-1">4.30.1 Example</a></li>
    </ul></li>
    <li><a id="toc-dv" href="#dv">4.31 dv</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Example-2" href="#Example-2">4.31.1 Example</a></li>
    </ul></li>
    <li><a id="toc-ffmetadata" href="#ffmetadata">4.32 ffmetadata</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Example-3" href="#Example-3">4.32.1 Example</a></li>
    </ul></li>
    <li><a id="toc-fifo" href="#fifo-1">4.33 fifo</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-21" href="#Options-21">4.33.1 Options</a></li>
      <li><a id="toc-Example-4" href="#Example-4">4.33.2 Example</a></li>
    </ul></li>
    <li><a id="toc-film_005fcpk" href="#film_005fcpk">4.34 film_cpk</a></li>
    <li><a id="toc-filmstrip" href="#filmstrip">4.35 filmstrip</a></li>
    <li><a id="toc-fits" href="#fits">4.36 fits</a></li>
    <li><a id="toc-flac" href="#flac">4.37 flac</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-22" href="#Options-22">4.37.1 Options</a></li>
      <li><a id="toc-Example-5" href="#Example-5">4.37.2 Example</a></li>
    </ul></li>
    <li><a id="toc-flv" href="#flv">4.38 flv</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-23" href="#Options-23">4.38.1 Options</a></li>
    </ul></li>
    <li><a id="toc-framecrc" href="#framecrc-1">4.39 framecrc</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples-8" href="#Examples-8">4.39.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-framehash" href="#framehash-1">4.40 framehash</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples-9" href="#Examples-9">4.40.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-framemd5" href="#framemd5-1">4.41 framemd5</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples-10" href="#Examples-10">4.41.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-gif-1" href="#gif-2">4.42 gif</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-24" href="#Options-24">4.42.1 Options</a></li>
      <li><a id="toc-Example-6" href="#Example-6">4.42.2 Example</a></li>
    </ul></li>
    <li><a id="toc-gxf" href="#gxf">4.43 gxf</a></li>
    <li><a id="toc-hash" href="#hash-1">4.44 hash</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples-11" href="#Examples-11">4.44.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-hds" href="#hds-1">4.45 hds</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-25" href="#Options-25">4.45.1 Options</a></li>
      <li><a id="toc-Example-7" href="#Example-7">4.45.2 Example</a></li>
    </ul></li>
    <li><a id="toc-hls-1" href="#hls-2">4.46 hls</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-26" href="#Options-26">4.46.1 Options</a></li>
    </ul></li>
    <li><a id="toc-iamf" href="#iamf">4.47 iamf</a></li>
    <li><a id="toc-ico" href="#ico-1">4.48 ico</a></li>
    <li><a id="toc-ilbc" href="#ilbc">4.49 ilbc</a></li>
    <li><a id="toc-image2_002c-image2pipe" href="#image2_002c-image2pipe">4.50 image2, image2pipe</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-27" href="#Options-27">4.50.1 Options</a></li>
      <li><a id="toc-Examples-12" href="#Examples-12">4.50.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-ircam" href="#ircam">4.51 ircam</a></li>
    <li><a id="toc-ivf" href="#ivf">4.52 ivf</a></li>
    <li><a id="toc-jacosub" href="#jacosub">4.53 jacosub</a></li>
    <li><a id="toc-kvag" href="#kvag">4.54 kvag</a></li>
    <li><a id="toc-lc3" href="#lc3">4.55 lc3</a></li>
    <li><a id="toc-lrc" href="#lrc">4.56 lrc</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Metadata" href="#Metadata">4.56.1 Metadata</a></li>
    </ul></li>
    <li><a id="toc-matroska" href="#matroska">4.57 matroska</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Metadata-1" href="#Metadata-1">4.57.1 Metadata</a></li>
      <li><a id="toc-Options-28" href="#Options-28">4.57.2 Options</a></li>
    </ul></li>
    <li><a id="toc-md5" href="#md5-1">4.58 md5</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples-13" href="#Examples-13">4.58.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-microdvd" href="#microdvd">4.59 microdvd</a></li>
    <li><a id="toc-mmf" href="#mmf">4.60 mmf</a></li>
    <li><a id="toc-mp3" href="#mp3">4.61 mp3</a></li>
    <li><a id="toc-mpegts-1" href="#mpegts-1">4.62 mpegts</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-29" href="#Options-29">4.62.1 Options</a></li>
      <li><a id="toc-Example-8" href="#Example-8">4.62.2 Example</a></li>
    </ul></li>
    <li><a id="toc-mxf_002c-mxf_005fd10_002c-mxf_005fopatom" href="#mxf_002c-mxf_005fd10_002c-mxf_005fopatom">4.63 mxf, mxf_d10, mxf_opatom</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-30" href="#Options-30">4.63.1 Options</a></li>
    </ul></li>
    <li><a id="toc-null" href="#null">4.64 null</a></li>
    <li><a id="toc-nut" href="#nut">4.65 nut</a></li>
    <li><a id="toc-ogg" href="#ogg">4.66 ogg</a></li>
    <li><a id="toc-rcwt-1" href="#rcwt-1">4.67 rcwt</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples-14" href="#Examples-14">4.67.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-segment_002c-stream_005fsegment_002c-ssegment" href="#segment_002c-stream_005fsegment_002c-ssegment">4.68 segment, stream_segment, ssegment</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-31" href="#Options-31">4.68.1 Options</a></li>
      <li><a id="toc-Examples-15" href="#Examples-15">4.68.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-smoothstreaming" href="#smoothstreaming">4.69 smoothstreaming</a></li>
    <li><a id="toc-streamhash" href="#streamhash-1">4.70 streamhash</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Examples-16" href="#Examples-16">4.70.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-tee" href="#tee-1">4.71 tee</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-32" href="#Options-32">4.71.1 Options</a></li>
      <li><a id="toc-Examples-17" href="#Examples-17">4.71.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-webm_005fchunk" href="#webm_005fchunk">4.72 webm_chunk</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-33" href="#Options-33">4.72.1 Options</a></li>
      <li><a id="toc-Example-9" href="#Example-9">4.72.2 Example</a></li>
    </ul></li>
    <li><a id="toc-webm_005fdash_005fmanifest" href="#webm_005fdash_005fmanifest">4.73 webm_dash_manifest</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-34" href="#Options-34">4.73.1 Options</a></li>
      <li><a id="toc-Example-10" href="#Example-10">4.73.2 Example</a></li>
    </ul></li>
    <li><a id="toc-whip" href="#whip-1">4.74 whip</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-35" href="#Options-35">4.74.1 Options</a></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-Metadata-2" href="#Metadata-2">5 Metadata</a></li>
  <li><a id="toc-See-Also" href="#See-Also">6 See Also</a></li>
  <li><a id="toc-Authors" href="#Authors">7 Authors</a></li>
</ul>
</div>
</div>

<a name="Description"></a>
<h2 class="chapter">1 Description<span class="pull-right"><a class="anchor hidden-xs" href="#Description" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Description" aria-hidden="true">TOC</a></span></h2>

<p>This document describes the supported formats (muxers and demuxers)
provided by the libavformat library.
</p>

<a name="Format-Options"></a>
<h2 class="chapter">2 Format Options<span class="pull-right"><a class="anchor hidden-xs" href="#Format-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Format-Options" aria-hidden="true">TOC</a></span></h2>

<p>The libavformat library provides some generic global options, which
can be set on all the muxers and demuxers. In addition each muxer or
demuxer may support so-called private options, which are specific for
that component.
</p>
<p>Options may be set by specifying -<var class="var">option</var> <var class="var">value</var> in the
FFmpeg tools, or by setting the value explicitly in the
<code class="code">AVFormatContext</code> options or using the <samp class="file">libavutil/opt.h</samp> API
for programmatic use.
</p>
<p>The list of supported options follows:
</p>
<dl class="table">
<dt><samp class="option">avioflags <var class="var">flags</var> (<em class="emph">input/output</em>)</samp></dt>
<dd><p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">direct</samp>&rsquo;</dt>
<dd><p>Reduce buffering.
</p></dd>
</dl>

</dd>
<dt><samp class="option">probesize <var class="var">integer</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Set probing size in bytes, i.e. the size of the data to analyze to get
stream information. A higher value will enable detecting more
information in case it is dispersed into the stream, but will increase
latency. Must be an integer not lesser than 32. It is 5000000 by default.
</p>
</dd>
<dt><samp class="option">max_probe_packets <var class="var">integer</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Set the maximum number of buffered packets when probing a codec.
Default is 2500 packets.
</p>
</dd>
<dt><samp class="option">packetsize <var class="var">integer</var> (<em class="emph">output</em>)</samp></dt>
<dd><p>Set packet size.
</p>
</dd>
<dt><samp class="option">fflags <var class="var">flags</var></samp></dt>
<dd><p>Set format flags. Some are implemented for a limited number of formats.
</p>
<p>Possible values for input files:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">discardcorrupt</samp>&rsquo;</dt>
<dd><p>Discard corrupted packets.
</p></dd>
<dt>&lsquo;<samp class="samp">fastseek</samp>&rsquo;</dt>
<dd><p>Enable fast, but inaccurate seeks for some formats.
</p></dd>
<dt>&lsquo;<samp class="samp">genpts</samp>&rsquo;</dt>
<dd><p>Generate missing PTS if DTS is present.
</p></dd>
<dt>&lsquo;<samp class="samp">igndts</samp>&rsquo;</dt>
<dd><p>Ignore DTS if PTS is also set. In case the PTS is set, the DTS value
is set to NOPTS. This is ignored when the <code class="code">nofillin</code> flag is set.
</p></dd>
<dt>&lsquo;<samp class="samp">ignidx</samp>&rsquo;</dt>
<dd><p>Ignore index.
</p></dd>
<dt>&lsquo;<samp class="samp">nobuffer</samp>&rsquo;</dt>
<dd><p>Reduce the latency introduced by buffering during initial input streams analysis.
</p></dd>
<dt>&lsquo;<samp class="samp">nofillin</samp>&rsquo;</dt>
<dd><p>Do not fill in missing values in packet fields that can be exactly calculated.
</p></dd>
<dt>&lsquo;<samp class="samp">noparse</samp>&rsquo;</dt>
<dd><p>Disable AVParsers, this needs <code class="code">+nofillin</code> too.
</p></dd>
<dt>&lsquo;<samp class="samp">sortdts</samp>&rsquo;</dt>
<dd><p>Try to interleave output packets by DTS. At present, available only for AVIs with an index.
</p></dd>
</dl>

<p>Possible values for output files:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">autobsf</samp>&rsquo;</dt>
<dd><p>Automatically apply bitstream filters as required by the output format. Enabled by default.
</p></dd>
<dt>&lsquo;<samp class="samp">bitexact</samp>&rsquo;</dt>
<dd><p>Only write platform-, build- and time-independent data.
This ensures that file and data checksums are reproducible and match between
platforms. Its primary use is for regression testing.
</p></dd>
<dt>&lsquo;<samp class="samp">flush_packets</samp>&rsquo;</dt>
<dd><p>Write out packets immediately.
</p></dd>
<dt>&lsquo;<samp class="samp">shortest</samp>&rsquo;</dt>
<dd><p>Stop muxing at the end of the shortest stream.
It may be needed to increase max_interleave_delta to avoid flushing the longer
streams before EOF.
</p></dd>
</dl>

</dd>
<dt><samp class="option">seek2any <var class="var">integer</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Allow seeking to non-keyframes on demuxer level when supported if set to 1.
Default is 0.
</p>
</dd>
<dt><samp class="option">analyzeduration <var class="var">integer</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Specify how many microseconds are analyzed to probe the input. A
higher value will enable detecting more accurate information, but will
increase latency. It defaults to 5,000,000 microseconds = 5 seconds.
</p>
</dd>
<dt><samp class="option">cryptokey <var class="var">hexadecimal string</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Set decryption key.
</p>
</dd>
<dt><samp class="option">indexmem <var class="var">integer</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Set max memory used for timestamp index (per stream).
</p>
</dd>
<dt><samp class="option">rtbufsize <var class="var">integer</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Set max memory used for buffering real-time frames.
</p>
</dd>
<dt><samp class="option">fdebug <var class="var">flags</var> (<em class="emph">input/output</em>)</samp></dt>
<dd><p>Print specific debug info.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">ts</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">max_delay <var class="var">integer</var> (<em class="emph">input/output</em>)</samp></dt>
<dd><p>Set maximum muxing or demuxing delay in microseconds.
</p>
</dd>
<dt><samp class="option">fpsprobesize <var class="var">integer</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Set number of frames used to probe fps.
</p>
</dd>
<dt><samp class="option">audio_preload <var class="var">integer</var> (<em class="emph">output</em>)</samp></dt>
<dd><p>Set microseconds by which audio packets should be interleaved earlier.
</p>
</dd>
<dt><samp class="option">chunk_duration <var class="var">integer</var> (<em class="emph">output</em>)</samp></dt>
<dd><p>Set microseconds for each chunk.
</p>
</dd>
<dt><samp class="option">chunk_size <var class="var">integer</var> (<em class="emph">output</em>)</samp></dt>
<dd><p>Set size in bytes for each chunk.
</p>
</dd>
<dt><samp class="option">err_detect, f_err_detect <var class="var">flags</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Set error detection flags. <code class="code">f_err_detect</code> is deprecated and
should be used only via the <code class="command">ffmpeg</code> tool.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">crccheck</samp>&rsquo;</dt>
<dd><p>Verify embedded CRCs.
</p></dd>
<dt>&lsquo;<samp class="samp">bitstream</samp>&rsquo;</dt>
<dd><p>Detect bitstream specification deviations.
</p></dd>
<dt>&lsquo;<samp class="samp">buffer</samp>&rsquo;</dt>
<dd><p>Detect improper bitstream length.
</p></dd>
<dt>&lsquo;<samp class="samp">explode</samp>&rsquo;</dt>
<dd><p>Abort decoding on minor error detection.
</p></dd>
<dt>&lsquo;<samp class="samp">careful</samp>&rsquo;</dt>
<dd><p>Consider things that violate the spec and have not been seen in the
wild as errors.
</p></dd>
<dt>&lsquo;<samp class="samp">compliant</samp>&rsquo;</dt>
<dd><p>Consider all spec non compliancies as errors.
</p></dd>
<dt>&lsquo;<samp class="samp">aggressive</samp>&rsquo;</dt>
<dd><p>Consider things that a sane encoder should not do as an error.
</p></dd>
</dl>

</dd>
<dt><samp class="option">max_interleave_delta <var class="var">integer</var> (<em class="emph">output</em>)</samp></dt>
<dd><p>Set maximum buffering duration for interleaving. The duration is
expressed in microseconds, and defaults to 10000000 (10 seconds).
</p>
<p>To ensure all the streams are interleaved correctly, libavformat will
wait until it has at least one packet for each stream before actually
writing any packets to the output file. When some streams are
&quot;sparse&quot; (i.e. there are large gaps between successive packets), this
can result in excessive buffering.
</p>
<p>This field specifies the maximum difference between the timestamps of the
first and the last packet in the muxing queue, above which libavformat
will output a packet regardless of whether it has queued a packet for all
the streams.
</p>
<p>If set to 0, libavformat will continue buffering packets until it has
a packet for each stream, regardless of the maximum timestamp
difference between the buffered packets.
</p>
</dd>
<dt><samp class="option">use_wallclock_as_timestamps <var class="var">integer</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Use wallclock as timestamps if set to 1. Default is 0.
</p>
</dd>
<dt><samp class="option">avoid_negative_ts <var class="var">integer</var> (<em class="emph">output</em>)</samp></dt>
<dd>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">make_non_negative</samp>&rsquo;</dt>
<dd><p>Shift timestamps to make them non-negative.
Also note that this affects only leading negative timestamps, and not
non-monotonic negative timestamps.
</p></dd>
<dt>&lsquo;<samp class="samp">make_zero</samp>&rsquo;</dt>
<dd><p>Shift timestamps so that the first timestamp is 0.
</p></dd>
<dt>&lsquo;<samp class="samp">auto (default)</samp>&rsquo;</dt>
<dd><p>Enables shifting when required by the target format.
</p></dd>
<dt>&lsquo;<samp class="samp">disabled</samp>&rsquo;</dt>
<dd><p>Disables shifting of timestamp.
</p></dd>
</dl>

<p>When shifting is enabled, all output timestamps are shifted by the
same amount. Audio, video, and subtitles desynching and relative
timestamp differences are preserved compared to how they would have
been without shifting.
</p>
</dd>
<dt><samp class="option">skip_initial_bytes <var class="var">integer</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Set number of bytes to skip before reading header and frames if set to 1.
Default is 0.
</p>
</dd>
<dt><samp class="option">correct_ts_overflow <var class="var">integer</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Correct single timestamp overflows if set to 1. Default is 1.
</p>
</dd>
<dt><samp class="option">flush_packets <var class="var">integer</var> (<em class="emph">output</em>)</samp></dt>
<dd><p>Flush the underlying I/O stream after each packet. Default is -1 (auto), which
means that the underlying protocol will decide, 1 enables it, and has the
effect of reducing the latency, 0 disables it and may increase IO throughput in
some cases.
</p>
</dd>
<dt><samp class="option">output_ts_offset <var class="var">offset</var> (<em class="emph">output</em>)</samp></dt>
<dd><p>Set the output time offset.
</p>
<p><var class="var">offset</var> must be a time duration specification,
see <a data-manual="ffmpeg-utils" href="./ffmpeg-utils.html#time-duration-syntax">the Time duration section in the ffmpeg-utils(1) manual</a>.
</p>
<p>The offset is added by the muxer to the output timestamps.
</p>
<p>Specifying a positive offset means that the corresponding streams are
delayed bt the time duration specified in <var class="var">offset</var>. Default value
is <code class="code">0</code> (meaning that no offset is applied).
</p>
</dd>
<dt><samp class="option">format_whitelist <var class="var">list</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>&quot;,&quot; separated list of allowed demuxers. By default all are allowed.
</p>
</dd>
<dt><samp class="option">dump_separator <var class="var">string</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Separator used to separate the fields printed on the command line about the
Stream parameters.
For example, to separate the fields with newlines and indentation:
</p><div class="example">
<pre class="example-preformatted">ffprobe -dump_separator &quot;
                          &quot;  -i ~/videos/matrixbench_mpeg2.mpg
</pre></div>

</dd>
<dt><samp class="option">max_streams <var class="var">integer</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Specifies the maximum number of streams. This can be used to reject files that
would require too many resources due to a large number of streams.
</p>
</dd>
<dt><samp class="option">skip_estimate_duration_from_pts <var class="var">bool</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Skip estimation of input duration if it requires an additional probing for PTS at end of file.
At present, applicable for MPEG-PS and MPEG-TS.
</p>
</dd>
<dt><samp class="option">duration_probesize <var class="var">integer</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Set probing size, in bytes, for input duration estimation when it actually requires
an additional probing for PTS at end of file (at present: MPEG-PS and MPEG-TS).
It is aimed at users interested in better durations probing for itself, or indirectly
because using the concat demuxer, for example.
The typical use case is an MPEG-TS CBR with a high bitrate, high video buffering and
ending cleaning with similar PTS for video and audio: in such a scenario, the large
physical gap between the last video packet and the last audio packet makes it necessary
to read many bytes in order to get the video stream duration.
Another use case is where the default probing behaviour only reaches a single video frame which is
not the last one of the stream due to frame reordering, so the duration is not accurate.
Setting this option has a performance impact even for small files because the probing
size is fixed.
Default behaviour is a general purpose trade-off, largely adaptive, but the probing size
will not be extended to get streams durations at all costs.
Must be an integer not lesser than 1, or 0 for default behaviour.
</p>
</dd>
<dt><samp class="option">strict, f_strict <var class="var">integer</var> (<em class="emph">input/output</em>)</samp></dt>
<dd><p>Specify how strictly to follow the standards. <code class="code">f_strict</code> is deprecated and
should be used only via the <code class="command">ffmpeg</code> tool.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">very</samp>&rsquo;</dt>
<dd><p>strictly conform to an older more strict version of the spec or reference software
</p></dd>
<dt>&lsquo;<samp class="samp">strict</samp>&rsquo;</dt>
<dd><p>strictly conform to all the things in the spec no matter what consequences
</p></dd>
<dt>&lsquo;<samp class="samp">normal</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">unofficial</samp>&rsquo;</dt>
<dd><p>allow unofficial extensions
</p></dd>
<dt>&lsquo;<samp class="samp">experimental</samp>&rsquo;</dt>
<dd><p>allow non standardized experimental things, experimental
(unfinished/work in progress/not well tested) decoders and encoders.
Note: experimental decoders can pose a security risk, do not use this for
decoding untrusted input.
</p></dd>
</dl>

</dd>
</dl>


<a class="anchor" id="Format-stream-specifiers"></a><a name="Format-stream-specifiers-1"></a>
<h3 class="section">2.1 Format stream specifiers<span class="pull-right"><a class="anchor hidden-xs" href="#Format-stream-specifiers" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Format-stream-specifiers" aria-hidden="true">TOC</a></span></h3>

<p>Format stream specifiers allow selection of one or more streams that
match specific properties.
</p>
<p>The exact semantics of stream specifiers is defined by the
<code class="code">avformat_match_stream_specifier()</code> function declared in the
<samp class="file">libavformat/avformat.h</samp> header and documented in the
<a data-manual="ffmpeg" href="./ffmpeg.html#Stream-specifiers">Stream specifiers section in the ffmpeg(1) manual</a>.
</p>
<a name="Demuxers"></a>
<h2 class="chapter">3 Demuxers<span class="pull-right"><a class="anchor hidden-xs" href="#Demuxers" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Demuxers" aria-hidden="true">TOC</a></span></h2>

<p>Demuxers are configured elements in FFmpeg that can read the
multimedia streams from a particular type of file.
</p>
<p>When you configure your FFmpeg build, all the supported demuxers
are enabled by default. You can list all available ones using the
configure option <code class="code">--list-demuxers</code>.
</p>
<p>You can disable all the demuxers using the configure option
<code class="code">--disable-demuxers</code>, and selectively enable a single demuxer with
the option <code class="code">--enable-demuxer=<var class="var">DEMUXER</var></code>, or disable it
with the option <code class="code">--disable-demuxer=<var class="var">DEMUXER</var></code>.
</p>
<p>The option <code class="code">-demuxers</code> of the ff* tools will display the list of
enabled demuxers. Use <code class="code">-formats</code> to view a combined list of
enabled demuxers and muxers.
</p>
<p>The description of some of the currently available demuxers follows.
</p>
<a name="aa"></a>
<h3 class="section">3.1 aa<span class="pull-right"><a class="anchor hidden-xs" href="#aa" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-aa" aria-hidden="true">TOC</a></span></h3>

<p>Audible Format 2, 3, and 4 demuxer.
</p>
<p>This demuxer is used to demux Audible Format 2, 3, and 4 (.aa) files.
</p>
<a name="aac"></a>
<h3 class="section">3.2 aac<span class="pull-right"><a class="anchor hidden-xs" href="#aac" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-aac" aria-hidden="true">TOC</a></span></h3>

<p>Raw Audio Data Transport Stream AAC demuxer.
</p>
<p>This demuxer is used to demux an ADTS input containing a single AAC stream
alongwith any ID3v1/2 or APE tags in it.
</p>
<a name="apng"></a>
<h3 class="section">3.3 apng<span class="pull-right"><a class="anchor hidden-xs" href="#apng" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-apng" aria-hidden="true">TOC</a></span></h3>

<p>Animated Portable Network Graphics demuxer.
</p>
<p>This demuxer is used to demux APNG files.
All headers, but the PNG signature, up to (but not including) the first
fcTL chunk are transmitted as extradata.
Frames are then split as being all the chunks between two fcTL ones, or
between the last fcTL and IEND chunks.
</p>
<dl class="table">
<dt><samp class="option">-ignore_loop <var class="var">bool</var></samp></dt>
<dd><p>Ignore the loop variable in the file if set. Default is enabled.
</p>
</dd>
<dt><samp class="option">-max_fps <var class="var">int</var></samp></dt>
<dd><p>Maximum framerate in frames per second. Default of 0 imposes no limit.
</p>
</dd>
<dt><samp class="option">-default_fps <var class="var">int</var></samp></dt>
<dd><p>Default framerate in frames per second when none is specified in the file
(0 meaning as fast as possible). Default is 15.
</p>
</dd>
</dl>

<a name="asf-1"></a>
<h3 class="section">3.4 asf<span class="pull-right"><a class="anchor hidden-xs" href="#asf" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-asf" aria-hidden="true">TOC</a></span></h3>

<p>Advanced Systems Format demuxer.
</p>
<p>This demuxer is used to demux ASF files and MMS network streams.
</p>
<dl class="table">
<dt><samp class="option">-no_resync_search <var class="var">bool</var></samp></dt>
<dd><p>Do not try to resynchronize by looking for a certain optional start code.
</p></dd>
</dl>

<a class="anchor" id="concat"></a><a name="concat-1"></a>
<h3 class="section">3.5 concat<span class="pull-right"><a class="anchor hidden-xs" href="#concat" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-concat" aria-hidden="true">TOC</a></span></h3>

<p>Virtual concatenation script demuxer.
</p>
<p>This demuxer reads a list of files and other directives from a text file and
demuxes them one after the other, as if all their packets had been muxed
together.
</p>
<p>The timestamps in the files are adjusted so that the first file starts at 0
and each next file starts where the previous one finishes. Note that it is
done globally and may cause gaps if all streams do not have exactly the same
length.
</p>
<p>All files must have the same streams (same codecs, same time base, etc.).
</p>
<p>The duration of each file is used to adjust the timestamps of the next file:
if the duration is incorrect (because it was computed using the bit-rate or
because the file is truncated, for example), it can cause artifacts. The
<code class="code">duration</code> directive can be used to override the duration stored in
each file.
</p>
<a name="Syntax"></a>
<h4 class="subsection">3.5.1 Syntax<span class="pull-right"><a class="anchor hidden-xs" href="#Syntax" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Syntax" aria-hidden="true">TOC</a></span></h4>

<p>The script is a text file in extended-ASCII, with one directive per line.
Empty lines, leading spaces and lines starting with &rsquo;#&rsquo; are ignored. The
following directive is recognized:
</p>
<dl class="table">
<dt><samp class="option"><code class="code">file <var class="var">path</var></code></samp></dt>
<dd><p>Path to a file to read; special characters and spaces must be escaped with
backslash or single quotes.
</p>
<p>All subsequent file-related directives apply to that file.
</p>
</dd>
<dt><samp class="option"><code class="code">ffconcat version 1.0</code></samp></dt>
<dd><p>Identify the script type and version.
</p>
<p>To make FFmpeg recognize the format automatically, this directive must
appear exactly as is (no extra space or byte-order-mark) on the very first
line of the script.
</p>
</dd>
<dt><samp class="option"><code class="code">duration <var class="var">dur</var></code></samp></dt>
<dd><p>Duration of the file. This information can be specified from the file;
specifying it here may be more efficient or help if the information from the
file is not available or accurate.
</p>
<p>If the duration is set for all files, then it is possible to seek in the
whole concatenated video.
</p>
</dd>
<dt><samp class="option"><code class="code">inpoint <var class="var">timestamp</var></code></samp></dt>
<dd><p>In point of the file. When the demuxer opens the file it instantly seeks to the
specified timestamp. Seeking is done so that all streams can be presented
successfully at In point.
</p>
<p>This directive works best with intra frame codecs, because for non-intra frame
ones you will usually get extra packets before the actual In point and the
decoded content will most likely contain frames before In point too.
</p>
<p>For each file, packets before the file In point will have timestamps less than
the calculated start timestamp of the file (negative in case of the first
file), and the duration of the files (if not specified by the <code class="code">duration</code>
directive) will be reduced based on their specified In point.
</p>
<p>Because of potential packets before the specified In point, packet timestamps
may overlap between two concatenated files.
</p>
</dd>
<dt><samp class="option"><code class="code">outpoint <var class="var">timestamp</var></code></samp></dt>
<dd><p>Out point of the file. When the demuxer reaches the specified decoding
timestamp in any of the streams, it handles it as an end of file condition and
skips the current and all the remaining packets from all streams.
</p>
<p>Out point is exclusive, which means that the demuxer will not output packets
with a decoding timestamp greater or equal to Out point.
</p>
<p>This directive works best with intra frame codecs and formats where all streams
are tightly interleaved. For non-intra frame codecs you will usually get
additional packets with presentation timestamp after Out point therefore the
decoded content will most likely contain frames after Out point too. If your
streams are not tightly interleaved you may not get all the packets from all
streams before Out point and you may only will be able to decode the earliest
stream until Out point.
</p>
<p>The duration of the files (if not specified by the <code class="code">duration</code>
directive) will be reduced based on their specified Out point.
</p>
</dd>
<dt><samp class="option"><code class="code">file_packet_metadata <var class="var">key=value</var></code></samp></dt>
<dd><p>Metadata of the packets of the file. The specified metadata will be set for
each file packet. You can specify this directive multiple times to add multiple
metadata entries.
This directive is deprecated, use <code class="code">file_packet_meta</code> instead.
</p>
</dd>
<dt><samp class="option"><code class="code">file_packet_meta <var class="var">key</var> <var class="var">value</var></code></samp></dt>
<dd><p>Metadata of the packets of the file. The specified metadata will be set for
each file packet. You can specify this directive multiple times to add multiple
metadata entries.
</p>
</dd>
<dt><samp class="option"><code class="code">option <var class="var">key</var> <var class="var">value</var></code></samp></dt>
<dd><p>Option to access, open and probe the file.
Can be present multiple times.
</p>
</dd>
<dt><samp class="option"><code class="code">stream</code></samp></dt>
<dd><p>Introduce a stream in the virtual file.
All subsequent stream-related directives apply to the last introduced
stream.
Some streams properties must be set in order to allow identifying the
matching streams in the subfiles.
If no streams are defined in the script, the streams from the first file are
copied.
</p>
</dd>
<dt><samp class="option"><code class="code">exact_stream_id <var class="var">id</var></code></samp></dt>
<dd><p>Set the id of the stream.
If this directive is given, the string with the corresponding id in the
subfiles will be used.
This is especially useful for MPEG-PS (VOB) files, where the order of the
streams is not reliable.
</p>
</dd>
<dt><samp class="option"><code class="code">stream_meta <var class="var">key</var> <var class="var">value</var></code></samp></dt>
<dd><p>Metadata for the stream.
Can be present multiple times.
</p>
</dd>
<dt><samp class="option"><code class="code">stream_codec <var class="var">value</var></code></samp></dt>
<dd><p>Codec for the stream.
</p>
</dd>
<dt><samp class="option"><code class="code">stream_extradata <var class="var">hex_string</var></code></samp></dt>
<dd><p>Extradata for the string, encoded in hexadecimal.
</p>
</dd>
<dt><samp class="option"><code class="code">chapter <var class="var">id</var> <var class="var">start</var> <var class="var">end</var></code></samp></dt>
<dd><p>Add a chapter. <var class="var">id</var> is an unique identifier, possibly small and
consecutive.
</p>
</dd>
</dl>

<a name="Options"></a>
<h4 class="subsection">3.5.2 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options" aria-hidden="true">TOC</a></span></h4>

<p>This demuxer accepts the following option:
</p>
<dl class="table">
<dt><samp class="option">safe</samp></dt>
<dd><p>If set to 1, reject unsafe file paths and directives.
A file path is considered safe if it
does not contain a protocol specification and is relative and all components
only contain characters from the portable character set (letters, digits,
period, underscore and hyphen) and have no period at the beginning of a
component.
</p>
<p>If set to 0, any file name is accepted.
</p>
<p>The default is 1.
</p>
</dd>
<dt><samp class="option">auto_convert</samp></dt>
<dd><p>If set to 1, try to perform automatic conversions on packet data to make the
streams concatenable.
The default is 1.
</p>
<p>Currently, the only conversion is adding the h264_mp4toannexb bitstream
filter to H.264 streams in MP4 format. This is necessary in particular if
there are resolution changes.
</p>
</dd>
<dt><samp class="option">segment_time_metadata</samp></dt>
<dd><p>If set to 1, every packet will contain the <var class="var">lavf.concat.start_time</var> and the
<var class="var">lavf.concat.duration</var> packet metadata values which are the start_time and
the duration of the respective file segments in the concatenated output
expressed in microseconds. The duration metadata is only set if it is known
based on the concat file.
The default is 0.
</p>
</dd>
</dl>

<a name="Examples"></a>
<h4 class="subsection">3.5.3 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Use absolute filenames and include some comments:
<div class="example">
<pre class="example-preformatted"># my first filename
file /mnt/share/file-1.wav
# my second filename including whitespace
file '/mnt/share/file 2.wav'
# my third filename including whitespace plus single quote
file '/mnt/share/file 3'\''.wav'
</pre></div>

</li><li>Allow for input format auto-probing, use safe filenames and set the duration of
the first file:
<div class="example">
<pre class="example-preformatted">ffconcat version 1.0

file file-1.wav
duration 20.0

file subdir/file-2.wav
</pre></div>
</li></ul>

<a name="dash-1"></a>
<h3 class="section">3.6 dash<span class="pull-right"><a class="anchor hidden-xs" href="#dash" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dash" aria-hidden="true">TOC</a></span></h3>

<p>Dynamic Adaptive Streaming over HTTP demuxer.
</p>
<p>This demuxer presents all AVStreams found in the manifest.
By setting the discard flags on AVStreams the caller can decide
which streams to actually receive.
Each stream mirrors the <code class="code">id</code> and <code class="code">bandwidth</code> properties from the
<code class="code">&lt;Representation&gt;</code> as metadata keys named &quot;id&quot; and &quot;variant_bitrate&quot; respectively.
</p>
<a name="Options-1"></a>
<h4 class="subsection">3.6.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-1" aria-hidden="true">TOC</a></span></h4>

<p>This demuxer accepts the following option:
</p>
<dl class="table">
<dt><samp class="option">cenc_decryption_key</samp></dt>
<dd><p>16-byte key, in hex, to decrypt files encrypted using ISO Common Encryption (CENC/AES-128 CTR; ISO/IEC 23001-7).
</p>
</dd>
</dl>

<a name="dvdvideo"></a>
<h3 class="section">3.7 dvdvideo<span class="pull-right"><a class="anchor hidden-xs" href="#dvdvideo" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dvdvideo" aria-hidden="true">TOC</a></span></h3>

<p>DVD-Video demuxer, powered by libdvdnav and libdvdread.
</p>
<p>Can directly ingest DVD titles, specifically sequential PGCs, into
a conversion pipeline. Menu assets, such as background video or audio,
can also be demuxed given the menu&rsquo;s coordinates (at best effort).
</p>
<p>Block devices (DVD drives), ISO files, and directory structures are accepted.
Activate with <code class="code">-f dvdvideo</code> in front of one of these inputs.
</p>
<p>This demuxer does NOT have decryption code of any kind. You are on your own
working with encrypted DVDs, and should not expect support on the matter.
</p>
<p>Underlying playback is handled by libdvdnav, and structure parsing by libdvdread.
FFmpeg must be built with GPL library support available as well as the
configure switches <code class="code">--enable-libdvdnav</code> and <code class="code">--enable-libdvdread</code>.
</p>
<p>You will need to provide either the desired &quot;title number&quot; or exact PGC/PG coordinates.
Many open-source DVD players and tools can aid in providing this information.
If not specified, the demuxer will default to title 1 which works for many discs.
However, due to the flexibility of the format, it is recommended to check manually.
There are many discs that are authored strangely or with invalid headers.
</p>
<p>If the input is a real DVD drive, please note that there are some drives which may
silently fail on reading bad sectors from the disc, returning random bits instead
which is effectively corrupt data. This is especially prominent on aging or rotting discs.
A second pass and integrity checks would be needed to detect the corruption.
This is not an FFmpeg issue.
</p>
<a name="Background"></a>
<h4 class="subsection">3.7.1 Background<span class="pull-right"><a class="anchor hidden-xs" href="#Background" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Background" aria-hidden="true">TOC</a></span></h4>

<p>DVD-Video is not a directly accessible, linear container format in the
traditional sense. Instead, it allows for complex and programmatic playback of
carefully muxed MPEG-PS streams that are stored in headerless VOB files.
To the end-user, these streams are known simply as &quot;titles&quot;, but the actual
logical playback sequence is defined by one or more &quot;PGCs&quot;, or Program Group Chains,
within the title. The PGC is in turn comprised of multiple &quot;PGs&quot;, or Programs&quot;,
which are the actual video segments (and for a typical video feature, sequentially
ordered). The PGC structure, along with stream layout and metadata, are stored in
IFO files that need to be parsed. PGCs can be thought of as playlists in easier terms.
</p>
<p>An actual DVD player relies on user GUI interaction via menus and an internal VM
to drive the direction of demuxing. Generally, the user would either navigate (via menus)
or automatically be redirected to the PGC of their choice. During this process and
the subsequent playback, the DVD player&rsquo;s internal VM also maintains a state and
executes instructions that can create jumps to different sectors during playback.
This is why libdvdnav is involved, as a linear read of the MPEG-PS blobs on the
disc (VOBs) is not enough to produce the right sequence in many cases.
</p>
<p>There are many other DVD structures (a long subject) that will not be discussed here.
NAV packets, in particular, are handled by this demuxer to build accurate timing
but not emitted as a stream. For a good high-level understanding, refer to:
<a class="url" href="https://code.videolan.org/videolan/libdvdnav/-/blob/master/doc/dvd_structures">https://code.videolan.org/videolan/libdvdnav/-/blob/master/doc/dvd_structures</a>
</p>
<a name="Options-2"></a>
<h4 class="subsection">3.7.2 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-2" aria-hidden="true">TOC</a></span></h4>

<p>This demuxer accepts the following options:
</p>
<dl class="table">
<dt><samp class="option">title <var class="var">int</var></samp></dt>
<dd><p>The title number to play. Must be set if <samp class="option">pgc</samp> and <samp class="option">pg</samp> are not set.
Not applicable to menus.
Default is 0 (auto), which currently only selects the first available title (title 1)
and notifies the user about the implications.
</p>
</dd>
<dt><samp class="option">chapter_start <var class="var">int</var></samp></dt>
<dd><p>The chapter, or PTT (part-of-title), number to start at. Not applicable to menus.
Default is 1.
</p>
</dd>
<dt><samp class="option">chapter_end <var class="var">int</var></samp></dt>
<dd><p>The chapter, or PTT (part-of-title), number to end at. Not applicable to menus.
Default is 0, which is a special value to signal end at the last possible chapter.
</p>
</dd>
<dt><samp class="option">angle <var class="var">int</var></samp></dt>
<dd><p>The video angle number, referring to what is essentially an additional
video stream that is composed from alternate frames interleaved in the VOBs.
Not applicable to menus.
Default is 1.
</p>
</dd>
<dt><samp class="option">region <var class="var">int</var></samp></dt>
<dd><p>The region code to use for playback. Some discs may use this to default playback
at a particular angle in different regions. This option will not affect the region code
of a real DVD drive, if used as an input. Not applicable to menus.
Default is 0, &quot;world&quot;.
</p>
</dd>
<dt><samp class="option">menu <var class="var">bool</var></samp></dt>
<dd><p>Demux menu assets instead of navigating a title. Requires exact coordinates
of the menu (<samp class="option">menu_lu</samp>, <samp class="option">menu_vts</samp>, <samp class="option">pgc</samp>, <samp class="option">pg</samp>).
Default is false.
</p>
</dd>
<dt><samp class="option">menu_lu <var class="var">int</var></samp></dt>
<dd><p>The menu language to demux. In DVD, menus are grouped by language.
Default is 1, the first language unit.
</p>
</dd>
<dt><samp class="option">menu_vts <var class="var">int</var></samp></dt>
<dd><p>The VTS where the menu lives, or 0 if it is a VMG menu (root-level).
Default is 1, menu of the first VTS.
</p>
</dd>
<dt><samp class="option">pgc <var class="var">int</var></samp></dt>
<dd><p>The entry PGC to start playback, in conjunction with <samp class="option">pg</samp>.
Alternative to setting <samp class="option">title</samp>.
Chapter markers are not supported at this time.
Must be explicitly set for menus.
Default is 0, automatically resolve from value of <samp class="option">title</samp>.
</p>
</dd>
<dt><samp class="option">pg <var class="var">int</var></samp></dt>
<dd><p>The entry PG to start playback, in conjunction with <samp class="option">pgc</samp>.
Alternative to setting <samp class="option">title</samp>.
Chapter markers are not supported at this time.
Default is 1, the first PG of the PGC.
</p>
</dd>
<dt><samp class="option">preindex <var class="var">bool</var></samp></dt>
<dd><p>Enable this to have accurate chapter (PTT) markers and duration measurement,
which requires a slow second pass read in order to index the chapter marker
timestamps from NAV packets. This is non-ideal extra work for real optical drives.
It is recommended and faster to use this option with a backup of the DVD structure
stored on a hard drive. Not compatible with <samp class="option">pgc</samp> and <samp class="option">pg</samp>.
Default is 0, false.
</p>
</dd>
<dt><samp class="option">trim <var class="var">bool</var></samp></dt>
<dd><p>Skip padding cells (i.e. cells shorter than 1 second) from the beginning.
There exist many discs with filler segments at the beginning of the PGC,
often with junk data intended for controlling a real DVD player&rsquo;s
buffering speed and with no other material data value.
Not applicable to menus.
Default is 1, true.
</p>
</dd>
</dl>

<a name="Examples-1"></a>
<h4 class="subsection">3.7.3 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-1" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Open title 3 from a given DVD structure:
<div class="example">
<pre class="example-preformatted">ffmpeg -f dvdvideo -title 3 -i &lt;path to DVD&gt; ...
</pre></div>

</li><li>Open chapters 3-6 from title 1 from a given DVD structure:
<div class="example">
<pre class="example-preformatted">ffmpeg -f dvdvideo -chapter_start 3 -chapter_end 6 -title 1 -i &lt;path to DVD&gt; ...
</pre></div>

</li><li>Open only chapter 5 from title 1 from a given DVD structure:
<div class="example">
<pre class="example-preformatted">ffmpeg -f dvdvideo -chapter_start 5 -chapter_end 5 -title 1 -i &lt;path to DVD&gt; ...
</pre></div>

</li><li>Demux menu with language 1 from VTS 1, PGC 1, starting at PG 1:
<div class="example">
<pre class="example-preformatted">ffmpeg -f dvdvideo -menu 1 -menu_lu 1 -menu_vts 1 -pgc 1 -pg 1 -i &lt;path to DVD&gt; ...
</pre></div>
</li></ul>

<a name="ea"></a>
<h3 class="section">3.8 ea<span class="pull-right"><a class="anchor hidden-xs" href="#ea" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ea" aria-hidden="true">TOC</a></span></h3>

<p>Electronic Arts Multimedia format demuxer.
</p>
<p>This format is used by various Electronic Arts games.
</p>
<a name="Options-3"></a>
<h4 class="subsection">3.8.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-3" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">merge_alpha <var class="var">bool</var></samp></dt>
<dd>
<p>Normally the VP6 alpha channel (if exists) is returned as a secondary video
stream, by setting this option you can make the demuxer return a single video
stream which contains the alpha channel in addition to the ordinary video.
</p>
</dd>
</dl>

<a name="imf"></a>
<h3 class="section">3.9 imf<span class="pull-right"><a class="anchor hidden-xs" href="#imf" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-imf" aria-hidden="true">TOC</a></span></h3>

<p>Interoperable Master Format demuxer.
</p>
<p>This demuxer presents audio and video streams found in an IMF Composition, as
specified in <a class="url" href="https://doi.org/10.5594/SMPTE.ST2067-2.2020">SMPTE ST 2067-2</a>.
</p>
<div class="example">
<pre class="example-preformatted">ffmpeg [-assetmaps &lt;path of ASSETMAP1&gt;,&lt;path of ASSETMAP2&gt;,...] -i &lt;path of CPL&gt; ...
</pre></div>

<p>If <code class="code">-assetmaps</code> is not specified, the demuxer looks for a file called
<samp class="file">ASSETMAP.xml</samp> in the same directory as the CPL.
</p>
<a name="flv_002c-live_005fflv_002c-kux"></a>
<h3 class="section">3.10 flv, live_flv, kux<span class="pull-right"><a class="anchor hidden-xs" href="#flv_002c-live_005fflv_002c-kux" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-flv_002c-live_005fflv_002c-kux" aria-hidden="true">TOC</a></span></h3>

<p>Adobe Flash Video Format demuxer.
</p>
<p>This demuxer is used to demux FLV files and RTMP network streams. In case of live network streams, if you force format, you may use live_flv option instead of flv to survive timestamp discontinuities.
KUX is a flv variant used on the Youku platform.
</p>
<div class="example">
<pre class="example-preformatted">ffmpeg -f flv -i myfile.flv ...
ffmpeg -f live_flv -i rtmp://&lt;any.server&gt;/anything/key ....
</pre></div>


<dl class="table">
<dt><samp class="option">-flv_metadata <var class="var">bool</var></samp></dt>
<dd><p>Allocate the streams according to the onMetaData array content.
</p>
</dd>
<dt><samp class="option">-flv_ignore_prevtag <var class="var">bool</var></samp></dt>
<dd><p>Ignore the size of previous tag value.
</p>
</dd>
<dt><samp class="option">-flv_full_metadata <var class="var">bool</var></samp></dt>
<dd><p>Output all context of the onMetadata.
</p></dd>
</dl>

<a name="gif-1"></a>
<h3 class="section">3.11 gif<span class="pull-right"><a class="anchor hidden-xs" href="#gif" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-gif" aria-hidden="true">TOC</a></span></h3>

<p>Animated GIF demuxer.
</p>
<p>It accepts the following options:
</p>
<dl class="table">
<dt><samp class="option">min_delay</samp></dt>
<dd><p>Set the minimum valid delay between frames in hundredths of seconds.
Range is 0 to 6000. Default value is 2.
</p>
</dd>
<dt><samp class="option">max_gif_delay</samp></dt>
<dd><p>Set the maximum valid delay between frames in hundredth of seconds.
Range is 0 to 65535. Default value is 65535 (nearly eleven minutes),
the maximum value allowed by the specification.
</p>
</dd>
<dt><samp class="option">default_delay</samp></dt>
<dd><p>Set the default delay between frames in hundredths of seconds.
Range is 0 to 6000. Default value is 10.
</p>
</dd>
<dt><samp class="option">ignore_loop</samp></dt>
<dd><p>GIF files can contain information to loop a certain number of times (or
infinitely). If <samp class="option">ignore_loop</samp> is set to 1, then the loop setting
from the input will be ignored and looping will not occur. If set to 0,
then looping will occur and will cycle the number of times according to
the GIF. Default value is 1.
</p></dd>
</dl>

<p>For example, with the overlay filter, place an infinitely looping GIF
over another video:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i input.mp4 -ignore_loop 0 -i input.gif -filter_complex overlay=shortest=1 out.mkv
</pre></div>

<p>Note that in the above example the shortest option for overlay filter is
used to end the output video at the length of the shortest input file,
which in this case is <samp class="file">input.mp4</samp> as the GIF in this example loops
infinitely.
</p>
<a name="hls-1"></a>
<h3 class="section">3.12 hls<span class="pull-right"><a class="anchor hidden-xs" href="#hls" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-hls" aria-hidden="true">TOC</a></span></h3>

<p>HLS demuxer
</p>
<p>Apple HTTP Live Streaming demuxer.
</p>
<p>This demuxer presents all AVStreams from all variant streams.
The id field is set to the bitrate variant index number. By setting
the discard flags on AVStreams (by pressing &rsquo;a&rsquo; or &rsquo;v&rsquo; in ffplay),
the caller can decide which variant streams to actually receive.
The total bitrate of the variant that the stream belongs to is
available in a metadata key named &quot;variant_bitrate&quot;.
</p>
<p>It accepts the following options:
</p>
<dl class="table">
<dt><samp class="option">live_start_index</samp></dt>
<dd><p>segment index to start live streams at (negative values are from the end).
</p>
</dd>
<dt><samp class="option">prefer_x_start</samp></dt>
<dd><p>prefer to use #EXT-X-START if it&rsquo;s in playlist instead of live_start_index.
</p>
</dd>
<dt><samp class="option">allowed_extensions</samp></dt>
<dd><p>&rsquo;,&rsquo; separated list of file extensions that hls is allowed to access.
</p>
</dd>
<dt><samp class="option">extension_picky</samp></dt>
<dd><p>This blocks disallowed extensions from probing
It also requires all available segments to have matching extensions to the format
except mpegts, which is always allowed.
It is recommended to set the whitelists correctly instead of depending on extensions
Enabled by default.
</p>
</dd>
<dt><samp class="option">max_reload</samp></dt>
<dd><p>Maximum number of times a insufficient list is attempted to be reloaded.
Default value is 1000.
</p>
</dd>
<dt><samp class="option">m3u8_hold_counters</samp></dt>
<dd><p>The maximum number of times to load m3u8 when it refreshes without new segments.
Default value is 1000.
</p>
</dd>
<dt><samp class="option">http_persistent</samp></dt>
<dd><p>Use persistent HTTP connections. Applicable only for HTTP streams.
Enabled by default.
</p>
</dd>
<dt><samp class="option">http_multiple</samp></dt>
<dd><p>Use multiple HTTP connections for downloading HTTP segments.
Enabled by default for HTTP/1.1 servers.
</p>
</dd>
<dt><samp class="option">http_seekable</samp></dt>
<dd><p>Use HTTP partial requests for downloading HTTP segments.
0 = disable, 1 = enable, -1 = auto, Default is auto.
</p>
</dd>
<dt><samp class="option">seg_format_options</samp></dt>
<dd><p>Set options for the demuxer of media segments using a list of key=value pairs separated by <code class="code">:</code>.
</p>
</dd>
<dt><samp class="option">seg_max_retry</samp></dt>
<dd><p>Maximum number of times to reload a segment on error, useful when segment skip on network error is not desired.
Default value is 0.
</p></dd>
</dl>

<a name="image2-1"></a>
<h3 class="section">3.13 image2<span class="pull-right"><a class="anchor hidden-xs" href="#image2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-image2" aria-hidden="true">TOC</a></span></h3>

<p>Image file demuxer.
</p>
<p>This demuxer reads from a list of image files specified by a pattern.
The syntax and meaning of the pattern is specified by the
option <var class="var">pattern_type</var>.
</p>
<p>The pattern may contain a suffix which is used to automatically
determine the format of the images contained in the files.
</p>
<p>The size, the pixel format, and the format of each image must be the
same for all the files in the sequence.
</p>
<p>This demuxer accepts the following options:
</p><dl class="table">
<dt><samp class="option">framerate</samp></dt>
<dd><p>Set the frame rate for the video stream. It defaults to 25.
</p></dd>
<dt><samp class="option">loop</samp></dt>
<dd><p>If set to 1, loop over the input. Default value is 0.
</p></dd>
<dt><samp class="option">pattern_type</samp></dt>
<dd><p>Select the pattern type used to interpret the provided filename.
</p>
<p><var class="var">pattern_type</var> accepts one of the following values.
</p><dl class="table">
<dt><samp class="option">none</samp></dt>
<dd><p>Disable pattern matching, therefore the video will only contain the specified
image. You should use this option if you do not want to create sequences from
multiple images and your filenames may contain special pattern characters.
</p></dd>
<dt><samp class="option">sequence</samp></dt>
<dd><p>Select a sequence pattern type, used to specify a sequence of files
indexed by sequential numbers.
</p>
<p>A sequence pattern may contain the string &quot;%d&quot; or &quot;%0<var class="var">N</var>d&quot;, which
specifies the position of the characters representing a sequential
number in each filename matched by the pattern. If the form
&quot;%d0<var class="var">N</var>d&quot; is used, the string representing the number in each
filename is 0-padded and <var class="var">N</var> is the total number of 0-padded
digits representing the number. The literal character &rsquo;%&rsquo; can be
specified in the pattern with the string &quot;%%&quot;.
</p>
<p>If the sequence pattern contains &quot;%d&quot; or &quot;%0<var class="var">N</var>d&quot;, the first filename of
the file list specified by the pattern must contain a number
inclusively contained between <var class="var">start_number</var> and
<var class="var">start_number</var>+<var class="var">start_number_range</var>-1, and all the following
numbers must be sequential.
</p>
<p>For example the pattern &quot;img-%03d.bmp&quot; will match a sequence of
filenames of the form <samp class="file">img-001.bmp</samp>, <samp class="file">img-002.bmp</samp>, ...,
<samp class="file">img-010.bmp</samp>, etc.; the pattern &quot;i%%m%%g-%d.jpg&quot; will match a
sequence of filenames of the form <samp class="file">i%m%g-1.jpg</samp>,
<samp class="file">i%m%g-2.jpg</samp>, ..., <samp class="file">i%m%g-10.jpg</samp>, etc.
</p>
<p>Note that the pattern must not necessarily contain &quot;%d&quot; or
&quot;%0<var class="var">N</var>d&quot;, for example to convert a single image file
<samp class="file">img.jpeg</samp> you can employ the command:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i img.jpeg img.png
</pre></div>

</dd>
<dt><samp class="option">glob</samp></dt>
<dd><p>Select a glob wildcard pattern type.
</p>
<p>The pattern is interpreted like a <code class="code">glob()</code> pattern. This is only
selectable if libavformat was compiled with globbing support.
</p>
</dd>
<dt><samp class="option">glob_sequence <em class="emph">(deprecated, will be removed)</em></samp></dt>
<dd><p>Select a mixed glob wildcard/sequence pattern.
</p>
<p>If your version of libavformat was compiled with globbing support, and
the provided pattern contains at least one glob meta character among
<code class="code">%*?[]{}</code> that is preceded by an unescaped &quot;%&quot;, the pattern is
interpreted like a <code class="code">glob()</code> pattern, otherwise it is interpreted
like a sequence pattern.
</p>
<p>All glob special characters <code class="code">%*?[]{}</code> must be prefixed
with &quot;%&quot;. To escape a literal &quot;%&quot; you shall use &quot;%%&quot;.
</p>
<p>For example the pattern <code class="code">foo-%*.jpeg</code> will match all the
filenames prefixed by &quot;foo-&quot; and terminating with &quot;.jpeg&quot;, and
<code class="code">foo-%?%?%?.jpeg</code> will match all the filenames prefixed with
&quot;foo-&quot;, followed by a sequence of three characters, and terminating
with &quot;.jpeg&quot;.
</p>
<p>This pattern type is deprecated in favor of <var class="var">glob</var> and
<var class="var">sequence</var>.
</p></dd>
</dl>

<p>Default value is <var class="var">glob_sequence</var>.
</p></dd>
<dt><samp class="option">pixel_format</samp></dt>
<dd><p>Set the pixel format of the images to read. If not specified the pixel
format is guessed from the first image file in the sequence.
</p></dd>
<dt><samp class="option">start_number</samp></dt>
<dd><p>Set the index of the file matched by the image file pattern to start
to read from. Default value is 0.
</p></dd>
<dt><samp class="option">start_number_range</samp></dt>
<dd><p>Set the index interval range to check when looking for the first image
file in the sequence, starting from <var class="var">start_number</var>. Default value
is 5.
</p></dd>
<dt><samp class="option">ts_from_file</samp></dt>
<dd><p>If set to 1, will set frame timestamp to modification time of image file. Note
that monotonity of timestamps is not provided: images go in the same order as
without this option. Default value is 0.
If set to 2, will set frame timestamp to the modification time of the image file in
nanosecond precision.
</p></dd>
<dt><samp class="option">video_size</samp></dt>
<dd><p>Set the video size of the images to read. If not specified the video
size is guessed from the first image file in the sequence.
</p></dd>
<dt><samp class="option">export_path_metadata</samp></dt>
<dd><p>If set to 1, will add two extra fields to the metadata found in input, making them
also available for other filters (see <var class="var">drawtext</var> filter for examples). Default
value is 0. The extra fields are described below:
</p><dl class="table">
<dt><samp class="option">lavf.image2dec.source_path</samp></dt>
<dd><p>Corresponds to the full path to the input file being read.
</p></dd>
<dt><samp class="option">lavf.image2dec.source_basename</samp></dt>
<dd><p>Corresponds to the name of the file being read.
</p></dd>
</dl>

</dd>
</dl>

<a name="Examples-2"></a>
<h4 class="subsection">3.13.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-2" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Use <code class="command">ffmpeg</code> for creating a video from the images in the file
sequence <samp class="file">img-001.jpeg</samp>, <samp class="file">img-002.jpeg</samp>, ..., assuming an
input frame rate of 10 frames per second:
<div class="example">
<pre class="example-preformatted">ffmpeg -framerate 10 -i 'img-%03d.jpeg' out.mkv
</pre></div>

</li><li>As above, but start by reading from a file with index 100 in the sequence:
<div class="example">
<pre class="example-preformatted">ffmpeg -framerate 10 -start_number 100 -i 'img-%03d.jpeg' out.mkv
</pre></div>

</li><li>Read images matching the &quot;*.png&quot; glob pattern , that is all the files
terminating with the &quot;.png&quot; suffix:
<div class="example">
<pre class="example-preformatted">ffmpeg -framerate 10 -pattern_type glob -i &quot;*.png&quot; out.mkv
</pre></div>
</li></ul>

<a name="libgme"></a>
<h3 class="section">3.14 libgme<span class="pull-right"><a class="anchor hidden-xs" href="#libgme" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libgme" aria-hidden="true">TOC</a></span></h3>

<p>The Game Music Emu library is a collection of video game music file emulators.
</p>
<p>See <a class="url" href="https://bitbucket.org/mpyne/game-music-emu/overview">https://bitbucket.org/mpyne/game-music-emu/overview</a> for more information.
</p>
<p>It accepts the following options:
</p>
<dl class="table">
<dt><samp class="option">track_index</samp></dt>
<dd><p>Set the index of which track to demux. The demuxer can only export one track.
Track indexes start at 0. Default is to pick the first track. Number of tracks
is exported as <var class="var">tracks</var> metadata entry.
</p>
</dd>
<dt><samp class="option">sample_rate</samp></dt>
<dd><p>Set the sampling rate of the exported track. Range is 1000 to 999999. Default is 44100.
</p>
</dd>
<dt><samp class="option">max_size <em class="emph">(bytes)</em></samp></dt>
<dd><p>The demuxer buffers the entire file into memory. Adjust this value to set the maximum buffer size,
which in turn, acts as a ceiling for the size of files that can be read.
Default is 50 MiB.
</p>
</dd>
</dl>

<a name="libmodplug"></a>
<h3 class="section">3.15 libmodplug<span class="pull-right"><a class="anchor hidden-xs" href="#libmodplug" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libmodplug" aria-hidden="true">TOC</a></span></h3>

<p>ModPlug based module demuxer
</p>
<p>See <a class="url" href="https://github.com/Konstanty/libmodplug">https://github.com/Konstanty/libmodplug</a>
</p>
<p>It will export one 2-channel 16-bit 44.1 kHz audio stream.
Optionally, a <code class="code">pal8</code> 16-color video stream can be exported with or without printed metadata.
</p>
<p>It accepts the following options:
</p>
<dl class="table">
<dt><samp class="option">noise_reduction</samp></dt>
<dd><p>Apply a simple low-pass filter. Can be 1 (on) or 0 (off). Default is 0.
</p>
</dd>
<dt><samp class="option">reverb_depth</samp></dt>
<dd><p>Set amount of reverb. Range 0-100. Default is 0.
</p>
</dd>
<dt><samp class="option">reverb_delay</samp></dt>
<dd><p>Set delay in ms, clamped to 40-250 ms. Default is 0.
</p>
</dd>
<dt><samp class="option">bass_amount</samp></dt>
<dd><p>Apply bass expansion a.k.a. XBass or megabass. Range is 0 (quiet) to 100 (loud). Default is 0.
</p>
</dd>
<dt><samp class="option">bass_range</samp></dt>
<dd><p>Set cutoff i.e. upper-bound for bass frequencies. Range is 10-100 Hz. Default is 0.
</p>
</dd>
<dt><samp class="option">surround_depth</samp></dt>
<dd><p>Apply a Dolby Pro-Logic surround effect. Range is 0 (quiet) to 100 (heavy). Default is 0.
</p>
</dd>
<dt><samp class="option">surround_delay</samp></dt>
<dd><p>Set surround delay in ms, clamped to 5-40 ms. Default is 0.
</p>
</dd>
<dt><samp class="option">max_size</samp></dt>
<dd><p>The demuxer buffers the entire file into memory. Adjust this value to set the maximum buffer size,
which in turn, acts as a ceiling for the size of files that can be read. Range is 0 to 100 MiB.
0 removes buffer size limit (not recommended). Default is 5 MiB.
</p>
</dd>
<dt><samp class="option">video_stream_expr</samp></dt>
<dd><p>String which is evaluated using the eval API to assign colors to the generated video stream.
Variables which can be used are <code class="code">x</code>, <code class="code">y</code>, <code class="code">w</code>, <code class="code">h</code>, <code class="code">t</code>, <code class="code">speed</code>,
<code class="code">tempo</code>, <code class="code">order</code>, <code class="code">pattern</code> and <code class="code">row</code>.
</p>
</dd>
<dt><samp class="option">video_stream</samp></dt>
<dd><p>Generate video stream. Can be 1 (on) or 0 (off). Default is 0.
</p>
</dd>
<dt><samp class="option">video_stream_w</samp></dt>
<dd><p>Set video frame width in &rsquo;chars&rsquo; where one char indicates 8 pixels. Range is 20-512. Default is 30.
</p>
</dd>
<dt><samp class="option">video_stream_h</samp></dt>
<dd><p>Set video frame height in &rsquo;chars&rsquo; where one char indicates 8 pixels. Range is 20-512. Default is 30.
</p>
</dd>
<dt><samp class="option">video_stream_ptxt</samp></dt>
<dd><p>Print metadata on video stream. Includes <code class="code">speed</code>, <code class="code">tempo</code>, <code class="code">order</code>, <code class="code">pattern</code>,
<code class="code">row</code> and <code class="code">ts</code> (time in ms). Can be 1 (on) or 0 (off). Default is 1.
</p>
</dd>
</dl>

<a name="libopenmpt"></a>
<h3 class="section">3.16 libopenmpt<span class="pull-right"><a class="anchor hidden-xs" href="#libopenmpt" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libopenmpt" aria-hidden="true">TOC</a></span></h3>

<p>libopenmpt based module demuxer
</p>
<p>See <a class="url" href="https://lib.openmpt.org/libopenmpt/">https://lib.openmpt.org/libopenmpt/</a> for more information.
</p>
<p>Some files have multiple subsongs (tracks) this can be set with the <samp class="option">subsong</samp>
option.
</p>
<p>It accepts the following options:
</p>
<dl class="table">
<dt><samp class="option">subsong</samp></dt>
<dd><p>Set the subsong index. This can be either  &rsquo;all&rsquo;, &rsquo;auto&rsquo;, or the index of the
subsong. Subsong indexes start at 0. The default is &rsquo;auto&rsquo;.
</p>
<p>The default value is to let libopenmpt choose.
</p>
</dd>
<dt><samp class="option">layout</samp></dt>
<dd><p>Set the channel layout. Valid values are 1, 2, and 4 channel layouts.
The default value is STEREO.
</p>
</dd>
<dt><samp class="option">sample_rate</samp></dt>
<dd><p>Set the sample rate for libopenmpt to output.
Range is from 1000 to INT_MAX. The value default is 48000.
</p></dd>
</dl>

<a name="mov_002fmp4_002f3gp"></a>
<h3 class="section">3.17 mov/mp4/3gp<span class="pull-right"><a class="anchor hidden-xs" href="#mov_002fmp4_002f3gp" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mov_002fmp4_002f3gp" aria-hidden="true">TOC</a></span></h3>

<p>Demuxer for Quicktime File Format &amp; ISO/IEC Base Media File Format (ISO/IEC 14496-12 or MPEG-4 Part 12, ISO/IEC 15444-12 or JPEG 2000 Part 12).
</p>
<p>Registered extensions: mov, mp4, m4a, 3gp, 3g2, mj2, psp, m4b, ism, ismv, isma, f4v
</p>
<a name="Options-4"></a>
<h4 class="subsection">3.17.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-4" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-4" aria-hidden="true">TOC</a></span></h4>

<p>This demuxer accepts the following options:
</p><dl class="table">
<dt><samp class="option">enable_drefs</samp></dt>
<dd><p>Enable loading of external tracks, disabled by default.
Enabling this can theoretically leak information in some use cases.
</p>
</dd>
<dt><samp class="option">use_absolute_path</samp></dt>
<dd><p>Allows loading of external tracks via absolute paths, disabled by default.
Enabling this poses a security risk. It should only be enabled if the source
is known to be non-malicious.
</p>
</dd>
<dt><samp class="option">seek_streams_individually</samp></dt>
<dd><p>When seeking, identify the closest point in each stream individually and demux packets in
that stream from identified point. This can lead to a different sequence of packets compared
to demuxing linearly from the beginning. Default is true.
</p>
</dd>
<dt><samp class="option">ignore_editlist</samp></dt>
<dd><p>Ignore any edit list atoms. The demuxer, by default, modifies the stream index to reflect the
timeline described by the edit list. Default is false.
</p>
</dd>
<dt><samp class="option">advanced_editlist</samp></dt>
<dd><p>Modify the stream index to reflect the timeline described by the edit list. <code class="code">ignore_editlist</code>
must be set to false for this option to be effective.
If both <code class="code">ignore_editlist</code> and this option are set to false, then only the
start of the stream index is modified to reflect initial dwell time or starting timestamp
described by the edit list. Default is true.
</p>
</dd>
<dt><samp class="option">ignore_chapters</samp></dt>
<dd><p>Don&rsquo;t parse chapters. This includes GoPro &rsquo;HiLight&rsquo; tags/moments. Note that chapters are
only parsed when input is seekable. Default is false.
</p>
</dd>
<dt><samp class="option">use_mfra_for</samp></dt>
<dd><p>For seekable fragmented input, set fragment&rsquo;s starting timestamp from media fragment random access box, if present.
</p>
<p>Following options are available:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">auto</samp>&rsquo;</dt>
<dd><p>Auto-detect whether to set mfra timestamps as PTS or DTS <em class="emph">(default)</em>
</p>
</dd>
<dt>&lsquo;<samp class="samp">dts</samp>&rsquo;</dt>
<dd><p>Set mfra timestamps as DTS
</p>
</dd>
<dt>&lsquo;<samp class="samp">pts</samp>&rsquo;</dt>
<dd><p>Set mfra timestamps as PTS
</p>
</dd>
<dt>&lsquo;<samp class="samp">0</samp>&rsquo;</dt>
<dd><p>Don&rsquo;t use mfra box to set timestamps
</p></dd>
</dl>

</dd>
<dt><samp class="option">use_tfdt</samp></dt>
<dd><p>For fragmented input, set fragment&rsquo;s starting timestamp to <code class="code">baseMediaDecodeTime</code> from the <code class="code">tfdt</code> box.
Default is enabled, which will prefer to use the <code class="code">tfdt</code> box to set DTS. Disable to use the <code class="code">earliest_presentation_time</code> from the <code class="code">sidx</code> box.
In either case, the timestamp from the <code class="code">mfra</code> box will be used if it&rsquo;s available and <code class="code">use_mfra_for</code> is
set to pts or dts.
</p>
</dd>
<dt><samp class="option">export_all</samp></dt>
<dd><p>Export unrecognized boxes within the <var class="var">udta</var> box as metadata entries. The first four
characters of the box type are set as the key. Default is false.
</p>
</dd>
<dt><samp class="option">export_xmp</samp></dt>
<dd><p>Export entire contents of <var class="var">XMP_</var> box and <var class="var">uuid</var> box as a string with key <code class="code">xmp</code>. Note that
if <code class="code">export_all</code> is set and this option isn&rsquo;t, the contents of <var class="var">XMP_</var> box are still exported
but with key <code class="code">XMP_</code>. Default is false.
</p>
</dd>
<dt><samp class="option">activation_bytes</samp></dt>
<dd><p>4-byte key required to decrypt Audible AAX and AAX+ files. See Audible AAX subsection below.
</p>
</dd>
<dt><samp class="option">audible_fixed_key</samp></dt>
<dd><p>Fixed key used for handling Audible AAX/AAX+ files. It has been pre-set so should not be necessary to
specify.
</p>
</dd>
<dt><samp class="option">decryption_key</samp></dt>
<dd><p>16-byte key, in hex, to decrypt files encrypted using ISO Common Encryption (CENC/AES-128 CTR; ISO/IEC 23001-7).
</p>
</dd>
<dt><samp class="option">max_stts_delta</samp></dt>
<dd><p>Very high sample deltas written in a trak&rsquo;s stts box may occasionally be intended but usually they are written in
error or used to store a negative value for dts correction when treated as signed 32-bit integers. This option lets
the user set an upper limit, beyond which the delta is clamped to 1. Values greater than the limit if negative when
cast to int32 are used to adjust onward dts.
</p>
<p>Unit is the track time scale. Range is 0 to UINT_MAX. Default is <code class="code">UINT_MAX - 48000*10</code> which allows up to
a 10 second dts correction for 48 kHz audio streams while accommodating 99.9% of <code class="code">uint32</code> range.
</p>
</dd>
<dt><samp class="option">interleaved_read</samp></dt>
<dd><p>Interleave packets from multiple tracks at demuxer level. For badly interleaved files, this prevents playback issues
caused by large gaps between packets in different tracks, as MOV/MP4 do not have packet placement requirements.
However, this can cause excessive seeking on very badly interleaved files, due to seeking between tracks, so disabling
it may prevent I/O issues, at the expense of playback.
</p>
</dd>
</dl>

<a name="Audible-AAX"></a>
<h4 class="subsection">3.17.2 Audible AAX<span class="pull-right"><a class="anchor hidden-xs" href="#Audible-AAX" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Audible-AAX" aria-hidden="true">TOC</a></span></h4>

<p>Audible AAX files are encrypted M4B files, and they can be decrypted by specifying a 4 byte activation secret.
</p><div class="example">
<pre class="example-preformatted">ffmpeg -activation_bytes 1CEB00DA -i test.aax -vn -c:a copy output.mp4
</pre></div>

<a name="mpegts"></a>
<h3 class="section">3.18 mpegts<span class="pull-right"><a class="anchor hidden-xs" href="#mpegts" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mpegts" aria-hidden="true">TOC</a></span></h3>

<p>MPEG-2 transport stream demuxer.
</p>
<p>This demuxer accepts the following options:
</p><dl class="table">
<dt><samp class="option">resync_size</samp></dt>
<dd><p>Set size limit for looking up a new synchronization. Default value is
65536.
</p>
</dd>
<dt><samp class="option">skip_unknown_pmt</samp></dt>
<dd><p>Skip PMTs for programs not defined in the PAT. Default value is 0.
</p>
</dd>
<dt><samp class="option">fix_teletext_pts</samp></dt>
<dd><p>Override teletext packet PTS and DTS values with the timestamps calculated
from the PCR of the first program which the teletext stream is part of and is
not discarded. Default value is 1, set this option to 0 if you want your
teletext packet PTS and DTS values untouched.
</p>
</dd>
<dt><samp class="option">ts_packetsize</samp></dt>
<dd><p>Output option carrying the raw packet size in bytes.
Show the detected raw packet size, cannot be set by the user.
</p>
</dd>
<dt><samp class="option">scan_all_pmts</samp></dt>
<dd><p>Scan and combine all PMTs. The value is an integer with value from -1
to 1 (-1 means automatic setting, 1 means enabled, 0 means
disabled). Default value is -1.
</p>
</dd>
<dt><samp class="option">merge_pmt_versions</samp></dt>
<dd><p>Re-use existing streams when a PMT&rsquo;s version is updated and elementary
streams move to different PIDs. Default value is 0.
</p>
</dd>
<dt><samp class="option">max_packet_size</samp></dt>
<dd><p>Set maximum size, in bytes, of packet emitted by the demuxer. Payloads above this size
are split across multiple packets. Range is 1 to INT_MAX/2. Default is 204800 bytes.
</p></dd>
</dl>

<a name="mpjpeg"></a>
<h3 class="section">3.19 mpjpeg<span class="pull-right"><a class="anchor hidden-xs" href="#mpjpeg" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mpjpeg" aria-hidden="true">TOC</a></span></h3>

<p>MJPEG encapsulated in multi-part MIME demuxer.
</p>
<p>This demuxer allows reading of MJPEG, where each frame is represented as a part of
multipart/x-mixed-replace stream.
</p><dl class="table">
<dt><samp class="option">strict_mime_boundary</samp></dt>
<dd><p>Default implementation applies a relaxed standard to multi-part MIME boundary detection,
to prevent regression with numerous existing endpoints not generating a proper MIME
MJPEG stream. Turning this option on by setting it to 1 will result in a stricter check
of the boundary value.
</p></dd>
</dl>

<a name="rawvideo"></a>
<h3 class="section">3.20 rawvideo<span class="pull-right"><a class="anchor hidden-xs" href="#rawvideo" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-rawvideo" aria-hidden="true">TOC</a></span></h3>

<p>Raw video demuxer.
</p>
<p>This demuxer allows one to read raw video data. Since there is no header
specifying the assumed video parameters, the user must specify them
in order to be able to decode the data correctly.
</p>
<p>This demuxer accepts the following options:
</p><dl class="table">
<dt><samp class="option">framerate</samp></dt>
<dd><p>Set input video frame rate. Default value is 25.
</p>
</dd>
<dt><samp class="option">pixel_format</samp></dt>
<dd><p>Set the input video pixel format. Default value is <code class="code">yuv420p</code>.
</p>
</dd>
<dt><samp class="option">video_size</samp></dt>
<dd><p>Set the input video size. This value must be specified explicitly.
</p></dd>
</dl>

<p>For example to read a rawvideo file <samp class="file">input.raw</samp> with
<code class="command">ffplay</code>, assuming a pixel format of <code class="code">rgb24</code>, a video
size of <code class="code">320x240</code>, and a frame rate of 10 images per second, use
the command:
</p><div class="example">
<pre class="example-preformatted">ffplay -f rawvideo -pixel_format rgb24 -video_size 320x240 -framerate 10 input.raw
</pre></div>

<a class="anchor" id="rcwtdec"></a><a name="rcwt"></a>
<h3 class="section">3.21 rcwt<span class="pull-right"><a class="anchor hidden-xs" href="#rcwt" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-rcwt" aria-hidden="true">TOC</a></span></h3>

<p>RCWT (Raw Captions With Time) is a format native to ccextractor, a commonly
used open source tool for processing 608/708 Closed Captions (CC) sources.
For more information on the format, see <a data-manual="ffmpeg-formats" href="./ffmpeg-formats.html#rcwtenc">(ffmpeg-formats)rcwtenc</a>.
</p>
<p>This demuxer implements the specification as of March 2024, which has
been stable and unchanged since April 2014.
</p>
<a name="Examples-3"></a>
<h4 class="subsection">3.21.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-3" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Render CC to ASS using the built-in decoder:
<div class="example">
<pre class="example-preformatted">ffmpeg -i CC.rcwt.bin CC.ass
</pre></div>
<p>Note that if your output appears to be empty, you may have to manually
set the decoder&rsquo;s <samp class="option">data_field</samp> option to pick the desired CC substream.
</p>
</li><li>Convert an RCWT backup to Scenarist (SCC) format:
<div class="example">
<pre class="example-preformatted">ffmpeg -i CC.rcwt.bin -c:s copy CC.scc
</pre></div>
<p>Note that the SCC format does not support all of the possible CC extensions
that can be stored in RCWT (such as EIA-708).
</p></li></ul>

<a name="sbg"></a>
<h3 class="section">3.22 sbg<span class="pull-right"><a class="anchor hidden-xs" href="#sbg" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-sbg" aria-hidden="true">TOC</a></span></h3>

<p>SBaGen script demuxer.
</p>
<p>This demuxer reads the script language used by SBaGen
<a class="url" href="http://uazu.net/sbagen/">http://uazu.net/sbagen/</a> to generate binaural beats sessions. A SBG
script looks like that:
</p><div class="example">
<pre class="example-preformatted">-SE
a: 300-2.5/3 440+4.5/0
b: 300-2.5/0 440+4.5/3
off: -
NOW      == a
+0:07:00 == b
+0:14:00 == a
+0:21:00 == b
+0:30:00    off
</pre></div>

<p>A SBG script can mix absolute and relative timestamps. If the script uses
either only absolute timestamps (including the script start time) or only
relative ones, then its layout is fixed, and the conversion is
straightforward. On the other hand, if the script mixes both kind of
timestamps, then the <var class="var">NOW</var> reference for relative timestamps will be
taken from the current time of day at the time the script is read, and the
script layout will be frozen according to that reference. That means that if
the script is directly played, the actual times will match the absolute
timestamps up to the sound controller&rsquo;s clock accuracy, but if the user
somehow pauses the playback or seeks, all times will be shifted accordingly.
</p>
<a name="tedcaptions"></a>
<h3 class="section">3.23 tedcaptions<span class="pull-right"><a class="anchor hidden-xs" href="#tedcaptions" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-tedcaptions" aria-hidden="true">TOC</a></span></h3>

<p>JSON captions used for <a class="url" href="http://www.ted.com/">TED Talks</a>.
</p>
<p>TED does not provide links to the captions, but they can be guessed from the
page. The file <samp class="file">tools/bookmarklets.html</samp> from the FFmpeg source tree
contains a bookmarklet to expose them.
</p>
<p>This demuxer accepts the following option:
</p><dl class="table">
<dt><samp class="option">start_time</samp></dt>
<dd><p>Set the start time of the TED talk, in milliseconds. The default is 15000
(15s). It is used to sync the captions with the downloadable videos, because
they include a 15s intro.
</p></dd>
</dl>

<p>Example: convert the captions to a format most players understand:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i http://www.ted.com/talks/subtitles/id/1/lang/en talk1-en.srt
</pre></div>

<a name="vapoursynth"></a>
<h3 class="section">3.24 vapoursynth<span class="pull-right"><a class="anchor hidden-xs" href="#vapoursynth" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-vapoursynth" aria-hidden="true">TOC</a></span></h3>

<p>Vapoursynth wrapper.
</p>
<p>Due to security concerns, Vapoursynth scripts will not
be autodetected so the input format has to be forced. For ff* CLI tools,
add <code class="code">-f vapoursynth</code> before the input <code class="code">-i yourscript.vpy</code>.
</p>
<p>This demuxer accepts the following option:
</p><dl class="table">
<dt><samp class="option">max_script_size</samp></dt>
<dd><p>The demuxer buffers the entire script into memory. Adjust this value to set the maximum buffer size,
which in turn, acts as a ceiling for the size of scripts that can be read.
Default is 1 MiB.
</p></dd>
</dl>

<a name="w64"></a>
<h3 class="section">3.25 w64<span class="pull-right"><a class="anchor hidden-xs" href="#w64" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-w64" aria-hidden="true">TOC</a></span></h3>

<p>Sony Wave64 Audio demuxer.
</p>
<p>This demuxer accepts the following options:
</p><dl class="table">
<dt><samp class="option">max_size</samp></dt>
<dd><p>See the same option for the <a class="ref" href="#wav">wav</a> demuxer.
</p></dd>
</dl>

<a class="anchor" id="wav"></a><a name="wav-1"></a>
<h3 class="section">3.26 wav<span class="pull-right"><a class="anchor hidden-xs" href="#wav" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-wav" aria-hidden="true">TOC</a></span></h3>

<p>RIFF Wave Audio demuxer.
</p>
<p>This demuxer accepts the following options:
</p><dl class="table">
<dt><samp class="option">max_size</samp></dt>
<dd><p>Specify the maximum packet size in bytes for the demuxed packets. By default
this is set to 0, which means that a sensible value is chosen based on the
input format.
</p></dd>
</dl>

<a name="Muxers"></a>
<h2 class="chapter">4 Muxers<span class="pull-right"><a class="anchor hidden-xs" href="#Muxers" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Muxers" aria-hidden="true">TOC</a></span></h2>

<p>Muxers are configured elements in FFmpeg which allow writing
multimedia streams to a particular type of file.
</p>
<p>When you configure your FFmpeg build, all the supported muxers
are enabled by default. You can list all available muxers using the
configure option <code class="code">--list-muxers</code>.
</p>
<p>You can disable all the muxers with the configure option
<code class="code">--disable-muxers</code> and selectively enable / disable single muxers
with the options <code class="code">--enable-muxer=<var class="var">MUXER</var></code> /
<code class="code">--disable-muxer=<var class="var">MUXER</var></code>.
</p>
<p>The option <code class="code">-muxers</code> of the ff* tools will display the list of
enabled muxers. Use <code class="code">-formats</code> to view a combined list of
enabled demuxers and muxers.
</p>
<p>A description of some of the currently available muxers follows.
</p>
<a class="anchor" id="raw-muxers"></a><a name="Raw-muxers"></a>
<h3 class="section">4.1 Raw muxers<span class="pull-right"><a class="anchor hidden-xs" href="#Raw-muxers" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Raw-muxers" aria-hidden="true">TOC</a></span></h3>

<p>This section covers raw muxers. They accept a single stream matching
the designated codec. They do not store timestamps or metadata. The
recognized extension is the same as the muxer name unless indicated
otherwise.
</p>
<p>It comprises the following muxers. The media type and the eventual
extensions used to automatically selects the muxer from the output
extensions are also shown.
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">ac3 <em class="emph">audio</em></samp>&rsquo;</dt>
<dd><p>Dolby Digital, also known as AC-3.
</p>
</dd>
<dt>&lsquo;<samp class="samp">adx <em class="emph">audio</em></samp>&rsquo;</dt>
<dd><p>CRI Middleware ADX audio.
</p>
<p>This muxer will write out the total sample count near the start of the
first packet when the output is seekable and the count can be stored
in 32 bits.
</p>
</dd>
<dt>&lsquo;<samp class="samp">aptx <em class="emph">audio</em></samp>&rsquo;</dt>
<dd><p>aptX (Audio Processing Technology for Bluetooth)
</p>
</dd>
<dt>&lsquo;<samp class="samp">aptx_hd <em class="emph">audio</em> (aptxdh)</samp>&rsquo;</dt>
<dd><p>aptX HD (Audio Processing Technology for Bluetooth) audio
</p>
</dd>
<dt>&lsquo;<samp class="samp">avs2 <em class="emph">video</em> (avs, avs2)</samp>&rsquo;</dt>
<dd><p>AVS2-P2 (Audio Video Standard - Second generation - Part 2) /
IEEE 1857.4 video
</p>
</dd>
<dt>&lsquo;<samp class="samp">avs3 <em class="emph">video</em> (avs3)</samp>&rsquo;</dt>
<dd><p>AVS3-P2 (Audio Video Standard - Third generation - Part 2) /
IEEE 1857.10 video
</p>
</dd>
<dt>&lsquo;<samp class="samp">cavsvideo <em class="emph">video</em> (cavs)</samp>&rsquo;</dt>
<dd><p>Chinese AVS (Audio Video Standard - First generation)
</p>
</dd>
<dt>&lsquo;<samp class="samp">codec2raw <em class="emph">audio</em></samp>&rsquo;</dt>
<dd><p>Codec 2 audio.
</p>
<p>No extension is registered so format name has to be supplied e.g. with
the ffmpeg CLI tool <code class="code">-f codec2raw</code>.
</p>
</dd>
<dt>&lsquo;<samp class="samp">data <em class="emph">any</em></samp>&rsquo;</dt>
<dd><p>Generic data muxer.
</p>
<p>This muxer accepts a single stream with any codec of any type. The
input stream has to be selected using the <code class="code">-map</code> option with the
<code class="command">ffmpeg</code> CLI tool.
</p>
<p>No extension is registered so format name has to be supplied e.g. with
the <code class="command">ffmpeg</code> CLI tool <code class="code">-f data</code>.
</p>
</dd>
<dt>&lsquo;<samp class="samp">dfpwm <em class="emph">audio</em> (dfpwm)</samp>&rsquo;</dt>
<dd><p>Raw DFPWM1a (Dynamic Filter Pulse With Modulation) audio muxer.
</p>
</dd>
<dt>&lsquo;<samp class="samp">dirac <em class="emph">video</em> (drc, vc2)</samp>&rsquo;</dt>
<dd><p>BBC Dirac video.
</p>
<p>The Dirac Pro codec is a subset and is standardized as SMPTE VC-2.
</p>
</dd>
<dt>&lsquo;<samp class="samp">dnxhd <em class="emph">video</em> (dnxhd, dnxhr)</samp>&rsquo;</dt>
<dd><p>Avid DNxHD video.
</p>
<p>It is standardized as SMPTE VC-3. Accepts DNxHR streams.
</p>
</dd>
<dt>&lsquo;<samp class="samp">dts <em class="emph">audio</em></samp>&rsquo;</dt>
<dd><p>DTS Coherent Acoustics (DCA) audio
</p>
</dd>
<dt>&lsquo;<samp class="samp">eac3 <em class="emph">audio</em></samp>&rsquo;</dt>
<dd><p>Dolby Digital Plus, also known as Enhanced AC-3
</p>
</dd>
<dt>&lsquo;<samp class="samp">evc <em class="emph">video</em> (evc)</samp>&rsquo;</dt>
<dd><p>MPEG-5 Essential Video Coding (EVC) / EVC / MPEG-5 Part 1 EVC video
</p>
</dd>
<dt>&lsquo;<samp class="samp">g722 <em class="emph">audio</em></samp>&rsquo;</dt>
<dd><p>ITU-T G.722 audio
</p>
</dd>
<dt>&lsquo;<samp class="samp">g723_1 <em class="emph">audio</em> (tco, rco)</samp>&rsquo;</dt>
<dd><p>ITU-T G.723.1 audio
</p>
</dd>
<dt>&lsquo;<samp class="samp">g726 <em class="emph">audio</em></samp>&rsquo;</dt>
<dd><p>ITU-T G.726 big-endian (&quot;left-justified&quot;) audio.
</p>
<p>No extension is registered so format name has to be supplied e.g. with
the <code class="command">ffmpeg</code> CLI tool <code class="code">-f g726</code>.
</p>
</dd>
<dt>&lsquo;<samp class="samp">g726le <em class="emph">audio</em></samp>&rsquo;</dt>
<dd><p>ITU-T G.726 little-endian (&quot;right-justified&quot;) audio.
</p>
<p>No extension is registered so format name has to be supplied e.g. with
the <code class="command">ffmpeg</code> CLI tool <code class="code">-f g726le</code>.
</p>
</dd>
<dt>&lsquo;<samp class="samp">gsm <em class="emph">audio</em></samp>&rsquo;</dt>
<dd><p>Global System for Mobile Communications audio
</p>
</dd>
<dt>&lsquo;<samp class="samp">h261 <em class="emph">video</em></samp>&rsquo;</dt>
<dd><p>ITU-T H.261 video
</p>
</dd>
<dt>&lsquo;<samp class="samp">h263 <em class="emph">video</em></samp>&rsquo;</dt>
<dd><p>ITU-T H.263 / H.263-1996, H.263+ / H.263-1998 / H.263 version 2 video
</p>
</dd>
<dt>&lsquo;<samp class="samp">h264 <em class="emph">video</em> (h264, 264)</samp>&rsquo;</dt>
<dd><p>ITU-T H.264 / MPEG-4 Part 10 AVC video. Bitstream shall be converted
to Annex B syntax if it&rsquo;s in length-prefixed mode.
</p>
</dd>
<dt>&lsquo;<samp class="samp">hevc <em class="emph">video</em> (hevc, h265, 265)</samp>&rsquo;</dt>
<dd><p>ITU-T H.265 / MPEG-H Part 2 HEVC video. Bitstream shall be converted
to Annex B syntax if it&rsquo;s in length-prefixed mode.
</p>
</dd>
<dt>&lsquo;<samp class="samp">m4v <em class="emph">video</em></samp>&rsquo;</dt>
<dd><p>MPEG-4 Part 2 video
</p>
</dd>
<dt>&lsquo;<samp class="samp">mjpeg <em class="emph">video</em> (mjpg, mjpeg)</samp>&rsquo;</dt>
<dd><p>Motion JPEG video
</p>
</dd>
<dt>&lsquo;<samp class="samp">mlp <em class="emph">audio</em></samp>&rsquo;</dt>
<dd><p>Meridian Lossless Packing, also known as Packed PCM
</p>
</dd>
<dt>&lsquo;<samp class="samp">mp2 <em class="emph">audio</em> (mp2, m2a, mpa)</samp>&rsquo;</dt>
<dd><p>MPEG-1 Audio Layer II audio
</p>
</dd>
<dt>&lsquo;<samp class="samp">mpeg1video <em class="emph">video</em> (mpg, mpeg, m1v)</samp>&rsquo;</dt>
<dd><p>MPEG-1 Part 2 video.
</p>
</dd>
<dt>&lsquo;<samp class="samp">mpeg2video <em class="emph">video</em> (m2v)</samp>&rsquo;</dt>
<dd><p>ITU-T H.262 / MPEG-2 Part 2 video
</p>
</dd>
<dt>&lsquo;<samp class="samp">obu <em class="emph">video</em></samp>&rsquo;</dt>
<dd><p>AV1 low overhead Open Bitstream Units muxer.
</p>
<p>Temporal delimiter OBUs will be inserted in all temporal units of the
stream.
</p>
</dd>
<dt>&lsquo;<samp class="samp">rawvideo <em class="emph">video</em> (yuv, rgb)</samp>&rsquo;</dt>
<dd><p>Raw uncompressed video.
</p>
</dd>
<dt>&lsquo;<samp class="samp">sbc <em class="emph">audio</em> (sbc, msbc)</samp>&rsquo;</dt>
<dd><p>Bluetooth SIG low-complexity subband codec audio
</p>
</dd>
<dt>&lsquo;<samp class="samp">truehd <em class="emph">audio</em> (thd)</samp>&rsquo;</dt>
<dd><p>Dolby TrueHD audio
</p>
</dd>
<dt>&lsquo;<samp class="samp">vc1 <em class="emph">video</em></samp>&rsquo;</dt>
<dd><p>SMPTE 421M / VC-1 video
</p></dd>
</dl>

<a name="Examples-4"></a>
<h4 class="subsection">4.1.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-4" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-4" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Store raw video frames with the &lsquo;<samp class="samp">rawvideo</samp>&rsquo; muxer using <code class="command">ffmpeg</code>:
<div class="example">
<pre class="example-preformatted">ffmpeg -f lavfi -i testsrc -t 10 -s hd1080p testsrc.yuv
</pre></div>

<p>Since the rawvideo muxer do not store the information related to size
and format, this information must be provided when demuxing the file:
</p><div class="example">
<pre class="example-preformatted">ffplay -video_size 1920x1080 -pixel_format rgb24 -f rawvideo testsrc.rgb
</pre></div>
</li></ul>

<a name="Raw-PCM-muxers"></a>
<h3 class="section">4.2 Raw PCM muxers<span class="pull-right"><a class="anchor hidden-xs" href="#Raw-PCM-muxers" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Raw-PCM-muxers" aria-hidden="true">TOC</a></span></h3>
<p>This section covers raw PCM (Pulse-Code Modulation) audio muxers.
</p>
<p>They accept a single stream matching the designated codec. They do not
store timestamps or metadata. The recognized extension is the same as
the muxer name.
</p>
<p>It comprises the following muxers. The optional additional extension
used to automatically select the muxer from the output extension is
also shown in parentheses.
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">alaw (al)</samp>&rsquo;</dt>
<dd><p>PCM A-law
</p>
</dd>
<dt>&lsquo;<samp class="samp">f32be</samp>&rsquo;</dt>
<dd><p>PCM 32-bit floating-point big-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">f32le</samp>&rsquo;</dt>
<dd><p>PCM 32-bit floating-point little-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">f64be</samp>&rsquo;</dt>
<dd><p>PCM 64-bit floating-point big-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">f64le</samp>&rsquo;</dt>
<dd><p>PCM 64-bit floating-point little-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">mulaw (ul)</samp>&rsquo;</dt>
<dd><p>PCM mu-law
</p>
</dd>
<dt>&lsquo;<samp class="samp">s16be</samp>&rsquo;</dt>
<dd><p>PCM signed 16-bit big-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">s16le</samp>&rsquo;</dt>
<dd><p>PCM signed 16-bit little-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">s24be</samp>&rsquo;</dt>
<dd><p>PCM signed 24-bit big-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">s24le</samp>&rsquo;</dt>
<dd><p>PCM signed 24-bit little-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">s32be</samp>&rsquo;</dt>
<dd><p>PCM signed 32-bit big-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">s32le</samp>&rsquo;</dt>
<dd><p>PCM signed 32-bit little-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">s8 (sb)</samp>&rsquo;</dt>
<dd><p>PCM signed 8-bit
</p>
</dd>
<dt>&lsquo;<samp class="samp">u16be</samp>&rsquo;</dt>
<dd><p>PCM unsigned 16-bit big-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">u16le</samp>&rsquo;</dt>
<dd><p>PCM unsigned 16-bit little-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">u24be</samp>&rsquo;</dt>
<dd><p>PCM unsigned 24-bit big-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">u24le</samp>&rsquo;</dt>
<dd><p>PCM unsigned 24-bit little-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">u32be</samp>&rsquo;</dt>
<dd><p>PCM unsigned 32-bit big-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">u32le</samp>&rsquo;</dt>
<dd><p>PCM unsigned 32-bit little-endian
</p>
</dd>
<dt>&lsquo;<samp class="samp">u8 (ub)</samp>&rsquo;</dt>
<dd><p>PCM unsigned 8-bit
</p>
</dd>
<dt>&lsquo;<samp class="samp">vidc</samp>&rsquo;</dt>
<dd><p>PCM Archimedes VIDC
</p></dd>
</dl>

<a name="MPEG_002d1_002fMPEG_002d2-program-stream-muxers"></a>
<h3 class="section">4.3 MPEG-1/MPEG-2 program stream muxers<span class="pull-right"><a class="anchor hidden-xs" href="#MPEG_002d1_002fMPEG_002d2-program-stream-muxers" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-MPEG_002d1_002fMPEG_002d2-program-stream-muxers" aria-hidden="true">TOC</a></span></h3>

<p>This section covers formats belonging to the MPEG-1 and MPEG-2 Systems
family.
</p>
<p>The MPEG-1 Systems format (also known as ISO/IEEC 11172-1 or MPEG-1
program stream) has been adopted for the format of media track stored
in VCD (Video Compact Disc).
</p>
<p>The MPEG-2 Systems standard (also known as ISO/IEEC 13818-1) covers
two containers formats, one known as transport stream and one known as
program stream; only the latter is covered here.
</p>
<p>The MPEG-2 program stream format (also known as VOB due to the
corresponding file extension) is an extension of MPEG-1 program
stream: in addition to support different codecs for the audio and
video streams, it also stores subtitles and navigation metadata.
MPEG-2 program stream has been adopted for storing media streams in
SVCD and DVD storage devices.
</p>
<p>This section comprises the following muxers.
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">mpeg (mpg,mpeg)</samp>&rsquo;</dt>
<dd><p>MPEG-1 Systems / MPEG-1 program stream muxer.
</p>
</dd>
<dt>&lsquo;<samp class="samp">vcd</samp>&rsquo;</dt>
<dd><p>MPEG-1 Systems / MPEG-1 program stream (VCD) muxer.
</p>
<p>This muxer can be used to generate tracks in the format accepted by
the VCD (Video Compact Disc) storage devices.
</p>
<p>It is the same as the &lsquo;<samp class="samp">mpeg</samp>&rsquo; muxer with a few differences.
</p>
</dd>
<dt>&lsquo;<samp class="samp">vob</samp>&rsquo;</dt>
<dd><p>MPEG-2 program stream (VOB) muxer.
</p>
</dd>
<dt>&lsquo;<samp class="samp">dvd</samp>&rsquo;</dt>
<dd><p>MPEG-2 program stream (DVD VOB) muxer.
</p>
<p>This muxer can be used to generate tracks in the format accepted by
the DVD (Digital Versatile Disc) storage devices.
</p>
<p>This is the same as the &lsquo;<samp class="samp">vob</samp>&rsquo; muxer with a few differences.
</p>
</dd>
<dt>&lsquo;<samp class="samp">svcd (vob)</samp>&rsquo;</dt>
<dd><p>MPEG-2 program stream (SVCD VOB) muxer.
</p>
<p>This muxer can be used to generate tracks in the format accepted by
the SVCD (Super Video Compact Disc) storage devices.
</p>
<p>This is the same as the &lsquo;<samp class="samp">vob</samp>&rsquo; muxer with a few differences.
</p></dd>
</dl>

<a name="Options-5"></a>
<h4 class="subsection">4.3.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-5" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-5" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">muxrate <var class="var">rate</var></samp></dt>
<dd><p>Set user-defined mux rate expressed as a number of bits/s. If not
specied the automatically computed mux rate is employed. Default value
is <code class="code">0</code>.
</p>
</dd>
<dt><samp class="option">preload <var class="var">delay</var></samp></dt>
<dd><p>Set initial demux-decode delay in microseconds. Default value is
<code class="code">500000</code>.
</p></dd>
</dl>

<a name="MOV_002fMPEG_002d4_002fISOMBFF-muxers"></a>
<h3 class="section">4.4 MOV/MPEG-4/ISOMBFF muxers<span class="pull-right"><a class="anchor hidden-xs" href="#MOV_002fMPEG_002d4_002fISOMBFF-muxers" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-MOV_002fMPEG_002d4_002fISOMBFF-muxers" aria-hidden="true">TOC</a></span></h3>

<p>This section covers formats belonging to the QuickTime / MOV family,
including the MPEG-4 Part 14 format and ISO base media file format
(ISOBMFF). These formats share a common structure based on the ISO
base media file format (ISOBMFF).
</p>
<p>The MOV format was originally developed for use with Apple QuickTime.
It was later used as the basis for the MPEG-4 Part 1 (later Part 14)
format, also known as ISO/IEC 14496-1. That format was then
generalized into ISOBMFF, also named MPEG-4 Part 12 format, ISO/IEC
14496-12, or ISO/IEC 15444-12.
</p>
<p>It comprises the following muxers.
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">3gp</samp>&rsquo;</dt>
<dd><p>Third Generation Partnership Project (3GPP) format for 3G UMTS
multimedia services
</p>
</dd>
<dt>&lsquo;<samp class="samp">3g2</samp>&rsquo;</dt>
<dd><p>Third Generation Partnership Project 2 (3GP2 or 3GPP2) format for 3G
CDMA2000 multimedia services, similar to &lsquo;<samp class="samp">3gp</samp>&rsquo; with extensions
and limitations
</p>
</dd>
<dt>&lsquo;<samp class="samp">f4v</samp>&rsquo;</dt>
<dd><p>Adobe Flash Video format
</p>
</dd>
<dt>&lsquo;<samp class="samp">ipod</samp>&rsquo;</dt>
<dd><p>MPEG-4 audio file format, as MOV/MP4 but limited to contain only audio
streams, typically played with the Apple ipod device
</p>
</dd>
<dt>&lsquo;<samp class="samp">ismv</samp>&rsquo;</dt>
<dd><p>Microsoft IIS (Internet Information Services) Smooth Streaming
Audio/Video (ISMV or ISMA) format. This is based on MPEG-4 Part 14
format with a few incompatible variants, used to stream media files
for the Microsoft IIS server.
</p>
</dd>
<dt>&lsquo;<samp class="samp">mov</samp>&rsquo;</dt>
<dd><p>QuickTime player format identified by the <code class="code">.mov</code> extension
</p>
</dd>
<dt>&lsquo;<samp class="samp">mp4</samp>&rsquo;</dt>
<dd><p>MP4 or MPEG-4 Part 14 format
</p>
</dd>
<dt>&lsquo;<samp class="samp">psp</samp>&rsquo;</dt>
<dd><p>PlayStation Portable MP4/MPEG-4 Part 14 format variant. This is based
on MPEG-4 Part 14 format with a few incompatible variants, used to
play files on PlayStation devices.
</p></dd>
</dl>

<a name="Fragmentation"></a>
<h4 class="subsection">4.4.1 Fragmentation<span class="pull-right"><a class="anchor hidden-xs" href="#Fragmentation" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Fragmentation" aria-hidden="true">TOC</a></span></h4>

<p>The &lsquo;<samp class="samp">mov</samp>&rsquo;, &lsquo;<samp class="samp">mp4</samp>&rsquo;, and &lsquo;<samp class="samp">ismv</samp>&rsquo; muxers support
fragmentation. Normally, a MOV/MP4 file has all the metadata about all
packets stored in one location.
</p>
<p>This data is usually written at the end of the file, but it can be
moved to the start for better playback by adding <code class="code">+faststart</code> to
the <code class="code">-movflags</code>, or using the <code class="command">qt-faststart</code> tool).
</p>
<p>A fragmented file consists of a number of fragments, where packets and
metadata about these packets are stored together. Writing a fragmented
file has the advantage that the file is decodable even if the writing
is interrupted (while a normal MOV/MP4 is undecodable if it is not
properly finished), and it requires less memory when writing very long
files (since writing normal MOV/MP4 files stores info about every
single packet in memory until the file is closed). The downside is
that it is less compatible with other applications.
</p>
<p>Fragmentation is enabled by setting one of the options that define
how to cut the file into fragments:
</p><dl class="table">
<dt><samp class="option">frag_duration</samp></dt>
<dt><samp class="option">frag_size</samp></dt>
<dt><samp class="option">min_frag_duration</samp></dt>
<dt><samp class="option">movflags +frag_keyframe</samp></dt>
<dt><samp class="option">movflags +frag_custom</samp></dt>
</dl>

<p>If more than one condition is specified, fragments are cut when one of
the specified conditions is fulfilled. The exception to this is the
option <samp class="option">min_frag_duration</samp>, which has to be fulfilled for any
of the other conditions to apply.
</p>
<a name="Options-6"></a>
<h4 class="subsection">4.4.2 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-6" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-6" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">brand <var class="var">brand_string</var></samp></dt>
<dd><p>Override major brand.
</p>
</dd>
<dt><samp class="option">empty_hdlr_name <var class="var">bool</var></samp></dt>
<dd><p>Enable to skip writing the name inside a <code class="code">hdlr</code> box.
Default is <code class="code">false</code>.
</p>
</dd>
<dt><samp class="option">encryption_key <var class="var">key</var></samp></dt>
<dd><p>set the media encryption key in hexadecimal format
</p>
</dd>
<dt><samp class="option">encryption_kid <var class="var">kid</var></samp></dt>
<dd><p>set the media encryption key identifier in hexadecimal format
</p>
</dd>
<dt><samp class="option">encryption_scheme <var class="var">scheme</var></samp></dt>
<dd><p>configure the encryption scheme, allowed values are &lsquo;<samp class="samp">none</samp>&rsquo;, and
&lsquo;<samp class="samp">cenc-aes-ctr</samp>&rsquo;
</p>
</dd>
<dt><samp class="option">frag_duration <var class="var">duration</var></samp></dt>
<dd><p>Create fragments that are <var class="var">duration</var> microseconds long.
</p>
</dd>
<dt><samp class="option">frag_interleave  <var class="var">number</var></samp></dt>
<dd><p>Interleave samples within fragments (max number of consecutive
samples, lower is tighter interleaving, but with more overhead. It is
set to <code class="code">0</code> by default.
</p>
</dd>
<dt><samp class="option">frag_size <var class="var">size</var></samp></dt>
<dd><p>create fragments that contain up to <var class="var">size</var> bytes of payload data
</p>
</dd>
<dt><samp class="option">iods_audio_profile <var class="var">profile</var></samp></dt>
<dd><p>specify iods number for the audio profile atom (from -1 to 255),
default is <code class="code">-1</code>
</p>
</dd>
<dt><samp class="option">iods_video_profile <var class="var">profile</var></samp></dt>
<dd><p>specify iods number for the video profile atom (from -1 to 255),
default is <code class="code">-1</code>
</p>
</dd>
<dt><samp class="option">ism_lookahead <var class="var">num_entries</var></samp></dt>
<dd><p>specify number of lookahead entries for ISM files (from 0 to 255),
default is <code class="code">0</code>
</p>
</dd>
<dt><samp class="option">min_frag_duration <var class="var">duration</var></samp></dt>
<dd><p>do not create fragments that are shorter than <var class="var">duration</var> microseconds long
</p>
</dd>
<dt><samp class="option">moov_size <var class="var">bytes</var></samp></dt>
<dd><p>Reserves space for the moov atom at the beginning of the file instead of placing the
moov atom at the end. If the space reserved is insufficient, muxing will fail.
</p>
</dd>
<dt><samp class="option">mov_gamma <var class="var">gamma</var></samp></dt>
<dd><p>specify gamma value for gama atom (as a decimal number from 0 to 10),
default is <code class="code">0.0</code>, must be set together with <code class="code">+ movflags</code>
</p>
</dd>
<dt><samp class="option">movflags <var class="var">flags</var></samp></dt>
<dd><p>Set various muxing switches. The following flags can be used:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">cmaf</samp>&rsquo;</dt>
<dd><p>write CMAF (Common Media Application Format) compatible fragmented
MP4 output
</p>
</dd>
<dt>&lsquo;<samp class="samp">dash</samp>&rsquo;</dt>
<dd><p>write DASH (Dynamic Adaptive Streaming over HTTP) compatible fragmented
MP4 output
</p>
</dd>
<dt>&lsquo;<samp class="samp">default_base_moof</samp>&rsquo;</dt>
<dd><p>Similarly to the &lsquo;<samp class="samp">omit_tfhd_offset</samp>&rsquo; flag, this flag avoids
writing the absolute base_data_offset field in tfhd atoms, but does so
by using the new default-base-is-moof flag instead. This flag is new
from 14496-12:2012. This may make the fragments easier to parse in
certain circumstances (avoiding basing track fragment location
calculations on the implicit end of the previous track fragment).
</p>
</dd>
<dt>&lsquo;<samp class="samp">delay_moov</samp>&rsquo;</dt>
<dd><p>delay writing the initial moov until the first fragment is cut, or
until the first fragment flush
</p>
</dd>
<dt>&lsquo;<samp class="samp">disable_chpl</samp>&rsquo;</dt>
<dd><p>Disable Nero chapter markers (chpl atom). Normally, both Nero chapters
and a QuickTime chapter track are written to the file. With this
option set, only the QuickTime chapter track will be written. Nero
chapters can cause failures when the file is reprocessed with certain
tagging programs, like mp3Tag 2.61a and iTunes 11.3, most likely other
versions are affected as well.
</p>
</dd>
<dt>&lsquo;<samp class="samp">faststart</samp>&rsquo;</dt>
<dd><p>Run a second pass moving the index (moov atom) to the beginning of the
file. This operation can take a while, and will not work in various
situations such as fragmented output, thus it is not enabled by
default.
</p>
</dd>
<dt>&lsquo;<samp class="samp">frag_custom</samp>&rsquo;</dt>
<dd><p>Allow the caller to manually choose when to cut fragments, by calling
<code class="code">av_write_frame(ctx, NULL)</code> to write a fragment with the packets
written so far. (This is only useful with other applications
integrating libavformat, not from <code class="command">ffmpeg</code>.)
</p>
</dd>
<dt>&lsquo;<samp class="samp">frag_discont</samp>&rsquo;</dt>
<dd><p>signal that the next fragment is discontinuous from earlier ones
</p>
</dd>
<dt>&lsquo;<samp class="samp">frag_every_frame</samp>&rsquo;</dt>
<dd><p>fragment at every frame
</p>
</dd>
<dt>&lsquo;<samp class="samp">frag_keyframe</samp>&rsquo;</dt>
<dd><p>start a new fragment at each video keyframe
</p>
</dd>
<dt>&lsquo;<samp class="samp">global_sidx</samp>&rsquo;</dt>
<dd><p>write a global sidx index at the start of the file
</p>
</dd>
<dt>&lsquo;<samp class="samp">isml</samp>&rsquo;</dt>
<dd><p>create a live smooth streaming feed (for pushing to a publishing point)
</p>
</dd>
<dt>&lsquo;<samp class="samp">negative_cts_offsets</samp>&rsquo;</dt>
<dd><p>Enables utilization of version 1 of the CTTS box, in which the CTS offsets can
be negative. This enables the initial sample to have DTS/CTS of zero, and
reduces the need for edit lists for some cases such as video tracks with
B-frames. Additionally, eases conformance with the DASH-IF interoperability
guidelines.
</p>
<p>This option is implicitly set when writing &lsquo;<samp class="samp">ismv</samp>&rsquo; (Smooth
Streaming) files.
</p>
</dd>
<dt>&lsquo;<samp class="samp">omit_tfhd_offset</samp>&rsquo;</dt>
<dd><p>Do not write any absolute base_data_offset in tfhd atoms. This avoids
tying fragments to absolute byte positions in the file/streams.
</p>
</dd>
<dt>&lsquo;<samp class="samp">prefer_icc</samp>&rsquo;</dt>
<dd><p>If writing colr atom prioritise usage of ICC profile if it exists in
stream packet side data.
</p>
</dd>
<dt>&lsquo;<samp class="samp">rtphint</samp>&rsquo;</dt>
<dd><p>add RTP hinting tracks to the output file
</p>
</dd>
<dt>&lsquo;<samp class="samp">separate_moof</samp>&rsquo;</dt>
<dd><p>Write a separate moof (movie fragment) atom for each track. Normally,
packets for all tracks are written in a moof atom (which is slightly
more efficient), but with this option set, the muxer writes one
moof/mdat pair for each track, making it easier to separate tracks.
</p>
</dd>
<dt>&lsquo;<samp class="samp">skip_sidx</samp>&rsquo;</dt>
<dd><p>Skip writing of sidx atom. When bitrate overhead due to sidx atom is
high, this option could be used for cases where sidx atom is not
mandatory. When the &lsquo;<samp class="samp">global_sidx</samp>&rsquo; flag is enabled, this option
is ignored.
</p>
</dd>
<dt>&lsquo;<samp class="samp">skip_trailer</samp>&rsquo;</dt>
<dd><p>skip writing the mfra/tfra/mfro trailer for fragmented files
</p>
</dd>
<dt>&lsquo;<samp class="samp">use_metadata_tags</samp>&rsquo;</dt>
<dd><p>use mdta atom for metadata
</p>
</dd>
<dt>&lsquo;<samp class="samp">write_colr</samp>&rsquo;</dt>
<dd><p>write colr atom even if the color info is unspecified. This flag is
experimental, may be renamed or changed, do not use from scripts.
</p>
</dd>
<dt>&lsquo;<samp class="samp">write_gama</samp>&rsquo;</dt>
<dd><p>write deprecated gama atom
</p>
</dd>
<dt>&lsquo;<samp class="samp">hybrid_fragmented</samp>&rsquo;</dt>
<dd><p>For recoverability - write the output file as a fragmented file.
This allows the intermediate file to be read while being written
(in particular, if the writing process is aborted uncleanly). When
writing is finished, the file is converted to a regular, non-fragmented
file, which is more compatible and allows easier and quicker seeking.
</p>
<p>If writing is aborted, the intermediate file can manually be
remuxed to get a regular, non-fragmented file of what had been
written into the unfinished file.
</p></dd>
</dl>

</dd>
<dt><samp class="option">movie_timescale <var class="var">scale</var></samp></dt>
<dd><p>Set the timescale written in the movie header box (<code class="code">mvhd</code>).
Range is 1 to INT_MAX. Default is <code class="code">1000</code>.
</p>
</dd>
<dt><samp class="option">rtpflags <var class="var">flags</var></samp></dt>
<dd><p>Add RTP hinting tracks to the output file.
</p>
<p>The following flags can be used:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">h264_mode0</samp>&rsquo;</dt>
<dd><p>use mode 0 for H.264 in RTP
</p>
</dd>
<dt>&lsquo;<samp class="samp">latm</samp>&rsquo;</dt>
<dd><p>use MP4A-LATM packetization instead of MPEG4-GENERIC for AAC
</p>
</dd>
<dt>&lsquo;<samp class="samp">rfc2190</samp>&rsquo;</dt>
<dd><p>use RFC 2190 packetization instead of RFC 4629 for H.263
</p>
</dd>
<dt>&lsquo;<samp class="samp">send_bye</samp>&rsquo;</dt>
<dd><p>send RTCP BYE packets when finishing
</p>
</dd>
<dt>&lsquo;<samp class="samp">skip_rtcp</samp>&rsquo;</dt>
<dd><p>do not send RTCP sender reports
</p></dd>
</dl>

</dd>
<dt><samp class="option">skip_iods <var class="var">bool</var></samp></dt>
<dd><p>skip writing iods atom (default value is <code class="code">true</code>)
</p>
</dd>
<dt><samp class="option">use_editlist <var class="var">bool</var></samp></dt>
<dd><p>use edit list (default value is <code class="code">auto</code>)
</p>
</dd>
<dt><samp class="option">use_stream_ids_as_track_ids <var class="var">bool</var></samp></dt>
<dd><p>use stream ids as track ids (default value is <code class="code">false</code>)
</p>
</dd>
<dt><samp class="option">video_track_timescale <var class="var">scale</var></samp></dt>
<dd><p>Set the timescale used for video tracks. Range is <code class="code">0</code> to INT_MAX. If
set to <code class="code">0</code>, the timescale is automatically set based on the
native stream time base. Default is <code class="code">0</code>.
</p>
</dd>
<dt><samp class="option">write_btrt <var class="var">bool</var></samp></dt>
<dd><p>Force or disable writing bitrate box inside stsd box of a track. The
box contains decoding buffer size (in bytes), maximum bitrate and
average bitrate for the track. The box will be skipped if none of
these values can be computed.  Default is <code class="code">-1</code> or <code class="code">auto</code>,
which will write the box only in MP4 mode.
</p>
</dd>
<dt><samp class="option">write_prft <var class="var">option</var></samp></dt>
<dd><p>Write producer time reference box (PRFT) with a specified time source for the
NTP field in the PRFT box. Set value as &lsquo;<samp class="samp">wallclock</samp>&rsquo; to specify timesource
as wallclock time and &lsquo;<samp class="samp">pts</samp>&rsquo; to specify timesource as input packets&rsquo; PTS
values.
</p>
</dd>
<dt><samp class="option">write_tmcd <var class="var">bool</var></samp></dt>
<dd><p>Specify <code class="code">on</code> to force writing a timecode track, <code class="code">off</code> to disable it
and <code class="code">auto</code> to write a timecode track only for mov and mp4 output (default).
</p>
<p>Setting value to &lsquo;<samp class="samp">pts</samp>&rsquo; is applicable only for a live encoding use case,
where PTS values are set as as wallclock time at the source. For example, an
encoding use case with decklink capture source where <samp class="option">video_pts</samp> and
<samp class="option">audio_pts</samp> are set to &lsquo;<samp class="samp">abs_wallclock</samp>&rsquo;.
</p></dd>
</dl>

<a name="Examples-5"></a>
<h4 class="subsection">4.4.3 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-5" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-5" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Push Smooth Streaming content in real time to a publishing point on
IIS with the &lsquo;<samp class="samp">ismv</samp>&rsquo; muxer using <code class="command">ffmpeg</code>:
<div class="example">
<pre class="example-preformatted">ffmpeg -re <var class="var">&lt;normal input/transcoding options&gt;</var> -movflags isml+frag_keyframe -f ismv http://server/publishingpoint.isml/Streams(Encoder1)
</pre></div>
</li></ul>

<a class="anchor" id="a64"></a><a name="a64-1"></a>
<h3 class="section">4.5 a64<span class="pull-right"><a class="anchor hidden-xs" href="#a64" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-a64" aria-hidden="true">TOC</a></span></h3>
<p>A64 Commodore 64 video muxer.
</p>
<p>This muxer accepts a single <code class="code">a64_multi</code> or <code class="code">a64_multi5</code>
codec video stream.
</p>
<a name="ac4"></a>
<h3 class="section">4.6 ac4<span class="pull-right"><a class="anchor hidden-xs" href="#ac4" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ac4" aria-hidden="true">TOC</a></span></h3>
<p>Raw AC-4 audio muxer.
</p>
<p>This muxer accepts a single <code class="code">ac4</code> audio stream.
</p>
<a name="Options-7"></a>
<h4 class="subsection">4.6.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-7" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-7" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">write_crc <var class="var">bool</var></samp></dt>
<dd><p>when enabled, write a CRC checksum for each packet to the output,
default is <code class="code">false</code>
</p></dd>
</dl>

<a class="anchor" id="adts"></a><a name="adts-1"></a>
<h3 class="section">4.7 adts<span class="pull-right"><a class="anchor hidden-xs" href="#adts" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-adts" aria-hidden="true">TOC</a></span></h3>
<p>Audio Data Transport Stream muxer.
</p>
<p>It accepts a single AAC stream.
</p>
<a name="Options-8"></a>
<h4 class="subsection">4.7.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-8" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-8" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">write_id3v2 <var class="var">bool</var></samp></dt>
<dd><p>Enable to write ID3v2.4 tags at the start of the stream. Default is
disabled.
</p>
</dd>
<dt><samp class="option">write_apetag <var class="var">bool</var></samp></dt>
<dd><p>Enable to write APE tags at the end of the stream. Default is
disabled.
</p>
</dd>
<dt><samp class="option">write_mpeg2 <var class="var">bool</var></samp></dt>
<dd><p>Enable to set MPEG version bit in the ADTS frame header to 1 which
indicates MPEG-2. Default is 0, which indicates MPEG-4.
</p></dd>
</dl>

<a class="anchor" id="aea"></a><a name="aea-1"></a>
<h3 class="section">4.8 aea<span class="pull-right"><a class="anchor hidden-xs" href="#aea" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-aea" aria-hidden="true">TOC</a></span></h3>
<p>MD STUDIO audio muxer.
</p>
<p>This muxer accepts a single ATRAC1 audio stream with either one or two channels
and a sample rate of 44100Hz.
</p>
<p>As AEA supports storing the track title, this muxer will also write
the title from stream&rsquo;s metadata to the container.
</p>
<a class="anchor" id="aiff"></a><a name="aiff-1"></a>
<h3 class="section">4.9 aiff<span class="pull-right"><a class="anchor hidden-xs" href="#aiff" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-aiff" aria-hidden="true">TOC</a></span></h3>
<p>Audio Interchange File Format muxer.
</p>
<a name="Options-9"></a>
<h4 class="subsection">4.9.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-9" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-9" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">write_id3v2 <var class="var">bool</var></samp></dt>
<dd><p>Enable ID3v2 tags writing when set to 1. Default is 0 (disabled).
</p>
</dd>
<dt><samp class="option">id3v2_version <var class="var">bool</var></samp></dt>
<dd><p>Select ID3v2 version to write. Currently only version 3 and 4 (aka.
ID3v2.3 and ID3v2.4) are supported. The default is version 4.
</p></dd>
</dl>

<a class="anchor" id="alp"></a><a name="alp-1"></a>
<h3 class="section">4.10 alp<span class="pull-right"><a class="anchor hidden-xs" href="#alp" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-alp" aria-hidden="true">TOC</a></span></h3>
<p>High Voltage Software&rsquo;s Lego Racers game audio muxer.
</p>
<p>It accepts a single ADPCM_IMA_ALP stream with no more than 2 channels
and a sample rate not greater than 44100 Hz.
</p>
<p>Extensions: <code class="code">tun</code>, <code class="code">pcm</code>
</p>
<a name="Options-10"></a>
<h4 class="subsection">4.10.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-10" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-10" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">type <var class="var">type</var></samp></dt>
<dd><p>Set file type.
</p>
<p><var class="var">type</var> accepts the following values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">tun</samp>&rsquo;</dt>
<dd><p>Set file type as music. Must have a sample rate of 22050 Hz.
</p>
</dd>
<dt>&lsquo;<samp class="samp">pcm</samp>&rsquo;</dt>
<dd><p>Set file type as sfx.
</p>
</dd>
<dt>&lsquo;<samp class="samp">auto</samp>&rsquo;</dt>
<dd><p>Set file type as per output file extension. <code class="code">.pcm</code> results in
type <code class="code">pcm</code> else type <code class="code">tun</code> is set. <var class="var">(default)</var>
</p></dd>
</dl>
</dd>
</dl>

<a name="amr"></a>
<h3 class="section">4.11 amr<span class="pull-right"><a class="anchor hidden-xs" href="#amr" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-amr" aria-hidden="true">TOC</a></span></h3>
<p>3GPP AMR (Adaptive Multi-Rate) audio muxer.
</p>
<p>It accepts a single audio stream containing an AMR NB stream.
</p>
<a name="amv"></a>
<h3 class="section">4.12 amv<span class="pull-right"><a class="anchor hidden-xs" href="#amv" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-amv" aria-hidden="true">TOC</a></span></h3>
<p>AMV (Actions Media Video) format muxer.
</p>
<a name="apm"></a>
<h3 class="section">4.13 apm<span class="pull-right"><a class="anchor hidden-xs" href="#apm" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-apm" aria-hidden="true">TOC</a></span></h3>
<p>Ubisoft Rayman 2 APM audio muxer.
</p>
<p>It accepts a single ADPCM IMA APM audio stream.
</p>
<a name="apng-1"></a>
<h3 class="section">4.14 apng<span class="pull-right"><a class="anchor hidden-xs" href="#apng-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-apng-1" aria-hidden="true">TOC</a></span></h3>
<p>Animated Portable Network Graphics muxer.
</p>
<p>It accepts a single APNG video stream.
</p>
<a name="Options-11"></a>
<h4 class="subsection">4.14.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-11" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-11" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">final_delay <var class="var">delay</var></samp></dt>
<dd><p>Force a delay expressed in seconds after the last frame of each
repetition. Default value is <code class="code">0.0</code>.
</p>
</dd>
<dt><samp class="option">plays <var class="var">repetitions</var></samp></dt>
<dd><p>specify how many times to play the content, <code class="code">0</code> causes an infinte
loop, with <code class="code">1</code> there is no loop
</p></dd>
</dl>

<a name="Examples-6"></a>
<h4 class="subsection">4.14.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-6" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-6" aria-hidden="true">TOC</a></span></h4>
<ul class="itemize mark-bullet">
<li>Use <code class="command">ffmpeg</code> to generate an APNG output with 2 repetitions,
and with a delay of half a second after the first repetition:
<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -final_delay 0.5 -plays 2 out.apng
</pre></div>
</li></ul>

<a name="argo_005fasf"></a>
<h3 class="section">4.15 argo_asf<span class="pull-right"><a class="anchor hidden-xs" href="#argo_005fasf" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-argo_005fasf" aria-hidden="true">TOC</a></span></h3>
<p>Argonaut Games ASF audio muxer.
</p>
<p>It accepts a single ADPCM audio stream.
</p>
<a name="Options-12"></a>
<h4 class="subsection">4.15.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-12" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-12" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">version_major <var class="var">version</var></samp></dt>
<dd><p>override file major version, specified as an integer, default value is
<code class="code">2</code>
</p>
</dd>
<dt><samp class="option">version_minor <var class="var">version</var></samp></dt>
<dd><p>override file minor version, specified as an integer, default value is
<code class="code">1</code>
</p>
</dd>
<dt><samp class="option">name <var class="var">name</var></samp></dt>
<dd><p>Embed file name into file, if not specified use the output file
name. The name is truncated to 8 characters.
</p></dd>
</dl>

<a name="argo_005fcvg"></a>
<h3 class="section">4.16 argo_cvg<span class="pull-right"><a class="anchor hidden-xs" href="#argo_005fcvg" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-argo_005fcvg" aria-hidden="true">TOC</a></span></h3>
<p>Argonaut Games CVG audio muxer.
</p>
<p>It accepts a single one-channel ADPCM 22050Hz audio stream.
</p>
<p>The <samp class="option">loop</samp> and <samp class="option">reverb</samp> options set the corresponding
flags in the header which can be later retrieved to process the audio
stream accordingly.
</p>
<a name="Options-13"></a>
<h4 class="subsection">4.16.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-13" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-13" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">skip_rate_check <var class="var">bool</var></samp></dt>
<dd><p>skip sample rate check (default is <code class="code">false</code>)
</p>
</dd>
<dt><samp class="option">loop <var class="var">bool</var></samp></dt>
<dd><p>set loop flag (default is <code class="code">false</code>)
</p>
</dd>
<dt><samp class="option">reverb <var class="var">boolean</var></samp></dt>
<dd><p>set reverb flag (default is <code class="code">true</code>)
</p></dd>
</dl>

<a class="anchor" id="asf"></a><a name="asf_002c-asf_005fstream"></a>
<h3 class="section">4.17 asf, asf_stream<span class="pull-right"><a class="anchor hidden-xs" href="#asf_002c-asf_005fstream" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-asf_002c-asf_005fstream" aria-hidden="true">TOC</a></span></h3>
<p>Advanced / Active Systems (or Streaming) Format audio muxer.
</p>
<p>The &lsquo;<samp class="samp">asf_stream</samp>&rsquo; variant should be selected for streaming.
</p>
<p>Note that Windows Media Audio (wma) and Windows Media Video (wmv) use this
muxer too.
</p>
<a name="Options-14"></a>
<h4 class="subsection">4.17.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-14" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-14" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">packet_size <var class="var">size</var></samp></dt>
<dd><p>Set the muxer packet size as a number of bytes. By tuning this setting
you may reduce data fragmentation or muxer overhead depending on your
source. Default value is <code class="code">3200</code>, minimum is <code class="code">100</code>, maximum
is <code class="code">64Ki</code>.
</p></dd>
</dl>

<a name="ass"></a>
<h3 class="section">4.18 ass<span class="pull-right"><a class="anchor hidden-xs" href="#ass" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ass" aria-hidden="true">TOC</a></span></h3>
<p>ASS/SSA (SubStation Alpha) subtitles muxer.
</p>
<p>It accepts a single ASS subtitles stream.
</p>
<a name="Options-15"></a>
<h4 class="subsection">4.18.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-15" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-15" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">ignore_readorder <var class="var">bool</var></samp></dt>
<dd><p>Write dialogue events immediately, even if they are out-of-order,
default is <code class="code">false</code>, otherwise they are cached until the expected
time event is found.
</p></dd>
</dl>

<a name="ast"></a>
<h3 class="section">4.19 ast<span class="pull-right"><a class="anchor hidden-xs" href="#ast" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ast" aria-hidden="true">TOC</a></span></h3>
<p>AST (Audio Stream) muxer.
</p>
<p>This format is used to play audio on some Nintendo Wii games.
</p>
<p>It accepts a single audio stream.
</p>
<p>The <samp class="option">loopstart</samp> and <samp class="option">loopend</samp> options can be used to
define a section of the file to loop for players honoring such
options.
</p>
<a name="Options-16"></a>
<h4 class="subsection">4.19.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-16" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-16" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">loopstart <var class="var">start</var></samp></dt>
<dd><p>Specify loop start position expressesd in milliseconds, from <code class="code">-1</code>
to <code class="code">INT_MAX</code>, in case <code class="code">-1</code> is set then no loop is specified
(default -1) and the <samp class="option">loopend</samp> value is ignored.
</p>
</dd>
<dt><samp class="option">loopend <var class="var">end</var></samp></dt>
<dd><p>Specify loop end position expressed in milliseconds, from <code class="code">0</code> to
<code class="code">INT_MAX</code>, default is <code class="code">0</code>, in case <code class="code">0</code> is set it
assumes the total stream duration.
</p></dd>
</dl>

<a name="au"></a>
<h3 class="section">4.20 au<span class="pull-right"><a class="anchor hidden-xs" href="#au" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-au" aria-hidden="true">TOC</a></span></h3>
<p>SUN AU audio muxer.
</p>
<p>It accepts a single audio stream.
</p>
<a class="anchor" id="avi"></a><a name="avi-1"></a>
<h3 class="section">4.21 avi<span class="pull-right"><a class="anchor hidden-xs" href="#avi" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-avi" aria-hidden="true">TOC</a></span></h3>
<p>Audio Video Interleaved muxer.
</p>
<p>AVI is a proprietary format developed by Microsoft, and later formally specified
through the Open DML specification.
</p>
<p>Because of differences in players implementations, it might be required to set
some options to make sure that the generated output can be correctly played by
the target player.
</p>
<a name="Options-17"></a>
<h4 class="subsection">4.21.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-17" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-17" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">flipped_raw_rgb <var class="var">bool</var></samp></dt>
<dd><p>If set to <code class="code">true</code>, store positive height for raw RGB bitmaps, which
indicates bitmap is stored bottom-up. Note that this option does not flip the
bitmap which has to be done manually beforehand, e.g. by using the &lsquo;<samp class="samp">vflip</samp>&rsquo;
filter. Default is <code class="code">false</code> and indicates bitmap is stored top down.
</p>
</dd>
<dt><samp class="option">reserve_index_space <var class="var">size</var></samp></dt>
<dd><p>Reserve the specified amount of bytes for the OpenDML master index of each
stream within the file header. By default additional master indexes are
embedded within the data packets if there is no space left in the first master
index and are linked together as a chain of indexes. This index structure can
cause problems for some use cases, e.g. third-party software strictly relying
on the OpenDML index specification or when file seeking is slow. Reserving
enough index space in the file header avoids these problems.
</p>
<p>The required index space depends on the output file size and should be about 16
bytes per gigabyte. When this option is omitted or set to zero the necessary
index space is guessed.
</p>
<p>Default value is <code class="code">0</code>.
</p>
</dd>
<dt><samp class="option">write_channel_mask <var class="var">bool</var></samp></dt>
<dd><p>Write the channel layout mask into the audio stream header.
</p>
<p>This option is enabled by default. Disabling the channel mask can be useful in
specific scenarios, e.g. when merging multiple audio streams into one for
compatibility with software that only supports a single audio stream in AVI
(see <a data-manual="ffmpeg-filters" href="./ffmpeg-filters.html#amerge">the &quot;amerge&quot; section in the ffmpeg-filters manual</a>).
</p></dd>
</dl>

<a name="avif"></a>
<h3 class="section">4.22 avif<span class="pull-right"><a class="anchor hidden-xs" href="#avif" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-avif" aria-hidden="true">TOC</a></span></h3>
<p>AV1 (Alliance for Open Media Video codec 1) image format muxer.
</p>
<p>This muxers stores images encoded using the AV1 codec.
</p>
<p>It accepts one or two video streams. In case two video streams are
provided, the second one shall contain a single plane storing the
alpha mask.
</p>
<p>In case more than one image is provided, the generated output is
considered an animated AVIF and the number of loops can be specified
with the <samp class="option">loop</samp> option.
</p>
<p>This is based on the specification by Alliance for Open Media at url
<a class="url" href="https://aomediacodec.github.io/av1-avif">https://aomediacodec.github.io/av1-avif</a>.
</p>
<a name="Options-18"></a>
<h4 class="subsection">4.22.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-18" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-18" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">loop <var class="var">count</var></samp></dt>
<dd><p>number of times to loop an animated AVIF, <code class="code">0</code> specify an infinite
loop, default is <code class="code">0</code>
</p>
</dd>
<dt><samp class="option">movie_timescale <var class="var">timescale</var></samp></dt>
<dd><p>Set the timescale written in the movie header box (<code class="code">mvhd</code>).
Range is 1 to INT_MAX. Default is <code class="code">1000</code>.
</p></dd>
</dl>

<a name="avm2"></a>
<h3 class="section">4.23 avm2<span class="pull-right"><a class="anchor hidden-xs" href="#avm2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-avm2" aria-hidden="true">TOC</a></span></h3>
<p>ShockWave Flash (SWF) / ActionScript Virtual Machine 2 (AVM2) format muxer.
</p>
<p>It accepts one audio stream, one video stream, or both.
</p>
<a name="bit"></a>
<h3 class="section">4.24 bit<span class="pull-right"><a class="anchor hidden-xs" href="#bit" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-bit" aria-hidden="true">TOC</a></span></h3>
<p>G.729 (.bit) file format muxer.
</p>
<p>It accepts a single G.729 audio stream.
</p>
<a name="caf"></a>
<h3 class="section">4.25 caf<span class="pull-right"><a class="anchor hidden-xs" href="#caf" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-caf" aria-hidden="true">TOC</a></span></h3>
<p>Apple CAF (Core Audio Format) muxer.
</p>
<p>It accepts a single audio stream.
</p>
<a name="codec2"></a>
<h3 class="section">4.26 codec2<span class="pull-right"><a class="anchor hidden-xs" href="#codec2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-codec2" aria-hidden="true">TOC</a></span></h3>
<p>Codec2 audio audio muxer.
</p>
<p>It accepts a single codec2 audio stream.
</p>
<a class="anchor" id="chromaprint"></a><a name="chromaprint-1"></a>
<h3 class="section">4.27 chromaprint<span class="pull-right"><a class="anchor hidden-xs" href="#chromaprint" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-chromaprint" aria-hidden="true">TOC</a></span></h3>
<p>Chromaprint fingerprinter muxers.
</p>
<p>To enable compilation of this filter you need to configure FFmpeg with
<code class="code">--enable-chromaprint</code>.
</p>
<p>This muxer feeds audio data to the Chromaprint library, which
generates a fingerprint for the provided audio data. See:
<a class="url" href="https://acoustid.org/chromaprint">https://acoustid.org/chromaprint</a>
</p>
<p>It takes a single signed native-endian 16-bit raw audio stream of at
most 2 channels.
</p>
<a name="Options-19"></a>
<h4 class="subsection">4.27.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-19" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-19" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">algorithm <var class="var">version</var></samp></dt>
<dd><p>Select version of algorithm to fingerprint with. Range is <code class="code">0</code> to
<code class="code">4</code>. Version <code class="code">3</code> enables silence detection. Default is <code class="code">1</code>.
</p>
</dd>
<dt><samp class="option">fp_format <var class="var">format</var></samp></dt>
<dd><p>Format to output the fingerprint as. Accepts the following options:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">base64</samp>&rsquo;</dt>
<dd><p>Base64 compressed fingerprint <em class="emph">(default)</em>
</p>
</dd>
<dt>&lsquo;<samp class="samp">compressed</samp>&rsquo;</dt>
<dd><p>Binary compressed fingerprint
</p>
</dd>
<dt>&lsquo;<samp class="samp">raw</samp>&rsquo;</dt>
<dd><p>Binary raw fingerprint
</p></dd>
</dl>

</dd>
<dt><samp class="option">silence_threshold <var class="var">threshold</var></samp></dt>
<dd><p>Threshold for detecting silence. Range is from <code class="code">-1</code> to
<code class="code">32767</code>, where <code class="code">-1</code> disables silence detection. Silence
detection can only be used with version <code class="code">3</code> of the algorithm.
</p>
<p>Silence detection must be disabled for use with the AcoustID
service. Default is <code class="code">-1</code>.
</p></dd>
</dl>

<a class="anchor" id="crc"></a><a name="crc-1"></a>
<h3 class="section">4.28 crc<span class="pull-right"><a class="anchor hidden-xs" href="#crc" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-crc" aria-hidden="true">TOC</a></span></h3>
<p>CRC (Cyclic Redundancy Check) muxer.
</p>
<p>This muxer computes and prints the Adler-32 CRC of all the input audio
and video frames. By default audio frames are converted to signed
16-bit raw audio and video frames to raw video before computing the
CRC.
</p>
<p>The output of the muxer consists of a single line of the form:
CRC=0x<var class="var">CRC</var>, where <var class="var">CRC</var> is a hexadecimal number 0-padded to
8 digits containing the CRC for all the decoded input frames.
</p>
<p>See also the <a class="ref" href="#framecrc">framecrc</a> muxer.
</p>
<a name="Examples-7"></a>
<h4 class="subsection">4.28.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-7" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-7" aria-hidden="true">TOC</a></span></h4>
<ul class="itemize mark-bullet">
<li>Use <code class="command">ffmpeg</code> to compute the CRC of the input, and store it in
the file <samp class="file">out.crc</samp>:
<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f crc out.crc
</pre></div>

</li><li>Use <code class="command">ffmpeg</code> to print the CRC to stdout with the command:
<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f crc -
</pre></div>

</li><li>You can select the output format of each frame with <code class="command">ffmpeg</code> by
specifying the audio and video codec and format. For example, to
compute the CRC of the input audio converted to PCM unsigned 8-bit
and the input video converted to MPEG-2 video, use the command:
<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -c:a pcm_u8 -c:v mpeg2video -f crc -
</pre></div>
</li></ul>

<a class="anchor" id="dash"></a><a name="dash-2"></a>
<h3 class="section">4.29 dash<span class="pull-right"><a class="anchor hidden-xs" href="#dash-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dash-1" aria-hidden="true">TOC</a></span></h3>
<p>Dynamic Adaptive Streaming over HTTP (DASH) muxer.
</p>
<p>This muxer creates segments and manifest files according to the
MPEG-DASH standard ISO/IEC 23009-1:2014 and following standard
updates.
</p>
<p>For more information see:
</p><ul class="itemize mark-bullet">
<li>ISO DASH Specification: <a class="url" href="http://standards.iso.org/ittf/PubliclyAvailableStandards/c065274_ISO_IEC_23009-1_2014.zip">http://standards.iso.org/ittf/PubliclyAvailableStandards/c065274_ISO_IEC_23009-1_2014.zip</a>
</li><li>WebM DASH Specification: <a class="url" href="https://sites.google.com/a/webmproject.org/wiki/adaptive-streaming/webm-dash-specification">https://sites.google.com/a/webmproject.org/wiki/adaptive-streaming/webm-dash-specification</a>
</li></ul>

<p>This muxer creates an MPD (Media Presentation Description) manifest
file and segment files for each stream. Segment files are placed in
the same directory of the MPD manifest file.
</p>
<p>The segment filename might contain pre-defined identifiers used in the
manifest <code class="code">SegmentTemplate</code> section as defined in section
*******.4 of the standard.
</p>
<p>Available identifiers are <code class="code">$RepresentationID$</code>, <code class="code">$Number$</code>,
<code class="code">$Bandwidth$</code>, and <code class="code">$Time$</code>. In addition to the standard
identifiers, an ffmpeg-specific <code class="code">$ext$</code> identifier is also
supported. When specified, <code class="command">ffmpeg</code> will replace <code class="code">$ext$</code>
in the file name with muxing format&rsquo;s extensions such as <code class="code">mp4</code>,
<code class="code">webm</code> etc.
</p>
<a name="Options-20"></a>
<h4 class="subsection">4.29.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-20" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-20" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">adaptation_sets <var class="var">adaptation_sets</var></samp></dt>
<dd><p>Assign streams to adaptation sets, specified in the MPD manifest
<code class="code">AdaptationSets</code> section.
</p>
<p>An adaptation set contains a set of one or more streams accessed as a
single subset, e.g. corresponding streams encoded at different size
selectable by the user depending on the available bandwidth, or to
different audio streams with a different language.
</p>
<p>Each adaptation set is specified with the syntax:
</p><div class="example">
<pre class="example-preformatted">id=<var class="var">index</var>,streams=<var class="var">streams</var>
</pre></div>

<p>where <var class="var">index</var> must be a numerical index, and <var class="var">streams</var> is a
sequence of <code class="code">,</code>-separated stream indices. Multiple adaptation
sets can be specified, separated by spaces.
</p>
<p>To map all video (or audio) streams to an adaptation set, <code class="code">v</code> (or
<code class="code">a</code>) can be used as stream identifier instead of IDs.
</p>
<p>When no assignment is defined, this defaults to an adaptation set for
each stream.
</p>
<p>The following optional fields can also be specified:
</p>
<dl class="table">
<dt><samp class="option">descriptor</samp></dt>
<dd><p>Define the descriptor as defined by ISO/IEC 23009-1:2014/Amd.2:2015.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">&lt;SupplementalProperty schemeIdUri=\&quot;urn:mpeg:dash:srd:2014\&quot; value=\&quot;0,0,0,1,1,2,2\&quot;/&gt;
</pre></div>

<p>The descriptor string should be a self-closing XML tag.
</p>
</dd>
<dt><samp class="option">frag_duration</samp></dt>
<dd><p>Override the global fragment duration specified with the
<samp class="option">frag_duration</samp> option.
</p>
</dd>
<dt><samp class="option">frag_type</samp></dt>
<dd><p>Override the global fragment type specified with the
<samp class="option">frag_type</samp> option.
</p>
</dd>
<dt><samp class="option">seg_duration</samp></dt>
<dd><p>Override the global segment duration specified with the
<samp class="option">seg_duration</samp> option.
</p>
</dd>
<dt><samp class="option">trick_id</samp></dt>
<dd><p>Mark an adaptation set as containing streams meant to be used for
Trick Mode for the referenced adaptation set.
</p></dd>
</dl>

<p>A few examples of possible values for the <samp class="option">adaptation_sets</samp>
option follow:
</p><div class="example">
<pre class="example-preformatted">id=0,seg_duration=2,frag_duration=1,frag_type=duration,streams=v id=1,seg_duration=2,frag_type=none,streams=a
</pre></div>

<div class="example">
<pre class="example-preformatted">id=0,seg_duration=2,frag_type=none,streams=0 id=1,seg_duration=10,frag_type=none,trick_id=0,streams=1
</pre></div>

</dd>
<dt><samp class="option">dash_segment_type <var class="var">type</var></samp></dt>
<dd><p>Set DASH segment files type.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">auto</samp>&rsquo;</dt>
<dd><p>The dash segment files format will be selected based on the stream
codec. This is the default mode.
</p></dd>
<dt>&lsquo;<samp class="samp">mp4</samp>&rsquo;</dt>
<dd><p>the dash segment files will be in ISOBMFF/MP4 format
</p></dd>
<dt>&lsquo;<samp class="samp">webm</samp>&rsquo;</dt>
<dd><p>the dash segment files will be in WebM format
</p></dd>
</dl>

</dd>
<dt><samp class="option">extra_window_size <var class="var">size</var></samp></dt>
<dd><p>Set the maximum number of segments kept outside of the manifest before
removing from disk.
</p>
</dd>
<dt><samp class="option">format_options <var class="var">options_list</var></samp></dt>
<dd><p>Set container format (mp4/webm) options using a <code class="code">:</code>-separated list of
key=value parameters. Values containing <code class="code">:</code> special characters must be
escaped.
</p>
</dd>
<dt><samp class="option">frag_duration <var class="var">duration</var></samp></dt>
<dd><p>Set the length in seconds of fragments within segments, fractional
value can also be set.
</p>
</dd>
<dt><samp class="option">frag_type <var class="var">type</var></samp></dt>
<dd><p>Set the type of interval for fragmentation.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">auto</samp>&rsquo;</dt>
<dd><p>set one fragment per segment
</p>
</dd>
<dt>&lsquo;<samp class="samp">every_frame</samp>&rsquo;</dt>
<dd><p>fragment at every frame
</p>
</dd>
<dt>&lsquo;<samp class="samp">duration</samp>&rsquo;</dt>
<dd><p>fragment at specific time intervals
</p>
</dd>
<dt>&lsquo;<samp class="samp">pframes</samp>&rsquo;</dt>
<dd><p>fragment at keyframes and following P-Frame reordering (Video only,
experimental)
</p></dd>
</dl>

</dd>
<dt><samp class="option">global_sidx <var class="var">bool</var></samp></dt>
<dd><p>Write global <code class="code">SIDX</code> atom. Applicable only for single file, mp4
output, non-streaming mode.
</p>
</dd>
<dt><samp class="option">hls_master_name <var class="var">file_name</var></samp></dt>
<dd><p>HLS master playlist name. Default is <samp class="file">master.m3u8</samp>.
</p>
</dd>
<dt><samp class="option">hls_playlist <var class="var">bool</var></samp></dt>
<dd><p>Generate HLS playlist files. The master playlist is generated with
filename specified by the <samp class="option">hls_master_name</samp> option. One media
playlist file is generated for each stream with filenames
<samp class="file">media_0.m3u8</samp>, <samp class="file">media_1.m3u8</samp>, etc.
</p>
</dd>
<dt><samp class="option">http_opts <var class="var">http_opts</var></samp></dt>
<dd><p>Specify a list of <code class="code">:</code>-separated key=value options to pass to the
underlying HTTP protocol. Applicable only for HTTP output.
</p>
</dd>
<dt><samp class="option">http_persistent <var class="var">bool</var></samp></dt>
<dd><p>Use persistent HTTP connections. Applicable only for HTTP output.
</p>
</dd>
<dt><samp class="option">http_user_agent <var class="var">user_agent</var></samp></dt>
<dd><p>Override User-Agent field in HTTP header. Applicable only for HTTP
output.
</p>
</dd>
<dt><samp class="option">ignore_io_errors <var class="var">bool</var></samp></dt>
<dd><p>Ignore IO errors during open and write. Useful for long-duration runs
with network output. This is disabled by default.
</p>
</dd>
<dt><samp class="option">index_correction <var class="var">bool</var></samp></dt>
<dd><p>Enable or disable segment index correction logic. Applicable only when
<samp class="option">use_template</samp> is enabled and <samp class="option">use_timeline</samp> is
disabled. This is disabled by default.
</p>
<p>When enabled, the logic monitors the flow of segment indexes. If a
streams&rsquo;s segment index value is not at the expected real time
position, then the logic corrects that index value.
</p>
<p>Typically this logic is needed in live streaming use cases. The
network bandwidth fluctuations are common during long run
streaming. Each fluctuation can cause the segment indexes fall behind
the expected real time position.
</p>
</dd>
<dt><samp class="option">init_seg_name <var class="var">init_name</var></samp></dt>
<dd><p>DASH-templated name to use for the initialization segment. Default is
<code class="code">init-stream$RepresentationID$.$ext$</code>. <code class="code">$ext$</code> is replaced
with the file name extension specific for the segment format.
</p>
</dd>
<dt><samp class="option">ldash <var class="var">bool</var></samp></dt>
<dd><p>Enable Low-latency Dash by constraining the presence and values of
some elements. This is disabled by default.
</p>
</dd>
<dt><samp class="option">lhls <var class="var">bool</var></samp></dt>
<dd><p>Enable Low-latency HLS (LHLS). Add <code class="code">#EXT-X-PREFETCH</code> tag with
current segment&rsquo;s URI. hls.js player folks are trying to standardize
an open LHLS spec. The draft spec is available at
<a class="url" href="https://github.com/video-dev/hlsjs-rfcs/blob/lhls-spec/proposals/0001-lhls.md">https://github.com/video-dev/hlsjs-rfcs/blob/lhls-spec/proposals/0001-lhls.md</a>.
</p>
<p>This option tries to comply with the above open spec. It enables
<samp class="option">streaming</samp> and <samp class="option">hls_playlist</samp> options automatically.
This is an experimental feature.
</p>
<p>Note: This is not Apple&rsquo;s version LHLS. See
<a class="url" href="https://datatracker.ietf.org/doc/html/draft-pantos-hls-rfc8216bis">https://datatracker.ietf.org/doc/html/draft-pantos-hls-rfc8216bis</a>
</p>
</dd>
<dt><samp class="option">master_m3u8_publish_rate <var class="var">segment_intervals_count</var></samp></dt>
<dd><p>Publish master playlist repeatedly every after specified number of
segment intervals.
</p>
</dd>
<dt><samp class="option">max_playback_rate <var class="var">rate</var></samp></dt>
<dd><p>Set the maximum playback rate indicated as appropriate for the
purposes of automatically adjusting playback latency and buffer
occupancy during normal playback by clients.
</p>
</dd>
<dt><samp class="option">media_seg_name <var class="var">segment_name</var></samp></dt>
<dd><p>DASH-templated name to use for the media segments. Default is
<code class="code">chunk-stream$RepresentationID$-$Number%05d$.$ext$</code>. <code class="code">$ext$</code>
is replaced with the file name extension specific for the segment
format.
</p>
</dd>
<dt><samp class="option">method <var class="var">method</var></samp></dt>
<dd><p>Use the given HTTP method to create output files. Generally set to <code class="code">PUT</code>
or <code class="code">POST</code>.
</p>
</dd>
<dt><samp class="option">min_playback_rate <var class="var">rate</var></samp></dt>
<dd><p>Set the minimum playback rate indicated as appropriate for the
purposes of automatically adjusting playback latency and buffer
occupancy during normal playback by clients.
</p>
</dd>
<dt><samp class="option">mpd_profile <var class="var">flags</var></samp></dt>
<dd><p>Set one or more MPD manifest profiles.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">dash</samp>&rsquo;</dt>
<dd><p>MPEG-DASH ISO Base media file format live profile
</p></dd>
<dt>&lsquo;<samp class="samp">dvb_dash</samp>&rsquo;</dt>
<dd><p>DVB-DASH profile
</p></dd>
</dl>

<p>Default value is <code class="code">dash</code>.
</p>
</dd>
<dt><samp class="option">remove_at_exit <var class="var">bool</var></samp></dt>
<dd><p>Enable or disable removal of all segments when finished. This is
disabled by default.
</p>
</dd>
<dt><samp class="option">seg_duration <var class="var">duration</var></samp></dt>
<dd><p>Set the segment length in seconds (fractional value can be set). The
value is treated as average segment duration when the
<samp class="option">use_template</samp> option is enabled and the <samp class="option">use_timeline</samp>
option is disabled and as minimum segment duration for all the other
use cases.
</p>
<p>Default value is <code class="code">5</code>.
</p>
</dd>
<dt><samp class="option">single_file <var class="var">bool</var></samp></dt>
<dd><p>Enable or disable storing all segments in one file, accessed using
byte ranges. This is disabled by default.
</p>
<p>The name of the single file can be specified with the
<samp class="option">single_file_name</samp> option, if not specified assume the basename
of the manifest file with the output format extension.
</p>
</dd>
<dt><samp class="option">single_file_name <var class="var">file_name</var></samp></dt>
<dd><p>DASH-templated name to use for the manifest <code class="code">baseURL</code>
element. Imply that the <samp class="option">single_file</samp> option is set to
<var class="var">true</var>. In the template, <code class="code">$ext$</code> is replaced with the file
name extension specific for the segment format.
</p>
</dd>
<dt><samp class="option">streaming <var class="var">bool</var></samp></dt>
<dd><p>Enable or disable chunk streaming mode of output. In chunk streaming
mode, each frame will be a <code class="code">moof</code> fragment which forms a
chunk. This is disabled by default.
</p>
</dd>
<dt><samp class="option">target_latency <var class="var">target_latency</var></samp></dt>
<dd><p>Set an intended target latency in seconds for serving (fractional
value can be set). Applicable only when the <samp class="option">streaming</samp> and
<samp class="option">write_prft</samp> options are enabled. This is an informative fields
clients can use to measure the latency of the service.
</p>
</dd>
<dt><samp class="option">timeout <var class="var">timeout</var></samp></dt>
<dd><p>Set timeout for socket I/O operations expressed in seconds (fractional
value can be set). Applicable only for HTTP output.
</p>
</dd>
<dt><samp class="option">update_period <var class="var">period</var></samp></dt>
<dd><p>Set the MPD update period, for dynamic content. The unit is
second. If set to <code class="code">0</code>, the period is automatically computed.
</p>
<p>Default value is <code class="code">0</code>.
</p>
</dd>
<dt><samp class="option">use_template <var class="var">bool</var></samp></dt>
<dd><p>Enable or disable use of <code class="code">SegmentTemplate</code> instead of
<code class="code">SegmentList</code> in the manifest. This is enabled by default.
</p>
</dd>
<dt><samp class="option">use_timeline <var class="var">bool</var></samp></dt>
<dd><p>Enable or disable use of <code class="code">SegmentTimeline</code> within the
<code class="code">SegmentTemplate</code> manifest section. This is enabled by default.
</p>
</dd>
<dt><samp class="option">utc_timing_url <var class="var">url</var></samp></dt>
<dd><p>URL of the page that will return the UTC timestamp in ISO
format, for example <code class="code">https://time.akamai.com/?iso</code>
</p>
</dd>
<dt><samp class="option">window_size <var class="var">size</var></samp></dt>
<dd><p>Set the maximum number of segments kept in the manifest, discard the
oldest one. This is useful for live streaming.
</p>
<p>If the value is <code class="code">0</code>, all segments are kept in the
manifest. Default value is <code class="code">0</code>.
</p>
</dd>
<dt><samp class="option">write_prft <var class="var">write_prft</var></samp></dt>
<dd><p>Write Producer Reference Time elements on supported streams. This also
enables writing prft boxes in the underlying muxer. Applicable only
when the <var class="var">utc_url</var> option is enabled. It is set to <var class="var">auto</var> by
default, in which case the muxer will attempt to enable it only in
modes that require it.
</p></dd>
</dl>

<a name="Example"></a>
<h4 class="subsection">4.29.2 Example<span class="pull-right"><a class="anchor hidden-xs" href="#Example" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Example" aria-hidden="true">TOC</a></span></h4>
<p>Generate a DASH output reading from an input source in realtime using
<code class="command">ffmpeg</code>.
</p>
<p>Two multimedia streams are generated from the input file, both
containing a video stream encoded through &lsquo;<samp class="samp">libx264</samp>&rsquo;, and an audio
stream encoded with &lsquo;<samp class="samp">libfdk_aac</samp>&rsquo;. The first multimedia stream
contains video with a bitrate of 800k and audio at the default rate,
the second with video scaled to 320x170 pixels at 300k and audio
resampled at 22005 Hz.
</p>
<p>The <samp class="option">window_size</samp> option keeps only the latest 5 segments with
the default duration of 5 seconds.
</p>
<div class="example">
<pre class="example-preformatted">ffmpeg -re -i &lt;input&gt; -map 0 -map 0 -c:a libfdk_aac -c:v libx264 \
-b:v:0 800k -profile:v:0 main \
-b:v:1 300k -s:v:1 320x170 -profile:v:1 baseline -ar:a:1 22050 \
-bf 1 -keyint_min 120 -g 120 -sc_threshold 0 -b_strategy 0 \
-use_timeline 1 -use_template 1 -window_size 5 \
-adaptation_sets &quot;id=0,streams=v id=1,streams=a&quot; \
-f dash /path/to/out.mpd
</pre></div>

<a name="daud"></a>
<h3 class="section">4.30 daud<span class="pull-right"><a class="anchor hidden-xs" href="#daud" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-daud" aria-hidden="true">TOC</a></span></h3>
<p>D-Cinema audio muxer.
</p>
<p>It accepts a single 6-channels audio stream resampled at 96000 Hz
encoded with the &lsquo;<samp class="samp">pcm_24daud</samp>&rsquo; codec.
</p>
<a name="Example-1"></a>
<h4 class="subsection">4.30.1 Example<span class="pull-right"><a class="anchor hidden-xs" href="#Example-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Example-1" aria-hidden="true">TOC</a></span></h4>
<p>Use <code class="command">ffmpeg</code> to mux input audio to a &lsquo;<samp class="samp">5.1</samp>&rsquo; channel layout
resampled at 96000Hz:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -af aresample=96000,pan=5.1 slow.302
</pre></div>

<p>For ffmpeg versions before 7.0 you might have to use the &lsquo;<samp class="samp">asetnsamples</samp>&rsquo;
filter to limit the muxed packet size, because this format does not support
muxing packets larger than 65535 bytes (3640 samples). For newer ffmpeg
versions audio is automatically packetized to 36000 byte (2000 sample) packets.
</p>
<a name="dv"></a>
<h3 class="section">4.31 dv<span class="pull-right"><a class="anchor hidden-xs" href="#dv" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dv" aria-hidden="true">TOC</a></span></h3>
<p>DV (Digital Video) muxer.
</p>
<p>It accepts exactly one &lsquo;<samp class="samp">dvvideo</samp>&rsquo; video stream and at most two
&lsquo;<samp class="samp">pcm_s16</samp>&rsquo; audio streams. More constraints are defined by the
property of the video, which must correspond to a DV video supported
profile, and on the framerate.
</p>
<a name="Example-2"></a>
<h4 class="subsection">4.31.1 Example<span class="pull-right"><a class="anchor hidden-xs" href="#Example-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Example-2" aria-hidden="true">TOC</a></span></h4>
<p>Use <code class="command">ffmpeg</code> to convert the input:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -s:v 720x480 -pix_fmt yuv411p -r 29.97 -ac 2 -ar 48000 -y out.dv
</pre></div>

<a name="ffmetadata"></a>
<h3 class="section">4.32 ffmetadata<span class="pull-right"><a class="anchor hidden-xs" href="#ffmetadata" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ffmetadata" aria-hidden="true">TOC</a></span></h3>
<p>FFmpeg metadata muxer.
</p>
<p>This muxer writes the streams metadata in the &lsquo;<samp class="samp">ffmetadata</samp>&rsquo;
format.
</p>
<p>See <a data-manual="ffmpeg-formats" href="./ffmpeg-formats.html#metadata">the Metadata chapter</a> for
information about the format.
</p>
<a name="Example-3"></a>
<h4 class="subsection">4.32.1 Example<span class="pull-right"><a class="anchor hidden-xs" href="#Example-3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Example-3" aria-hidden="true">TOC</a></span></h4>

<p>Use <code class="command">ffmpeg</code> to extract metadata from an input file to a <samp class="file">metadata.ffmeta</samp>
file in &lsquo;<samp class="samp">ffmetadata</samp>&rsquo; format:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f ffmetadata metadata.ffmeta
</pre></div>

<a class="anchor" id="fifo"></a><a name="fifo-1"></a>
<h3 class="section">4.33 fifo<span class="pull-right"><a class="anchor hidden-xs" href="#fifo" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-fifo" aria-hidden="true">TOC</a></span></h3>
<p>FIFO (First-In First-Out) muxer.
</p>
<p>The &lsquo;<samp class="samp">fifo</samp>&rsquo; pseudo-muxer allows the separation of encoding and
muxing by using a first-in-first-out queue and running the actual muxer
in a separate thread.
</p>
<p>This is especially useful in combination with
the <a class="ref" href="#tee">tee</a> muxer and can be used to send data to several
destinations with different reliability/writing speed/latency.
</p>
<p>The target muxer is either selected from the output name or specified
through the <samp class="option">fifo_format</samp> option.
</p>
<p>The behavior of the &lsquo;<samp class="samp">fifo</samp>&rsquo; muxer if the queue fills up or if the
output fails (e.g. if a packet cannot be written to the output) is
selectable:
</p><ul class="itemize mark-bullet">
<li>Output can be transparently restarted with configurable delay between
retries based on real time or time of the processed stream.

</li><li>Encoding can be blocked during temporary failure, or continue transparently
dropping packets in case the FIFO queue fills up.
</li></ul>

<p>API users should be aware that callback functions
(<code class="code">interrupt_callback</code>, <code class="code">io_open</code> and <code class="code">io_close</code>) used
within its <code class="code">AVFormatContext</code> must be thread-safe.
</p>
<a name="Options-21"></a>
<h4 class="subsection">4.33.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-21" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-21" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">attempt_recovery <var class="var">bool</var></samp></dt>
<dd><p>If failure occurs, attempt to recover the output. This is especially
useful when used with network output, since it makes it possible to
restart streaming transparently. By default this option is set to
<code class="code">false</code>.
</p>
</dd>
<dt><samp class="option">drop_pkts_on_overflow <var class="var">bool</var></samp></dt>
<dd><p>If set to <code class="code">true</code>, in case the fifo queue fills up, packets will
be dropped rather than blocking the encoder. This makes it possible to
continue streaming without delaying the input, at the cost of omitting
part of the stream. By default this option is set to <code class="code">false</code>, so in
such cases the encoder will be blocked until the muxer processes some
of the packets and none of them is lost.
</p>
</dd>
<dt><samp class="option">fifo_format <var class="var">format_name</var></samp></dt>
<dd><p>Specify the format name. Useful if it cannot be guessed from the
output name suffix.
</p>
</dd>
<dt><samp class="option">format_opts <var class="var">options</var></samp></dt>
<dd><p>Specify format options for the underlying muxer. Muxer options can be
specified as a list of <var class="var">key</var>=<var class="var">value</var> pairs separated by &rsquo;:&rsquo;.
</p>
</dd>
<dt><samp class="option">max_recovery_attempts <var class="var">count</var></samp></dt>
<dd><p>Set maximum number of successive unsuccessful recovery attempts after
which the output fails permanently. By default this option is set to
<code class="code">0</code> (unlimited).
</p>
</dd>
<dt><samp class="option">queue_size <var class="var">size</var></samp></dt>
<dd><p>Specify size of the queue as a number of packets. Default value is
<code class="code">60</code>.
</p>
</dd>
<dt><samp class="option">recover_any_error <var class="var">bool</var></samp></dt>
<dd><p>If set to <code class="code">true</code>, recovery will be attempted regardless of type
of the error causing the failure. By default this option is set to
<code class="code">false</code> and in case of certain (usually permanent) errors the
recovery is not attempted even when the <samp class="option">attempt_recovery</samp>
option is set to <code class="code">true</code>.
</p>
</dd>
<dt><samp class="option">recovery_wait_streamtime <var class="var">bool</var></samp></dt>
<dd><p>If set to <code class="code">false</code>, the real time is used when waiting for the
recovery attempt (i.e. the recovery will be attempted after the time
specified by the <samp class="option">recovery_wait_time</samp> option).
</p>
<p>If set to <code class="code">true</code>, the time of the processed stream is taken into
account instead (i.e. the recovery will be attempted after discarding
the packets corresponding to the <samp class="option">recovery_wait_time</samp> option).
</p>
<p>By default this option is set to <code class="code">false</code>.
</p>
</dd>
<dt><samp class="option">recovery_wait_time <var class="var">duration</var></samp></dt>
<dd><p>Specify waiting time in seconds before the next recovery attempt after
previous unsuccessful recovery attempt. Default value is <code class="code">5</code>.
</p>
</dd>
<dt><samp class="option">restart_with_keyframe <var class="var">bool</var></samp></dt>
<dd><p>Specify whether to wait for the keyframe after recovering from
queue overflow or failure. This option is set to <code class="code">false</code> by default.
</p>
</dd>
<dt><samp class="option">timeshift <var class="var">duration</var></samp></dt>
<dd><p>Buffer the specified amount of packets and delay writing the
output. Note that the value of the <samp class="option">queue_size</samp> option must be
big enough to store the packets for timeshift. At the end of the input
the fifo buffer is flushed at realtime speed.
</p></dd>
</dl>

<a name="Example-4"></a>
<h4 class="subsection">4.33.2 Example<span class="pull-right"><a class="anchor hidden-xs" href="#Example-4" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Example-4" aria-hidden="true">TOC</a></span></h4>

<p>Use <code class="command">ffmpeg</code> to stream to an RTMP server, continue processing
the stream at real-time rate even in case of temporary failure
(network outage) and attempt to recover streaming every second
indefinitely:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -re -i ... -c:v libx264 -c:a aac -f fifo -fifo_format flv \
  -drop_pkts_on_overflow 1 -attempt_recovery 1 -recovery_wait_time 1 \
  -map 0:v -map 0:a rtmp://example.com/live/stream_name
</pre></div>

<a name="film_005fcpk"></a>
<h3 class="section">4.34 film_cpk<span class="pull-right"><a class="anchor hidden-xs" href="#film_005fcpk" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-film_005fcpk" aria-hidden="true">TOC</a></span></h3>
<p>Sega film (.cpk) muxer.
</p>
<p>This format was used as internal format for several Sega games.
</p>
<p>For more information regarding the Sega film file format, visit
<a class="url" href="http://wiki.multimedia.cx/index.php?title=Sega_FILM">http://wiki.multimedia.cx/index.php?title=Sega_FILM</a>.
</p>
<p>It accepts at maximum one &lsquo;<samp class="samp">cinepak</samp>&rsquo; or raw video stream, and at
maximum one audio stream.
</p>
<a name="filmstrip"></a>
<h3 class="section">4.35 filmstrip<span class="pull-right"><a class="anchor hidden-xs" href="#filmstrip" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-filmstrip" aria-hidden="true">TOC</a></span></h3>
<p>Adobe Filmstrip muxer.
</p>
<p>This format is used by several Adobe tools to store a generated filmstrip export. It
accepts a single raw video stream.
</p>
<a name="fits"></a>
<h3 class="section">4.36 fits<span class="pull-right"><a class="anchor hidden-xs" href="#fits" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-fits" aria-hidden="true">TOC</a></span></h3>
<p>Flexible Image Transport System (FITS) muxer.
</p>
<p>This image format is used to store astronomical data.
</p>
<p>For more information regarding the format, visit
<a class="url" href="https://fits.gsfc.nasa.gov">https://fits.gsfc.nasa.gov</a>.
</p>
<a name="flac"></a>
<h3 class="section">4.37 flac<span class="pull-right"><a class="anchor hidden-xs" href="#flac" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-flac" aria-hidden="true">TOC</a></span></h3>
<p>Raw FLAC audio muxer.
</p>
<p>This muxer accepts exactly one FLAC audio stream. Additionally, it is possible to add
images with disposition &lsquo;<samp class="samp">attached_pic</samp>&rsquo;.
</p>
<a name="Options-22"></a>
<h4 class="subsection">4.37.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-22" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-22" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">write_header <var class="var">bool</var></samp></dt>
<dd><p>write the file header if set to <code class="code">true</code>, default is <code class="code">true</code>
</p></dd>
</dl>

<a name="Example-5"></a>
<h4 class="subsection">4.37.2 Example<span class="pull-right"><a class="anchor hidden-xs" href="#Example-5" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Example-5" aria-hidden="true">TOC</a></span></h4>
<p>Use <code class="command">ffmpeg</code> to store the audio stream from an input file,
together with several pictures used with &lsquo;<samp class="samp">attached_pic</samp>&rsquo;
disposition:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -i pic1.png -i pic2.jpg -map 0:a -map 1 -map 2 -disposition:v attached_pic OUTPUT
</pre></div>

<a name="flv"></a>
<h3 class="section">4.38 flv<span class="pull-right"><a class="anchor hidden-xs" href="#flv" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-flv" aria-hidden="true">TOC</a></span></h3>
<p>Adobe Flash Video Format muxer.
</p>
<a name="Options-23"></a>
<h4 class="subsection">4.38.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-23" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-23" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">flvflags <var class="var">flags</var></samp></dt>
<dd><p>Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">aac_seq_header_detect</samp>&rsquo;</dt>
<dd><p>Place AAC sequence header based on audio stream data.
</p>
</dd>
<dt>&lsquo;<samp class="samp">no_sequence_end</samp>&rsquo;</dt>
<dd><p>Disable sequence end tag.
</p>
</dd>
<dt>&lsquo;<samp class="samp">no_metadata</samp>&rsquo;</dt>
<dd><p>Disable metadata tag.
</p>
</dd>
<dt>&lsquo;<samp class="samp">no_duration_filesize</samp>&rsquo;</dt>
<dd><p>Disable duration and filesize in metadata when they are equal to zero
at the end of stream. (Be used to non-seekable living stream).
</p>
</dd>
<dt>&lsquo;<samp class="samp">add_keyframe_index</samp>&rsquo;</dt>
<dd><p>Used to facilitate seeking; particularly for HTTP pseudo streaming.
</p></dd>
</dl>
</dd>
</dl>

<a class="anchor" id="framecrc"></a><a name="framecrc-1"></a>
<h3 class="section">4.39 framecrc<span class="pull-right"><a class="anchor hidden-xs" href="#framecrc" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-framecrc" aria-hidden="true">TOC</a></span></h3>

<p>Per-packet CRC (Cyclic Redundancy Check) testing format.
</p>
<p>This muxer computes and prints the Adler-32 CRC for each audio
and video packet. By default audio frames are converted to signed
16-bit raw audio and video frames to raw video before computing the
CRC.
</p>
<p>The output of the muxer consists of a line for each audio and video
packet of the form:
</p><div class="example">
<pre class="example-preformatted"><var class="var">stream_index</var>, <var class="var">packet_dts</var>, <var class="var">packet_pts</var>, <var class="var">packet_duration</var>, <var class="var">packet_size</var>, 0x<var class="var">CRC</var>
</pre></div>

<p><var class="var">CRC</var> is a hexadecimal number 0-padded to 8 digits containing the
CRC of the packet.
</p>
<a name="Examples-8"></a>
<h4 class="subsection">4.39.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-8" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-8" aria-hidden="true">TOC</a></span></h4>

<p>For example to compute the CRC of the audio and video frames in
<samp class="file">INPUT</samp>, converted to raw audio and video packets, and store it
in the file <samp class="file">out.crc</samp>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f framecrc out.crc
</pre></div>

<p>To print the information to stdout, use the command:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f framecrc -
</pre></div>

<p>With <code class="command">ffmpeg</code>, you can select the output format to which the
audio and video frames are encoded before computing the CRC for each
packet by specifying the audio and video codec. For example, to
compute the CRC of each decoded input audio frame converted to PCM
unsigned 8-bit and of each decoded input video frame converted to
MPEG-2 video, use the command:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -c:a pcm_u8 -c:v mpeg2video -f framecrc -
</pre></div>

<p>See also the <a class="ref" href="#crc">crc</a> muxer.
</p>
<a class="anchor" id="framehash"></a><a name="framehash-1"></a>
<h3 class="section">4.40 framehash<span class="pull-right"><a class="anchor hidden-xs" href="#framehash" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-framehash" aria-hidden="true">TOC</a></span></h3>

<p>Per-packet hash testing format.
</p>
<p>This muxer computes and prints a cryptographic hash for each audio
and video packet. This can be used for packet-by-packet equality
checks without having to individually do a binary comparison on each.
</p>
<p>By default audio frames are converted to signed 16-bit raw audio and
video frames to raw video before computing the hash, but the output
of explicit conversions to other codecs can also be used. It uses the
SHA-256 cryptographic hash function by default, but supports several
other algorithms.
</p>
<p>The output of the muxer consists of a line for each audio and video
packet of the form:
</p><div class="example">
<pre class="example-preformatted"><var class="var">stream_index</var>, <var class="var">packet_dts</var>, <var class="var">packet_pts</var>, <var class="var">packet_duration</var>, <var class="var">packet_size</var>, <var class="var">hash</var>
</pre></div>

<p><var class="var">hash</var> is a hexadecimal number representing the computed hash
for the packet.
</p>
<dl class="table">
<dt><samp class="option">hash <var class="var">algorithm</var></samp></dt>
<dd><p>Use the cryptographic hash function specified by the string <var class="var">algorithm</var>.
Supported values include <code class="code">MD5</code>, <code class="code">murmur3</code>, <code class="code">RIPEMD128</code>,
<code class="code">RIPEMD160</code>, <code class="code">RIPEMD256</code>, <code class="code">RIPEMD320</code>, <code class="code">SHA160</code>,
<code class="code">SHA224</code>, <code class="code">SHA256</code> (default), <code class="code">SHA512/224</code>, <code class="code">SHA512/256</code>,
<code class="code">SHA384</code>, <code class="code">SHA512</code>, <code class="code">CRC32</code> and <code class="code">adler32</code>.
</p>
</dd>
</dl>

<a name="Examples-9"></a>
<h4 class="subsection">4.40.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-9" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-9" aria-hidden="true">TOC</a></span></h4>

<p>To compute the SHA-256 hash of the audio and video frames in <samp class="file">INPUT</samp>,
converted to raw audio and video packets, and store it in the file
<samp class="file">out.sha256</samp>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f framehash out.sha256
</pre></div>

<p>To print the information to stdout, using the MD5 hash function, use
the command:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f framehash -hash md5 -
</pre></div>

<p>See also the <a class="ref" href="#hash">hash</a> muxer.
</p>
<a class="anchor" id="framemd5"></a><a name="framemd5-1"></a>
<h3 class="section">4.41 framemd5<span class="pull-right"><a class="anchor hidden-xs" href="#framemd5" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-framemd5" aria-hidden="true">TOC</a></span></h3>

<p>Per-packet MD5 testing format.
</p>
<p>This is a variant of the <a class="ref" href="#framehash">framehash</a> muxer. Unlike that muxer,
it defaults to using the MD5 hash function.
</p>
<a name="Examples-10"></a>
<h4 class="subsection">4.41.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-10" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-10" aria-hidden="true">TOC</a></span></h4>

<p>To compute the MD5 hash of the audio and video frames in <samp class="file">INPUT</samp>,
converted to raw audio and video packets, and store it in the file
<samp class="file">out.md5</samp>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f framemd5 out.md5
</pre></div>

<p>To print the information to stdout, use the command:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f framemd5 -
</pre></div>

<p>See also the <a class="ref" href="#framehash">framehash</a> and <a class="ref" href="#md5">md5</a> muxers.
</p>
<a class="anchor" id="gif"></a><a name="gif-2"></a>
<h3 class="section">4.42 gif<span class="pull-right"><a class="anchor hidden-xs" href="#gif-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-gif-1" aria-hidden="true">TOC</a></span></h3>
<p>Animated GIF muxer.
</p>
<p>Note that the GIF format has a very large time base: the delay between two frames can
therefore not be smaller than one centi second.
</p>
<a name="Options-24"></a>
<h4 class="subsection">4.42.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-24" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-24" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">loop <var class="var">bool</var></samp></dt>
<dd><p>Set the number of times to loop the output. Use <code class="code">-1</code> for no loop, <code class="code">0</code>
for looping indefinitely (default).
</p>
</dd>
<dt><samp class="option">final_delay <var class="var">delay</var></samp></dt>
<dd><p>Force the delay (expressed in centiseconds) after the last frame. Each frame
ends with a delay until the next frame. The default is <code class="code">-1</code>, which is a
special value to tell the muxer to re-use the previous delay. In case of a
loop, you might want to customize this value to mark a pause for instance.
</p></dd>
</dl>

<a name="Example-6"></a>
<h4 class="subsection">4.42.2 Example<span class="pull-right"><a class="anchor hidden-xs" href="#Example-6" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Example-6" aria-hidden="true">TOC</a></span></h4>
<p>Encode a gif looping 10 times, with a 5 seconds delay between
the loops:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -loop 10 -final_delay 500 out.gif
</pre></div>

<p>Note 1: if you wish to extract the frames into separate GIF files, you need to
force the <a class="ref" href="#image2">image2</a> muxer:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -c:v gif -f image2 &quot;out%d.gif&quot;
</pre></div>

<a name="gxf"></a>
<h3 class="section">4.43 gxf<span class="pull-right"><a class="anchor hidden-xs" href="#gxf" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-gxf" aria-hidden="true">TOC</a></span></h3>
<p>General eXchange Format (GXF) muxer.
</p>
<p>GXF was developed by Grass Valley Group, then standardized by SMPTE as SMPTE
360M and was extended in SMPTE RDD 14-2007 to include high-definition video
resolutions.
</p>
<p>It accepts at most one video stream with codec &lsquo;<samp class="samp">mjpeg</samp>&rsquo;, or
&lsquo;<samp class="samp">mpeg1video</samp>&rsquo;, or &lsquo;<samp class="samp">mpeg2video</samp>&rsquo;, or &lsquo;<samp class="samp">dvvideo</samp>&rsquo; with resolution
&lsquo;<samp class="samp">512x480</samp>&rsquo; or &lsquo;<samp class="samp">608x576</samp>&rsquo;, and several audio streams with rate 48000Hz
and codec &lsquo;<samp class="samp">pcm16_le</samp>&rsquo;.
</p>
<a class="anchor" id="hash"></a><a name="hash-1"></a>
<h3 class="section">4.44 hash<span class="pull-right"><a class="anchor hidden-xs" href="#hash" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-hash" aria-hidden="true">TOC</a></span></h3>

<p>Hash testing format.
</p>
<p>This muxer computes and prints a cryptographic hash of all the input
audio and video frames. This can be used for equality checks without
having to do a complete binary comparison.
</p>
<p>By default audio frames are converted to signed 16-bit raw audio and
video frames to raw video before computing the hash, but the output
of explicit conversions to other codecs can also be used. Timestamps
are ignored. It uses the SHA-256 cryptographic hash function by default,
but supports several other algorithms.
</p>
<p>The output of the muxer consists of a single line of the form:
<var class="var">algo</var>=<var class="var">hash</var>, where <var class="var">algo</var> is a short string representing
the hash function used, and <var class="var">hash</var> is a hexadecimal number
representing the computed hash.
</p>
<dl class="table">
<dt><samp class="option">hash <var class="var">algorithm</var></samp></dt>
<dd><p>Use the cryptographic hash function specified by the string <var class="var">algorithm</var>.
Supported values include <code class="code">MD5</code>, <code class="code">murmur3</code>, <code class="code">RIPEMD128</code>,
<code class="code">RIPEMD160</code>, <code class="code">RIPEMD256</code>, <code class="code">RIPEMD320</code>, <code class="code">SHA160</code>,
<code class="code">SHA224</code>, <code class="code">SHA256</code> (default), <code class="code">SHA512/224</code>, <code class="code">SHA512/256</code>,
<code class="code">SHA384</code>, <code class="code">SHA512</code>, <code class="code">CRC32</code> and <code class="code">adler32</code>.
</p>
</dd>
</dl>

<a name="Examples-11"></a>
<h4 class="subsection">4.44.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-11" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-11" aria-hidden="true">TOC</a></span></h4>

<p>To compute the SHA-256 hash of the input converted to raw audio and
video, and store it in the file <samp class="file">out.sha256</samp>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f hash out.sha256
</pre></div>

<p>To print an MD5 hash to stdout use the command:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f hash -hash md5 -
</pre></div>

<p>See also the <a class="ref" href="#framehash">framehash</a> muxer.
</p>
<a class="anchor" id="hds"></a><a name="hds-1"></a>
<h3 class="section">4.45 hds<span class="pull-right"><a class="anchor hidden-xs" href="#hds" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-hds" aria-hidden="true">TOC</a></span></h3>
<p>HTTP Dynamic Streaming (HDS) muxer.
</p>
<p>HTTP dynamic streaming, or HDS, is an adaptive bitrate streaming method
developed by Adobe. HDS delivers MP4 video content over HTTP connections. HDS
can be used for on-demand streaming or live streaming.
</p>
<p>This muxer creates an .f4m (Adobe Flash Media Manifest File) manifest, an .abst
(Adobe Bootstrap File) for each stream, and segment files in a directory
specified as the output.
</p>
<p>These needs to be accessed by an HDS player throuhg HTTPS for it to be able to
perform playback on the generated stream.
</p>
<a name="Options-25"></a>
<h4 class="subsection">4.45.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-25" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-25" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">extra_window_size <var class="var">int</var></samp></dt>
<dd><p>number of fragments kept outside of the manifest before removing from disk
</p>
</dd>
<dt><samp class="option">min_frag_duration <var class="var">microseconds</var></samp></dt>
<dd><p>minimum fragment duration (in microseconds), default value is 1 second
(<code class="code">10000000</code>)
</p>
</dd>
<dt><samp class="option">remove_at_exit <var class="var">bool</var></samp></dt>
<dd><p>remove all fragments when finished when set to <code class="code">true</code>
</p>
</dd>
<dt><samp class="option">window_size <var class="var">int</var></samp></dt>
<dd><p>number of fragments kept in the manifest, if set to a value different from
<code class="code">0</code>. By default all segments are kept in the output directory.
</p></dd>
</dl>

<a name="Example-7"></a>
<h4 class="subsection">4.45.2 Example<span class="pull-right"><a class="anchor hidden-xs" href="#Example-7" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Example-7" aria-hidden="true">TOC</a></span></h4>
<p>Use <code class="command">ffmpeg</code> to generate HDS files to the <samp class="file">output.hds</samp> directory in
real-time rate:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -re -i INPUT -f hds -b:v 200k output.hds
</pre></div>

<a class="anchor" id="hls"></a><a name="hls-2"></a>
<h3 class="section">4.46 hls<span class="pull-right"><a class="anchor hidden-xs" href="#hls-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-hls-1" aria-hidden="true">TOC</a></span></h3>

<p>Apple HTTP Live Streaming muxer that segments MPEG-TS according to
the HTTP Live Streaming (HLS) specification.
</p>
<p>It creates a playlist file, and one or more segment files. The output filename
specifies the playlist filename.
</p>
<p>By default, the muxer creates a file for each segment produced. These files
have the same name as the playlist, followed by a sequential number and a
.ts extension.
</p>
<p>Make sure to require a closed GOP when encoding and to set the GOP
size to fit your segment time constraint.
</p>
<p>For example, to convert an input file with <code class="command">ffmpeg</code>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i in.mkv -c:v h264 -flags +cgop -g 30 -hls_time 1 out.m3u8
</pre></div>
<p>This example will produce the playlist, <samp class="file">out.m3u8</samp>, and segment files:
<samp class="file">out0.ts</samp>, <samp class="file">out1.ts</samp>, <samp class="file">out2.ts</samp>, etc.
</p>
<p>See also the <a class="ref" href="#segment">segment</a> muxer, which provides a more generic and
flexible implementation of a segmenter, and can be used to perform HLS
segmentation.
</p>
<a name="Options-26"></a>
<h4 class="subsection">4.46.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-26" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-26" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">hls_init_time <var class="var">duration</var></samp></dt>
<dd><p>Set the initial target segment length. Default value is <var class="var">0</var>.
</p>
<p><var class="var">duration</var> must be a time duration specification,
see <a data-manual="ffmpeg-utils" href="./ffmpeg-utils.html#time-duration-syntax">the Time duration section in the ffmpeg-utils(1) manual</a>.
</p>
<p>Segment will be cut on the next key frame after this time has passed on the
first m3u8 list. After the initial playlist is filled, <code class="command">ffmpeg</code> will cut
segments at duration equal to <samp class="option">hls_time</samp>.
</p>
</dd>
<dt><samp class="option">hls_time <var class="var">duration</var></samp></dt>
<dd><p>Set the target segment length. Default value is 2.
</p>
<p><var class="var">duration</var> must be a time duration specification,
see <a data-manual="ffmpeg-utils" href="./ffmpeg-utils.html#time-duration-syntax">the Time duration section in the ffmpeg-utils(1) manual</a>.
Segment will be cut on the next key frame after this time has passed.
</p>
</dd>
<dt><samp class="option">hls_list_size <var class="var">size</var></samp></dt>
<dd><p>Set the maximum number of playlist entries. If set to 0 the list file
will contain all the segments. Default value is 5.
</p>
</dd>
<dt><samp class="option">hls_delete_threshold <var class="var">size</var></samp></dt>
<dd><p>Set the number of unreferenced segments to keep on disk before <code class="code">hls_flags delete_segments</code>
deletes them. Increase this to allow continue clients to download segments which
were recently referenced in the playlist. Default value is 1, meaning segments older than
<samp class="option">hls_list_size+1</samp> will be deleted.
</p>
</dd>
<dt><samp class="option">hls_start_number_source <var class="var">source</var></samp></dt>
<dd><p>Start the playlist sequence number (<code class="code">#EXT-X-MEDIA-SEQUENCE</code>) according to the specified source.
Unless <samp class="option">hls_flags single_file</samp> is set, it also specifies source of starting sequence numbers of
segment and subtitle filenames. In any case, if <samp class="option">hls_flags append_list</samp>
is set and read playlist sequence number is greater than the specified start sequence number,
then that value will be used as start value.
</p>
<p>It accepts the following values:
</p>
<dl class="table">
<dt><samp class="option">generic (default)</samp></dt>
<dd><p>Set the start numbers according to the <samp class="option">start_number</samp> option value.
</p>
</dd>
<dt><samp class="option">epoch</samp></dt>
<dd><p>Set the start number as the seconds since epoch (1970-01-01 00:00:00).
</p>
</dd>
<dt><samp class="option">epoch_us</samp></dt>
<dd><p>Set the start number as the microseconds since epoch (1970-01-01 00:00:00).
</p>
</dd>
<dt><samp class="option">datetime</samp></dt>
<dd><p>Set the start number based on the current date/time as YYYYmmddHHMMSS. e.g. 20161231235759.
</p></dd>
</dl>

</dd>
<dt><samp class="option">start_number <var class="var">number</var></samp></dt>
<dd><p>Start the playlist sequence number (<code class="code">#EXT-X-MEDIA-SEQUENCE</code>) from the specified <var class="var">number</var>
when <samp class="option">hls_start_number_source</samp> value is <var class="var">generic</var>. (This is the default case.)
Unless <samp class="option">hls_flags single_file</samp> is set, it also specifies starting sequence numbers of segment and subtitle filenames.
Default value is 0.
</p>
</dd>
<dt><samp class="option">hls_allow_cache <var class="var">bool</var></samp></dt>
<dd><p>Explicitly set whether the client MAY (1) or MUST NOT (0) cache media segments.
</p>
</dd>
<dt><samp class="option">hls_base_url <var class="var">baseurl</var></samp></dt>
<dd><p>Append <var class="var">baseurl</var> to every entry in the playlist.
Useful to generate playlists with absolute paths.
</p>
<p>Note that the playlist sequence number must be unique for each segment
and it is not to be confused with the segment filename sequence number
which can be cyclic, for example if the <samp class="option">wrap</samp> option is
specified.
</p>
</dd>
<dt><samp class="option">hls_segment_filename <var class="var">filename</var></samp></dt>
<dd><p>Set the segment filename. Unless the <samp class="option">hls_flags</samp> option is set with
&lsquo;<samp class="samp">single_file</samp>&rsquo;, <var class="var">filename</var> is used as a string format with the
segment number appended.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i in.nut -hls_segment_filename 'file%03d.ts' out.m3u8
</pre></div>

<p>will produce the playlist, <samp class="file">out.m3u8</samp>, and segment files:
<samp class="file">file000.ts</samp>, <samp class="file">file001.ts</samp>, <samp class="file">file002.ts</samp>, etc.
</p>
<p><var class="var">filename</var> may contain a full path or relative path specification,
but only the file name part without any path will be contained in the m3u8 segment list.
Should a relative path be specified, the path of the created segment
files will be relative to the current working directory.
When <samp class="option">strftime_mkdir</samp> is set, the whole expanded value of <var class="var">filename</var> will be written into the m3u8 segment list.
</p>
<p>When <samp class="option">var_stream_map</samp> is set with two or more variant streams, the
<var class="var">filename</var> pattern must contain the string &quot;%v&quot;, and this string will be
expanded to the position of variant stream index in the generated segment file
names.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i in.ts -b:v:0 1000k -b:v:1 256k -b:a:0 64k -b:a:1 32k \
  -map 0:v -map 0:a -map 0:v -map 0:a -f hls -var_stream_map &quot;v:0,a:0 v:1,a:1&quot; \
  -hls_segment_filename 'file_%v_%03d.ts' out_%v.m3u8
</pre></div>

<p>will produce the playlists segment file sets:
<samp class="file">file_0_000.ts</samp>, <samp class="file">file_0_001.ts</samp>, <samp class="file">file_0_002.ts</samp>, etc. and
<samp class="file">file_1_000.ts</samp>, <samp class="file">file_1_001.ts</samp>, <samp class="file">file_1_002.ts</samp>, etc.
</p>
<p>The string &quot;%v&quot; may be present in the filename or in the last directory name
containing the file, but only in one of them. (Additionally, %v may appear multiple times in the last
sub-directory or filename.) If the string %v is present in the directory name, then
sub-directories are created after expanding the directory name pattern. This
enables creation of segments corresponding to different variant streams in
subdirectories.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i in.ts -b:v:0 1000k -b:v:1 256k -b:a:0 64k -b:a:1 32k \
  -map 0:v -map 0:a -map 0:v -map 0:a -f hls -var_stream_map &quot;v:0,a:0 v:1,a:1&quot; \
  -hls_segment_filename 'vs%v/file_%03d.ts' vs%v/out.m3u8
</pre></div>
<p>will produce the playlists segment file sets:
<samp class="file">vs0/file_000.ts</samp>, <samp class="file">vs0/file_001.ts</samp>, <samp class="file">vs0/file_002.ts</samp>, etc. and
<samp class="file">vs1/file_000.ts</samp>, <samp class="file">vs1/file_001.ts</samp>, <samp class="file">vs1/file_002.ts</samp>, etc.
</p>
</dd>
<dt><samp class="option">strftime <var class="var">bool</var></samp></dt>
<dd><p>Use <code class="code">strftime()</code> on <var class="var">filename</var> to expand the segment filename with
localtime. The segment number is also available in this mode, but to use it,
you need to set &lsquo;<samp class="samp">second_level_segment_index</samp>&rsquo; in the <samp class="option">hls_flag</samp> and
%%d will be the specifier.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i in.nut -strftime 1 -hls_segment_filename 'file-%Y%m%d-%s.ts' out.m3u8
</pre></div>
<p>will produce the playlist, <samp class="file">out.m3u8</samp>, and segment files:
<samp class="file">file-20160215-1455569023.ts</samp>, <samp class="file">file-20160215-1455569024.ts</samp>, etc.
Note: On some systems/environments, the <code class="code">%s</code> specifier is not
available. See <code class="code">strftime()</code> documentation.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i in.nut -strftime 1 -hls_flags second_level_segment_index -hls_segment_filename 'file-%Y%m%d-%%04d.ts' out.m3u8
</pre></div>
<p>will produce the playlist, <samp class="file">out.m3u8</samp>, and segment files:
<samp class="file">file-20160215-0001.ts</samp>, <samp class="file">file-20160215-0002.ts</samp>, etc.
</p>
</dd>
<dt><samp class="option">strftime_mkdir <var class="var">bool</var></samp></dt>
<dd><p>Used together with <samp class="option">strftime</samp>, it will create all subdirectories which
are present in the expanded values of option <samp class="option">hls_segment_filename</samp>.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i in.nut -strftime 1 -strftime_mkdir 1 -hls_segment_filename '%Y%m%d/file-%Y%m%d-%s.ts' out.m3u8
</pre></div>
<p>will create a directory <samp class="file">201560215</samp> (if it does not exist), and then
produce the playlist, <samp class="file">out.m3u8</samp>, and segment files:
<samp class="file">20160215/file-20160215-1455569023.ts</samp>,
<samp class="file">20160215/file-20160215-1455569024.ts</samp>, etc.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i in.nut -strftime 1 -strftime_mkdir 1 -hls_segment_filename '%Y/%m/%d/file-%Y%m%d-%s.ts' out.m3u8
</pre></div>
<p>will create a directory hierarchy <samp class="file">2016/02/15</samp> (if any of them do not
exist), and then produce the playlist, <samp class="file">out.m3u8</samp>, and segment files:
<samp class="file">2016/02/15/file-20160215-1455569023.ts</samp>,
<samp class="file">2016/02/15/file-20160215-1455569024.ts</samp>, etc.
</p>
</dd>
<dt><samp class="option">hls_segment_options <var class="var">options_list</var></samp></dt>
<dd><p>Set output format options using a :-separated list of key=value
parameters. Values containing <code class="code">:</code> special characters must be
escaped.
</p>
</dd>
<dt><samp class="option">hls_key_info_file <var class="var">key_info_file</var></samp></dt>
<dd><p>Use the information in <var class="var">key_info_file</var> for segment encryption. The first
line of <var class="var">key_info_file</var> specifies the key URI written to the playlist. The
key URL is used to access the encryption key during playback. The second line
specifies the path to the key file used to obtain the key during the encryption
process. The key file is read as a single packed array of 16 octets in binary
format. The optional third line specifies the initialization vector (IV) as a
hexadecimal string to be used instead of the segment sequence number (default)
for encryption. Changes to <var class="var">key_info_file</var> will result in segment
encryption with the new key/IV and an entry in the playlist for the new key
URI/IV if <samp class="option">hls_flags periodic_rekey</samp> is enabled.
</p>
<p>Key info file format:
</p><div class="example">
<pre class="example-preformatted"><var class="var">key URI</var>
<var class="var">key file path</var>
<var class="var">IV</var> (optional)
</pre></div>

<p>Example key URIs:
</p><div class="example">
<pre class="example-preformatted">http://server/file.key
/path/to/file.key
file.key
</pre></div>

<p>Example key file paths:
</p><div class="example">
<pre class="example-preformatted">file.key
/path/to/file.key
</pre></div>

<p>Example IV:
</p><div class="example">
<pre class="example-preformatted">0123456789ABCDEF0123456789ABCDEF
</pre></div>

<p>Key info file example:
</p><div class="example">
<pre class="example-preformatted">http://server/file.key
/path/to/file.key
0123456789ABCDEF0123456789ABCDEF
</pre></div>

<p>Example shell script:
</p><div class="example">
<pre class="example-preformatted">#!/bin/sh
BASE_URL=${1:-'.'}
openssl rand 16 &gt; file.key
echo $BASE_URL/file.key &gt; file.keyinfo
echo file.key &gt;&gt; file.keyinfo
echo $(openssl rand -hex 16) &gt;&gt; file.keyinfo
ffmpeg -f lavfi -re -i testsrc -c:v h264 -hls_flags delete_segments \
  -hls_key_info_file file.keyinfo out.m3u8
</pre></div>

</dd>
<dt><samp class="option">hls_enc <var class="var">bool</var></samp></dt>
<dd><p>Enable (1) or disable (0) the AES128 encryption.
When enabled every segment generated is encrypted and the encryption key
is saved as <var class="var">playlist name</var>.key.
</p>
</dd>
<dt><samp class="option">hls_enc_key <var class="var">key</var></samp></dt>
<dd><p>Specify a 16-octet key to encrypt the segments, by default it is randomly
generated.
</p>
</dd>
<dt><samp class="option">hls_enc_key_url <var class="var">keyurl</var></samp></dt>
<dd><p>If set, <var class="var">keyurl</var> is prepended instead of <var class="var">baseurl</var> to the key filename
in the playlist.
</p>
</dd>
<dt><samp class="option">hls_enc_iv <var class="var">iv</var></samp></dt>
<dd><p>Specify the 16-octet initialization vector for every segment instead of the
autogenerated ones.
</p>
</dd>
<dt><samp class="option">hls_segment_type <var class="var">flags</var></samp></dt>
<dd><p>Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">mpegts</samp>&rsquo;</dt>
<dd><p>Output segment files in MPEG-2 Transport Stream format. This is
compatible with all HLS versions.
</p>
</dd>
<dt>&lsquo;<samp class="samp">fmp4</samp>&rsquo;</dt>
<dd><p>Output segment files in fragmented MP4 format, similar to MPEG-DASH.
fmp4 files may be used in HLS version 7 and above.
</p></dd>
</dl>

</dd>
<dt><samp class="option">hls_fmp4_init_filename <var class="var">filename</var></samp></dt>
<dd><p>Set filename for the fragment files header file, default filename is <samp class="file">init.mp4</samp>.
</p>
<p>When <samp class="option">strftime</samp> is enabled, <var class="var">filename</var> is expanded to the segment filename with localtime.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i in.nut -hls_segment_type fmp4 -strftime 1 -hls_fmp4_init_filename &quot;%s_init.mp4&quot; out.m3u8
</pre></div>
<p>will produce init like this <samp class="file">1602678741_init.mp4</samp>.
</p>
</dd>
<dt><samp class="option">hls_fmp4_init_resend <var class="var">bool</var></samp></dt>
<dd><p>Resend init file after m3u8 file refresh every time, default is <var class="var">0</var>.
</p>
<p>When <samp class="option">var_stream_map</samp> is set with two or more variant streams, the
<var class="var">filename</var> pattern must contain the string &quot;%v&quot;, this string specifies
the position of variant stream index in the generated init file names.
The string &quot;%v&quot; may be present in the filename or in the last directory name
containing the file. If the string is present in the directory name, then
sub-directories are created after expanding the directory name pattern. This
enables creation of init files corresponding to different variant streams in
subdirectories.
</p>
</dd>
<dt><samp class="option">hls_flags <var class="var">flags</var></samp></dt>
<dd><p>Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">single_file</samp>&rsquo;</dt>
<dd><p>If this flag is set, the muxer will store all segments in a single MPEG-TS
file, and will use byte ranges in the playlist. HLS playlists generated with
this way will have the version number 4.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i in.nut -hls_flags single_file out.m3u8
</pre></div>
<p>will produce the playlist, <samp class="file">out.m3u8</samp>, and a single segment file,
<samp class="file">out.ts</samp>.
</p>
</dd>
<dt>&lsquo;<samp class="samp">delete_segments</samp>&rsquo;</dt>
<dd><p>Segment files removed from the playlist are deleted after a period of time
equal to the duration of the segment plus the duration of the playlist.
</p>
</dd>
<dt>&lsquo;<samp class="samp">append_list</samp>&rsquo;</dt>
<dd><p>Append new segments into the end of old segment list,
and remove the <code class="code">#EXT-X-ENDLIST</code> from the old segment list.
</p>
</dd>
<dt>&lsquo;<samp class="samp">round_durations</samp>&rsquo;</dt>
<dd><p>Round the duration info in the playlist file segment info to integer
values, instead of using floating point.
If there are no other features requiring higher HLS versions be used,
then this will allow <code class="command">ffmpeg</code> to output a HLS version 2 m3u8.
</p>
</dd>
<dt>&lsquo;<samp class="samp">discont_start</samp>&rsquo;</dt>
<dd><p>Add the <code class="code">#EXT-X-DISCONTINUITY</code> tag to the playlist, before the
first segment&rsquo;s information.
</p>
</dd>
<dt>&lsquo;<samp class="samp">omit_endlist</samp>&rsquo;</dt>
<dd><p>Do not append the <code class="code">EXT-X-ENDLIST</code> tag at the end of the playlist.
</p>
</dd>
<dt>&lsquo;<samp class="samp">periodic_rekey</samp>&rsquo;</dt>
<dd><p>The file specified by <code class="code">hls_key_info_file</code> will be checked periodically and
detect updates to the encryption info. Be sure to replace this file atomically,
including the file containing the AES encryption key.
</p>
</dd>
<dt>&lsquo;<samp class="samp">independent_segments</samp>&rsquo;</dt>
<dd><p>Add the <code class="code">#EXT-X-INDEPENDENT-SEGMENTS</code> tag to playlists that has video segments
and when all the segments of that playlist are guaranteed to start with a key frame.
</p>
</dd>
<dt>&lsquo;<samp class="samp">iframes_only</samp>&rsquo;</dt>
<dd><p>Add the <code class="code">#EXT-X-I-FRAMES-ONLY</code> tag to playlists that has video segments
and can play only I-frames in the <code class="code">#EXT-X-BYTERANGE</code> mode.
</p>
</dd>
<dt>&lsquo;<samp class="samp">split_by_time</samp>&rsquo;</dt>
<dd><p>Allow segments to start on frames other than key frames. This improves
behavior on some players when the time between key frames is inconsistent,
but may make things worse on others, and can cause some oddities during
seeking. This flag should be used with the <samp class="option">hls_time</samp> option.
</p>
</dd>
<dt>&lsquo;<samp class="samp">program_date_time</samp>&rsquo;</dt>
<dd><p>Generate <code class="code">EXT-X-PROGRAM-DATE-TIME</code> tags.
</p>
</dd>
<dt>&lsquo;<samp class="samp">second_level_segment_index</samp>&rsquo;</dt>
<dd><p>Make it possible to use segment indexes as %%d in the
<samp class="option">hls_segment_filename</samp> option expression besides date/time values when
<samp class="option">strftime</samp> option is on. To get fixed width numbers with trailing zeroes, %%0xd format
is available where x is the required width.
</p>
</dd>
<dt>&lsquo;<samp class="samp">second_level_segment_size</samp>&rsquo;</dt>
<dd><p>Make it possible to use segment sizes (counted in bytes) as %%s in
<samp class="option">hls_segment_filename</samp> option expression besides date/time values when
strftime is on. To get fixed width numbers with trailing zeroes, %%0xs format
is available where x is the required width.
</p>
</dd>
<dt>&lsquo;<samp class="samp">second_level_segment_duration</samp>&rsquo;</dt>
<dd><p>Make it possible to use segment duration (calculated in microseconds) as %%t in
<samp class="option">hls_segment_filename</samp> option expression besides date/time values when
strftime is on. To get fixed width numbers with trailing zeroes, %%0xt format
is available where x is the required width.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i sample.mpeg \
   -f hls -hls_time 3 -hls_list_size 5 \
   -hls_flags second_level_segment_index+second_level_segment_size+second_level_segment_duration \
   -strftime 1 -strftime_mkdir 1 -hls_segment_filename &quot;segment_%Y%m%d%H%M%S_%%04d_%%08s_%%013t.ts&quot; stream.m3u8
</pre></div>
<p>will produce segments like this:
<samp class="file">segment_20170102194334_0003_00122200_0000003000000.ts</samp>, <samp class="file">segment_20170102194334_0004_00120072_0000003000000.ts</samp> etc.
</p>
</dd>
<dt>&lsquo;<samp class="samp">temp_file</samp>&rsquo;</dt>
<dd><p>Write segment data to <samp class="file">filename.tmp</samp> and rename to filename only once the
segment is complete.
</p>
<p>A webserver serving up segments can be configured to reject requests to *.tmp to
prevent access to in-progress segments before they have been added to the m3u8
playlist.
</p>
<p>This flag also affects how m3u8 playlist files are created. If this flag is set,
all playlist files will be written into a temporary file and renamed after they
are complete, similarly as segments are handled. But playlists with <code class="code">file</code>
protocol and with <samp class="option">hls_playlist_type</samp> type other than &lsquo;<samp class="samp">vod</samp>&rsquo; are
always written into a temporary file regardless of this flag.
</p>
<p>Master playlist files specified with <samp class="option">master_pl_name</samp>, if any, with
<code class="code">file</code> protocol, are always written into temporary file regardless of this
flag if <samp class="option">master_pl_publish_rate</samp> value is other than zero.
</p></dd>
</dl>

</dd>
<dt><samp class="option">hls_playlist_type <var class="var">type</var></samp></dt>
<dd><p>If type is &lsquo;<samp class="samp">event</samp>&rsquo;, emit <code class="code">#EXT-X-PLAYLIST-TYPE:EVENT</code> in the m3u8
header. This forces <samp class="option">hls_list_size</samp> to 0; the playlist can only be
appended to.
</p>
<p>If type is &lsquo;<samp class="samp">vod</samp>&rsquo;, emit <code class="code">#EXT-X-PLAYLIST-TYPE:VOD</code> in the m3u8
header. This forces <samp class="option">hls_list_size</samp> to 0; the playlist must not change.
</p>
</dd>
<dt><samp class="option">method <var class="var">method</var></samp></dt>
<dd><p>Use the given HTTP method to create the hls files.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -re -i in.ts -f hls -method PUT http://example.com/live/out.m3u8
</pre></div>
<p>will upload all the mpegts segment files to the HTTP server using the HTTP PUT
method, and update the m3u8 files every <code class="code">refresh</code> times using the same
method. Note that the HTTP server must support the given method for uploading
files.
</p>
</dd>
<dt><samp class="option">http_user_agent <var class="var">agent</var></samp></dt>
<dd><p>Override User-Agent field in HTTP header. Applicable only for HTTP output.
</p>
</dd>
<dt><samp class="option">var_stream_map <var class="var">stream_map</var></samp></dt>
<dd><p>Specify a map string defining how to group the audio, video and subtitle streams
into different variant streams. The variant stream groups are separated by
space.
</p>
<p>Expected string format is like this &quot;a:0,v:0 a:1,v:1 ....&quot;. Here a:, v:, s: are
the keys to specify audio, video and subtitle streams respectively.
Allowed values are 0 to 9 (limited just based on practical usage).
</p>
<p>When there are two or more variant streams, the output filename pattern must
contain the string &quot;%v&quot;: this string specifies the position of variant stream
index in the output media playlist filenames. The string &quot;%v&quot; may be present in
the filename or in the last directory name containing the file. If the string is
present in the directory name, then sub-directories are created after expanding
the directory name pattern. This enables creation of variant streams in
subdirectories.
</p>
<p>A few examples follow.
</p>
<ul class="itemize mark-bullet">
<li>Create two hls variant streams. The first variant stream will contain video
stream of bitrate 1000k and audio stream of bitrate 64k and the second variant
stream will contain video stream of bitrate 256k and audio stream of bitrate
32k. Here, two media playlist with file names <samp class="file">out_0.m3u8</samp> and
<samp class="file">out_1.m3u8</samp> will be created.
<div class="example">
<pre class="example-preformatted">ffmpeg -re -i in.ts -b:v:0 1000k -b:v:1 256k -b:a:0 64k -b:a:1 32k \
  -map 0:v -map 0:a -map 0:v -map 0:a -f hls -var_stream_map &quot;v:0,a:0 v:1,a:1&quot; \
  http://example.com/live/out_%v.m3u8
</pre></div>

</li><li>If you want something meaningful text instead of indexes in result names, you
may specify names for each or some of the variants. The following example will
create two hls variant streams as in the previous one. But here, the two media
playlist with file names <samp class="file">out_my_hd.m3u8</samp> and <samp class="file">out_my_sd.m3u8</samp> will be
created.
<div class="example">
<pre class="example-preformatted">ffmpeg -re -i in.ts -b:v:0 1000k -b:v:1 256k -b:a:0 64k -b:a:1 32k \
  -map 0:v -map 0:a -map 0:v -map 0:a -f hls -var_stream_map &quot;v:0,a:0,name:my_hd v:1,a:1,name:my_sd&quot; \
  http://example.com/live/out_%v.m3u8
</pre></div>

</li><li>Create three hls variant streams. The first variant stream will be a video only
stream with video bitrate 1000k, the second variant stream will be an audio only
stream with bitrate 64k and the third variant stream will be a video only stream
with bitrate 256k. Here, three media playlist with file names <samp class="file">out_0.m3u8</samp>,
<samp class="file">out_1.m3u8</samp> and <samp class="file">out_2.m3u8</samp> will be created.
<div class="example">
<pre class="example-preformatted">ffmpeg -re -i in.ts -b:v:0 1000k -b:v:1 256k -b:a:0 64k \
  -map 0:v -map 0:a -map 0:v -f hls -var_stream_map &quot;v:0 a:0 v:1&quot; \
  http://example.com/live/out_%v.m3u8
</pre></div>

</li><li>Create the variant streams in subdirectories. Here, the first media playlist is
created at <samp class="file">http://example.com/live/vs_0/out.m3u8</samp> and the second one at
<samp class="file">http://example.com/live/vs_1/out.m3u8</samp>.
<div class="example">
<pre class="example-preformatted">ffmpeg -re -i in.ts -b:v:0 1000k -b:v:1 256k -b:a:0 64k -b:a:1 32k \
  -map 0:v -map 0:a -map 0:v -map 0:a -f hls -var_stream_map &quot;v:0,a:0 v:1,a:1&quot; \
  http://example.com/live/vs_%v/out.m3u8
</pre></div>

</li><li>Create two audio only and two video only variant streams. In addition to the
<code class="code">#EXT-X-STREAM-INF</code> tag for each variant stream in the master playlist, the
<code class="code">#EXT-X-MEDIA</code> tag is also added for the two audio only variant streams and
they are mapped to the two video only variant streams with audio group names
&rsquo;aud_low&rsquo; and &rsquo;aud_high&rsquo;.
By default, a single hls variant containing all the encoded streams is created.
<div class="example">
<pre class="example-preformatted">ffmpeg -re -i in.ts -b:a:0 32k -b:a:1 64k -b:v:0 1000k -b:v:1 3000k  \
  -map 0:a -map 0:a -map 0:v -map 0:v -f hls \
  -var_stream_map &quot;a:0,agroup:aud_low a:1,agroup:aud_high v:0,agroup:aud_low v:1,agroup:aud_high&quot; \
  -master_pl_name master.m3u8 \
  http://example.com/live/out_%v.m3u8
</pre></div>

</li><li>
Create two audio only and one video only variant streams. In addition to the
<code class="code">#EXT-X-STREAM-INF</code> tag for each variant stream in the master playlist, the
<code class="code">#EXT-X-MEDIA</code> tag is also added for the two audio only variant streams and
they are mapped to the one video only variant streams with audio group name
&rsquo;aud_low&rsquo;, and the audio group have default stat is NO or YES.
By default, a single hls variant containing all the encoded streams is created.
<div class="example">
<pre class="example-preformatted">ffmpeg -re -i in.ts -b:a:0 32k -b:a:1 64k -b:v:0 1000k \
  -map 0:a -map 0:a -map 0:v -f hls \
  -var_stream_map &quot;a:0,agroup:aud_low,default:yes a:1,agroup:aud_low v:0,agroup:aud_low&quot; \
  -master_pl_name master.m3u8 \
  http://example.com/live/out_%v.m3u8
</pre></div>

</li><li>Create two audio only and one video only variant streams. In addition to the
<code class="code">#EXT-X-STREAM-INF</code> tag for each variant stream in the master playlist, the
<code class="code">#EXT-X-MEDIA</code> tag is also added for the two audio only variant streams and
they are mapped to the one video only variant streams with audio group name
&rsquo;aud_low&rsquo;, and the audio group have default stat is NO or YES, and one audio
have and language is named ENG, the other audio language is named CHN. By
default, a single hls variant containing all the encoded streams is created.
<div class="example">
<pre class="example-preformatted">ffmpeg -re -i in.ts -b:a:0 32k -b:a:1 64k -b:v:0 1000k \
  -map 0:a -map 0:a -map 0:v -f hls \
  -var_stream_map &quot;a:0,agroup:aud_low,default:yes,language:ENG a:1,agroup:aud_low,language:CHN v:0,agroup:aud_low&quot; \
  -master_pl_name master.m3u8 \
  http://example.com/live/out_%v.m3u8
</pre></div>

</li><li>Create a single variant stream. Add the <code class="code">#EXT-X-MEDIA</code> tag with
<code class="code">TYPE=SUBTITLES</code> in the master playlist with webvtt subtitle group name
&rsquo;subtitle&rsquo; and optional subtitle name, e.g. &rsquo;English&rsquo;. Make sure the input
file has one text subtitle stream at least.
<div class="example">
<pre class="example-preformatted">ffmpeg -y -i input_with_subtitle.mkv \
 -b:v:0 5250k -c:v h264 -pix_fmt yuv420p -profile:v main -level 4.1 \
 -b:a:0 256k \
 -c:s webvtt -c:a mp2 -ar 48000 -ac 2 -map 0:v -map 0:a:0 -map 0:s:0 \
 -f hls -var_stream_map &quot;v:0,a:0,s:0,sgroup:subtitle,sname:English&quot; \
 -master_pl_name master.m3u8 -t 300 -hls_time 10 -hls_init_time 4 -hls_list_size \
 10 -master_pl_publish_rate 10 -hls_flags \
 delete_segments+discont_start+split_by_time ./tmp/video.m3u8
</pre></div>
</li></ul>

</dd>
<dt><samp class="option">cc_stream_map <var class="var">cc_stream_map</var></samp></dt>
<dd><p>Map string which specifies different closed captions groups and their
attributes. The closed captions stream groups are separated by space.
</p>
<p>Expected string format is like this
&quot;ccgroup:&lt;group name&gt;,instreamid:&lt;INSTREAM-ID&gt;,language:&lt;language code&gt; ....&quot;.
&rsquo;ccgroup&rsquo; and &rsquo;instreamid&rsquo; are mandatory attributes. &rsquo;language&rsquo; is an optional
attribute.
</p>
<p>The closed captions groups configured using this option are mapped to different
variant streams by providing the same &rsquo;ccgroup&rsquo; name in the
<samp class="option">var_stream_map</samp> string.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -re -i in.ts -b:v:0 1000k -b:v:1 256k -b:a:0 64k -b:a:1 32k \
  -a53cc:0 1 -a53cc:1 1 \
  -map 0:v -map 0:a -map 0:v -map 0:a -f hls \
  -cc_stream_map &quot;ccgroup:cc,instreamid:CC1,language:en ccgroup:cc,instreamid:CC2,language:sp&quot; \
  -var_stream_map &quot;v:0,a:0,ccgroup:cc v:1,a:1,ccgroup:cc&quot; \
  -master_pl_name master.m3u8 \
  http://example.com/live/out_%v.m3u8
</pre></div>
<p>will add two <code class="code">#EXT-X-MEDIA</code> tags with <code class="code">TYPE=CLOSED-CAPTIONS</code> in the
master playlist for the INSTREAM-IDs &rsquo;CC1&rsquo; and &rsquo;CC2&rsquo;. Also, it will add
<code class="code">CLOSED-CAPTIONS</code> attribute with group name &rsquo;cc&rsquo; for the two output variant
streams.
</p>
<p>If <samp class="option">var_stream_map</samp> is not set, then the first available ccgroup in
<samp class="option">cc_stream_map</samp> is mapped to the output variant stream.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -re -i in.ts -b:v 1000k -b:a 64k -a53cc 1 -f hls \
  -cc_stream_map &quot;ccgroup:cc,instreamid:CC1,language:en&quot; \
  -master_pl_name master.m3u8 \
  http://example.com/live/out.m3u8
</pre></div>
<p>this will add <code class="code">#EXT-X-MEDIA</code> tag with <code class="code">TYPE=CLOSED-CAPTIONS</code> in the
master playlist with group name &rsquo;cc&rsquo;, language &rsquo;en&rsquo; (english) and INSTREAM-ID
&rsquo;CC1&rsquo;. Also, it will add <code class="code">CLOSED-CAPTIONS</code> attribute with group name &rsquo;cc&rsquo;
for the output variant stream.
</p>
</dd>
<dt><samp class="option">master_pl_name <var class="var">name</var></samp></dt>
<dd><p>Create HLS master playlist with the given name.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -re -i in.ts -f hls -master_pl_name master.m3u8 http://example.com/live/out.m3u8
</pre></div>
<p>creates an HLS master playlist with name <samp class="file">master.m3u8</samp> which is published
at <a class="url" href="http://example.com/live/">http://example.com/live/</a>.
</p>
</dd>
<dt><samp class="option">master_pl_publish_rate <var class="var">count</var></samp></dt>
<dd><p>Publish master play list repeatedly every after specified number of segment intervals.
</p>
<p>For example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -re -i in.ts -f hls -master_pl_name master.m3u8 \
-hls_time 2 -master_pl_publish_rate 30 http://example.com/live/out.m3u8
</pre></div>
<p>creates an HLS master playlist with name <samp class="file">master.m3u8</samp> and keeps
publishing it repeatedly every after 30 segments i.e. every after 60s.
</p>
</dd>
<dt><samp class="option">http_persistent <var class="var">bool</var></samp></dt>
<dd><p>Use persistent HTTP connections. Applicable only for HTTP output.
</p>
</dd>
<dt><samp class="option">timeout <var class="var">timeout</var></samp></dt>
<dd><p>Set timeout for socket I/O operations. Applicable only for HTTP output.
</p>
</dd>
<dt><samp class="option">ignore_io_errors <var class="var">bool</var></samp></dt>
<dd><p>Ignore IO errors during open, write and delete. Useful for long-duration runs with network output.
</p>
</dd>
<dt><samp class="option">headers <var class="var">headers</var></samp></dt>
<dd><p>Set custom HTTP headers, can override built in default headers. Applicable only for HTTP output.
</p></dd>
</dl>

<a name="iamf"></a>
<h3 class="section">4.47 iamf<span class="pull-right"><a class="anchor hidden-xs" href="#iamf" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-iamf" aria-hidden="true">TOC</a></span></h3>
<p>Immersive Audio Model and Formats (IAMF) muxer.
</p>
<p>IAMF is used to provide immersive audio content for presentation on a wide range
of devices in both streaming and offline applications. These applications
include internet audio streaming, multicasting/broadcasting services, file
download, gaming, communication, virtual and augmented reality, and others. In
these applications, audio may be played back on a wide range of devices, e.g.,
headphones, mobile phones, tablets, TVs, sound bars, home theater systems, and
big screens.
</p>
<p>This format was promoted and desgined by Alliance for Open Media.
</p>
<p>For more information about this format, see <a class="url" href="https://aomedia.org/iamf/">https://aomedia.org/iamf/</a>.
</p>
<a class="anchor" id="ico"></a><a name="ico-1"></a>
<h3 class="section">4.48 ico<span class="pull-right"><a class="anchor hidden-xs" href="#ico" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ico" aria-hidden="true">TOC</a></span></h3>

<p>ICO file muxer.
</p>
<p>Microsoft&rsquo;s icon file format (ICO) has some strict limitations that should be noted:
</p>
<ul class="itemize mark-bullet">
<li>Size cannot exceed 256 pixels in any dimension

</li><li>Only BMP and PNG images can be stored

</li><li>If a BMP image is used, it must be one of the following pixel formats:
<div class="example">
<pre class="example-preformatted">BMP Bit Depth      FFmpeg Pixel Format
1bit               pal8
4bit               pal8
8bit               pal8
16bit              rgb555le
24bit              bgr24
32bit              bgra
</pre></div>

</li><li>If a BMP image is used, it must use the BITMAPINFOHEADER DIB header

</li><li>If a PNG image is used, it must use the rgba pixel format
</li></ul>

<a name="ilbc"></a>
<h3 class="section">4.49 ilbc<span class="pull-right"><a class="anchor hidden-xs" href="#ilbc" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ilbc" aria-hidden="true">TOC</a></span></h3>
<p>Internet Low Bitrate Codec (iLBC) raw muxer.
</p>
<p>It accepts a single &lsquo;<samp class="samp">ilbc</samp>&rsquo; audio stream.
</p>
<a class="anchor" id="image2"></a><a name="image2_002c-image2pipe"></a>
<h3 class="section">4.50 image2, image2pipe<span class="pull-right"><a class="anchor hidden-xs" href="#image2_002c-image2pipe" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-image2_002c-image2pipe" aria-hidden="true">TOC</a></span></h3>
<p>Image file muxer.
</p>
<p>The &lsquo;<samp class="samp">image2</samp>&rsquo; muxer writes video frames to image files.
</p>
<p>The output filenames are specified by a pattern, which can be used to
produce sequentially numbered series of files.
The pattern may contain the string &quot;%d&quot; or &quot;%0<var class="var">N</var>d&quot;, this string
specifies the position of the characters representing a numbering in
the filenames. If the form &quot;%0<var class="var">N</var>d&quot; is used, the string
representing the number in each filename is 0-padded to <var class="var">N</var>
digits. The literal character &rsquo;%&rsquo; can be specified in the pattern with
the string &quot;%%&quot;.
</p>
<p>If the pattern contains &quot;%d&quot; or &quot;%0<var class="var">N</var>d&quot;, the first filename of
the file list specified will contain the number 1, all the following
numbers will be sequential.
</p>
<p>The pattern may contain a suffix which is used to automatically
determine the format of the image files to write.
</p>
<p>For example the pattern &quot;img-%03d.bmp&quot; will specify a sequence of
filenames of the form <samp class="file">img-001.bmp</samp>, <samp class="file">img-002.bmp</samp>, ...,
<samp class="file">img-010.bmp</samp>, etc.
The pattern &quot;img%%-%d.jpg&quot; will specify a sequence of filenames of the
form <samp class="file">img%-1.jpg</samp>, <samp class="file">img%-2.jpg</samp>, ..., <samp class="file">img%-10.jpg</samp>,
etc.
</p>
<p>The image muxer supports the .Y.U.V image file format. This format is
special in that each image frame consists of three files, for
each of the YUV420P components. To read or write this image file format,
specify the name of the &rsquo;.Y&rsquo; file. The muxer will automatically open the
&rsquo;.U&rsquo; and &rsquo;.V&rsquo; files as required.
</p>
<p>The &lsquo;<samp class="samp">image2pipe</samp>&rsquo; muxer accepts the same options as the &lsquo;<samp class="samp">image2</samp>&rsquo; muxer,
but ignores the pattern verification and expansion, as it is supposed to write
to the command output rather than to an actual stored file.
</p>
<a name="Options-27"></a>
<h4 class="subsection">4.50.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-27" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-27" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">frame_pts <var class="var">bool</var></samp></dt>
<dd><p>If set to 1, expand the filename with the packet PTS (presentation time stamp).
Default value is 0.
</p>
</dd>
<dt><samp class="option">start_number <var class="var">count</var></samp></dt>
<dd><p>Start the sequence from the specified number. Default value is 1.
</p>
</dd>
<dt><samp class="option">update <var class="var">bool</var></samp></dt>
<dd><p>If set to 1, the filename will always be interpreted as just a
filename, not a pattern, and the corresponding file will be continuously
overwritten with new images. Default value is 0.
</p>
</dd>
<dt><samp class="option">strftime <var class="var">bool</var></samp></dt>
<dd><p>If set to 1, expand the filename with date and time information from
<code class="code">strftime()</code>. Default value is 0.
</p>
</dd>
<dt><samp class="option">atomic_writing <var class="var">bool</var></samp></dt>
<dd><p>Write output to a temporary file, which is renamed to target filename once
writing is completed. Default is disabled.
</p>
</dd>
<dt><samp class="option">protocol_opts <var class="var">options_list</var></samp></dt>
<dd><p>Set protocol options as a :-separated list of key=value parameters. Values
containing the <code class="code">:</code> special character must be escaped.
</p></dd>
</dl>

<a name="Examples-12"></a>
<h4 class="subsection">4.50.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-12" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-12" aria-hidden="true">TOC</a></span></h4>
<ul class="itemize mark-bullet">
<li>Use <code class="command">ffmpeg</code> for creating a sequence of files <samp class="file">img-001.jpeg</samp>,
<samp class="file">img-002.jpeg</samp>, ..., taking one image every second from the input video:
<div class="example">
<pre class="example-preformatted">ffmpeg -i in.avi -vsync cfr -r 1 -f image2 'img-%03d.jpeg'
</pre></div>

<p>Note that with <code class="command">ffmpeg</code>, if the format is not specified with the
<code class="code">-f</code> option and the output filename specifies an image file
format, the image2 muxer is automatically selected, so the previous
command can be written as:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i in.avi -vsync cfr -r 1 'img-%03d.jpeg'
</pre></div>

<p>Note also that the pattern must not necessarily contain &quot;%d&quot; or
&quot;%0<var class="var">N</var>d&quot;, for example to create a single image file
<samp class="file">img.jpeg</samp> from the start of the input video you can employ the command:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i in.avi -f image2 -frames:v 1 img.jpeg
</pre></div>

</li><li>The <samp class="option">strftime</samp> option allows you to expand the filename with
date and time information. Check the documentation of
the <code class="code">strftime()</code> function for the syntax.

<p>To generate image files from the <code class="code">strftime()</code> &quot;%Y-%m-%d_%H-%M-%S&quot; pattern,
the following <code class="command">ffmpeg</code> command can be used:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -f v4l2 -r 1 -i /dev/video0 -f image2 -strftime 1 &quot;%Y-%m-%d_%H-%M-%S.jpg&quot;
</pre></div>

</li><li>Set the file name with current frame&rsquo;s PTS:
<div class="example">
<pre class="example-preformatted">ffmpeg -f v4l2 -r 1 -i /dev/video0 -copyts -f image2 -frame_pts true %d.jpg
</pre></div>

</li><li>Publish contents of your desktop directly to a WebDAV server every second:
<div class="example">
<pre class="example-preformatted">ffmpeg -f x11grab -framerate 1 -i :0.0 -q:v 6 -update 1 -protocol_opts method=PUT http://example.com/desktop.jpg
</pre></div>
</li></ul>

<a name="ircam"></a>
<h3 class="section">4.51 ircam<span class="pull-right"><a class="anchor hidden-xs" href="#ircam" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ircam" aria-hidden="true">TOC</a></span></h3>
<p>Berkeley / IRCAM / CARL Sound Filesystem (BICSF) format muxer.
</p>
<p>The Berkeley/IRCAM/CARL Sound Format, developed in the 1980s, is a result of the
merging of several different earlier sound file formats and systems including
the csound system developed by Dr Gareth Loy at the Computer Audio Research Lab
(CARL) at UC San Diego, the IRCAM sound file system developed by Rob Gross and
Dan Timis at the Institut de Recherche et Coordination Acoustique / Musique in
Paris and the Berkeley Fast Filesystem.
</p>
<p>It was developed initially as part of the Berkeley/IRCAM/CARL Sound Filesystem,
a suite of programs designed to implement a filesystem for audio applications
running under Berkeley UNIX. It was particularly popular in academic music
research centres, and was used a number of times in the creation of early
computer-generated compositions.
</p>
<p>This muxer accepts a single audio stream containing PCM data.
</p>
<a name="ivf"></a>
<h3 class="section">4.52 ivf<span class="pull-right"><a class="anchor hidden-xs" href="#ivf" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ivf" aria-hidden="true">TOC</a></span></h3>
<p>On2 IVF muxer.
</p>
<p>IVF was developed by On2 Technologies (formerly known as Duck
Corporation), to store internally developed codecs.
</p>
<p>This muxer accepts a single &lsquo;<samp class="samp">vp8</samp>&rsquo;, &lsquo;<samp class="samp">vp9</samp>&rsquo;, or &lsquo;<samp class="samp">av1</samp>&rsquo;
video stream.
</p>
<a name="jacosub"></a>
<h3 class="section">4.53 jacosub<span class="pull-right"><a class="anchor hidden-xs" href="#jacosub" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-jacosub" aria-hidden="true">TOC</a></span></h3>
<p>JACOsub subtitle format muxer.
</p>
<p>This muxer accepts a single &lsquo;<samp class="samp">jacosub</samp>&rsquo; subtitles stream.
</p>
<p>For more information about the format, see
<a class="url" href="http://unicorn.us.com/jacosub/jscripts.html">http://unicorn.us.com/jacosub/jscripts.html</a>.
</p>
<a name="kvag"></a>
<h3 class="section">4.54 kvag<span class="pull-right"><a class="anchor hidden-xs" href="#kvag" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-kvag" aria-hidden="true">TOC</a></span></h3>
<p>Simon &amp; Schuster Interactive VAG muxer.
</p>
<p>This custom VAG container is used by some Simon &amp; Schuster Interactive
games such as &quot;Real War&quot;, and &quot;Real War: Rogue States&quot;.
</p>
<p>This muxer accepts a single &lsquo;<samp class="samp">adpcm_ima_ssi</samp>&rsquo; audio stream.
</p>
<a name="lc3"></a>
<h3 class="section">4.55 lc3<span class="pull-right"><a class="anchor hidden-xs" href="#lc3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-lc3" aria-hidden="true">TOC</a></span></h3>
<p>Bluetooth SIG Low Complexity Communication Codec audio (LC3), or
ETSI TS 103 634 Low Complexity Communication Codec plus (LC3plus).
</p>
<p>This muxer accepts a single &lsquo;<samp class="samp">lc3</samp>&rsquo; audio stream.
</p>
<a name="lrc"></a>
<h3 class="section">4.56 lrc<span class="pull-right"><a class="anchor hidden-xs" href="#lrc" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-lrc" aria-hidden="true">TOC</a></span></h3>
<p>LRC lyrics file format muxer.
</p>
<p>LRC (short for LyRiCs) is a computer file format that synchronizes
song lyrics with an audio file, such as MP3, Vorbis, or MIDI.
</p>
<p>This muxer accepts a single &lsquo;<samp class="samp">subrip</samp>&rsquo; or &lsquo;<samp class="samp">text</samp>&rsquo; subtitles stream.
</p>
<a name="Metadata"></a>
<h4 class="subsection">4.56.1 Metadata<span class="pull-right"><a class="anchor hidden-xs" href="#Metadata" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Metadata" aria-hidden="true">TOC</a></span></h4>
<p>The following metadata tags are converted to the format corresponding
metadata:
</p>
<dl class="table">
<dt><samp class="option">title</samp></dt>
<dt><samp class="option">album</samp></dt>
<dt><samp class="option">artist</samp></dt>
<dt><samp class="option">author</samp></dt>
<dt><samp class="option">creator</samp></dt>
<dt><samp class="option">encoder</samp></dt>
<dt><samp class="option">encoder_version</samp></dt>
</dl>

<p>If &lsquo;<samp class="samp">encoder_version</samp>&rsquo; is not explicitly set, it is automatically
set to the libavformat version.
</p>
<a name="matroska"></a>
<h3 class="section">4.57 matroska<span class="pull-right"><a class="anchor hidden-xs" href="#matroska" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-matroska" aria-hidden="true">TOC</a></span></h3>
<p>Matroska container muxer.
</p>
<p>This muxer implements the matroska and webm container specs.
</p>
<a name="Metadata-1"></a>
<h4 class="subsection">4.57.1 Metadata<span class="pull-right"><a class="anchor hidden-xs" href="#Metadata-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Metadata-1" aria-hidden="true">TOC</a></span></h4>
<p>The recognized metadata settings in this muxer are:
</p>
<dl class="table">
<dt><samp class="option">title</samp></dt>
<dd><p>Set title name provided to a single track. This gets mapped to
the FileDescription element for a stream written as attachment.
</p>
</dd>
<dt><samp class="option">language</samp></dt>
<dd><p>Specify the language of the track in the Matroska languages form.
</p>
<p>The language can be either the 3 letters bibliographic ISO-639-2 (ISO
639-2/B) form (like &quot;fre&quot; for French), or a language code mixed with a
country code for specialities in languages (like &quot;fre-ca&quot; for Canadian
French).
</p>
</dd>
<dt><samp class="option">stereo_mode</samp></dt>
<dd><p>Set stereo 3D video layout of two views in a single video track.
</p>
<p>The following values are recognized:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">mono</samp>&rsquo;</dt>
<dd><p>video is not stereo
</p></dd>
<dt>&lsquo;<samp class="samp">left_right</samp>&rsquo;</dt>
<dd><p>Both views are arranged side by side, Left-eye view is on the left
</p></dd>
<dt>&lsquo;<samp class="samp">bottom_top</samp>&rsquo;</dt>
<dd><p>Both views are arranged in top-bottom orientation, Left-eye view is at bottom
</p></dd>
<dt>&lsquo;<samp class="samp">top_bottom</samp>&rsquo;</dt>
<dd><p>Both views are arranged in top-bottom orientation, Left-eye view is on top
</p></dd>
<dt>&lsquo;<samp class="samp">checkerboard_rl</samp>&rsquo;</dt>
<dd><p>Each view is arranged in a checkerboard interleaved pattern, Left-eye view being first
</p></dd>
<dt>&lsquo;<samp class="samp">checkerboard_lr</samp>&rsquo;</dt>
<dd><p>Each view is arranged in a checkerboard interleaved pattern, Right-eye view being first
</p></dd>
<dt>&lsquo;<samp class="samp">row_interleaved_rl</samp>&rsquo;</dt>
<dd><p>Each view is constituted by a row based interleaving, Right-eye view is first row
</p></dd>
<dt>&lsquo;<samp class="samp">row_interleaved_lr</samp>&rsquo;</dt>
<dd><p>Each view is constituted by a row based interleaving, Left-eye view is first row
</p></dd>
<dt>&lsquo;<samp class="samp">col_interleaved_rl</samp>&rsquo;</dt>
<dd><p>Both views are arranged in a column based interleaving manner, Right-eye view is first column
</p></dd>
<dt>&lsquo;<samp class="samp">col_interleaved_lr</samp>&rsquo;</dt>
<dd><p>Both views are arranged in a column based interleaving manner, Left-eye view is first column
</p></dd>
<dt>&lsquo;<samp class="samp">anaglyph_cyan_red</samp>&rsquo;</dt>
<dd><p>All frames are in anaglyph format viewable through red-cyan filters
</p></dd>
<dt>&lsquo;<samp class="samp">right_left</samp>&rsquo;</dt>
<dd><p>Both views are arranged side by side, Right-eye view is on the left
</p></dd>
<dt>&lsquo;<samp class="samp">anaglyph_green_magenta</samp>&rsquo;</dt>
<dd><p>All frames are in anaglyph format viewable through green-magenta filters
</p></dd>
<dt>&lsquo;<samp class="samp">block_lr</samp>&rsquo;</dt>
<dd><p>Both eyes laced in one Block, Left-eye view is first
</p></dd>
<dt>&lsquo;<samp class="samp">block_rl</samp>&rsquo;</dt>
<dd><p>Both eyes laced in one Block, Right-eye view is first
</p></dd>
</dl>
</dd>
</dl>

<p>For example a 3D WebM clip can be created using the following command line:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i sample_left_right_clip.mpg -an -c:v libvpx -metadata stereo_mode=left_right -y stereo_clip.webm
</pre></div>

<a name="Options-28"></a>
<h4 class="subsection">4.57.2 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-28" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-28" aria-hidden="true">TOC</a></span></h4>
<dl class="table">
<dt><samp class="option">reserve_index_space <var class="var">size</var></samp></dt>
<dd><p>By default, this muxer writes the index for seeking (called cues in Matroska
terms) at the end of the file, because it cannot know in advance how much space
to leave for the index at the beginning of the file. However for some use cases
&ndash; e.g.  streaming where seeking is possible but slow &ndash; it is useful to put the
index at the beginning of the file.
</p>
<p>If this option is set to a non-zero value, the muxer will reserve <var class="var">size</var> bytes
of space in the file header and then try to write the cues there when the muxing
finishes. If the reserved space does not suffice, no Cues will be written, the
file will be finalized and writing the trailer will return an error.
A safe size for most use cases should be about 50kB per hour of video.
</p>
<p>Note that cues are only written if the output is seekable and this option will
have no effect if it is not.
</p>
</dd>
<dt><samp class="option">cues_to_front <var class="var">bool</var></samp></dt>
<dd><p>If set, the muxer will write the index at the beginning of the file
by shifting the main data if necessary. This can be combined with
reserve_index_space in which case the data is only shifted if
the initially reserved space turns out to be insufficient.
</p>
<p>This option is ignored if the output is unseekable.
</p>
</dd>
<dt><samp class="option">cluster_size_limit <var class="var">size</var></samp></dt>
<dd><p>Store at most the provided amount of bytes in a cluster.
</p>
<p>If not specified, the limit is set automatically to a sensible
hardcoded fixed value.
</p>
</dd>
<dt><samp class="option">cluster_time_limit <var class="var">duration</var></samp></dt>
<dd><p>Store at most the provided number of milliseconds in a cluster.
</p>
<p>If not specified, the limit is set automatically to a sensible
hardcoded fixed value.
</p>
</dd>
<dt><samp class="option">dash <var class="var">bool</var></samp></dt>
<dd><p>Create a WebM file conforming to WebM DASH specification. By default
it is set to <code class="code">false</code>.
</p>
</dd>
<dt><samp class="option">dash_track_number <var class="var">index</var></samp></dt>
<dd><p>Track number for the DASH stream. By default it is set to <code class="code">1</code>.
</p>
</dd>
<dt><samp class="option">live <var class="var">bool</var></samp></dt>
<dd><p>Write files assuming it is a live stream. By default it is set to
<code class="code">false</code>.
</p>
</dd>
<dt><samp class="option">allow_raw_vfw <var class="var">bool</var></samp></dt>
<dd><p>Allow raw VFW mode. By default it is set to <code class="code">false</code>.
</p>
</dd>
<dt><samp class="option">flipped_raw_rgb <var class="var">bool</var></samp></dt>
<dd><p>If set to <code class="code">true</code>, store positive height for raw RGB bitmaps, which indicates
bitmap is stored bottom-up. Note that this option does not flip the bitmap
which has to be done manually beforehand, e.g. by using the &lsquo;<samp class="samp">vflip</samp>&rsquo; filter.
Default is <code class="code">false</code> and indicates bitmap is stored top down.
</p>
</dd>
<dt><samp class="option">write_crc32 <var class="var">bool</var></samp></dt>
<dd><p>Write a CRC32 element inside every Level 1 element. By default it is
set to <code class="code">true</code>. This option is ignored for WebM.
</p>
</dd>
<dt><samp class="option">default_mode <var class="var">mode</var></samp></dt>
<dd><p>Control how the FlagDefault of the output tracks will be set.
It influences which tracks players should play by default. The default mode
is &lsquo;<samp class="samp">passthrough</samp>&rsquo;.
</p><dl class="table">
<dt>&lsquo;<samp class="samp">infer</samp>&rsquo;</dt>
<dd><p>Every track with disposition default will have the FlagDefault set.
Additionally, for each type of track (audio, video or subtitle), if no track
with disposition default of this type exists, then the first track of this type
will be marked as default (if existing). This ensures that the default flag
is set in a sensible way even if the input originated from containers that
lack the concept of default tracks.
</p></dd>
<dt>&lsquo;<samp class="samp">infer_no_subs</samp>&rsquo;</dt>
<dd><p>This mode is the same as infer except that if no subtitle track with
disposition default exists, no subtitle track will be marked as default.
</p></dd>
<dt>&lsquo;<samp class="samp">passthrough</samp>&rsquo;</dt>
<dd><p>In this mode the FlagDefault is set if and only if the AV_DISPOSITION_DEFAULT
flag is set in the disposition of the corresponding stream.
</p></dd>
</dl>
</dd>
</dl>

<a class="anchor" id="md5"></a><a name="md5-1"></a>
<h3 class="section">4.58 md5<span class="pull-right"><a class="anchor hidden-xs" href="#md5" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-md5" aria-hidden="true">TOC</a></span></h3>
<p>MD5 testing format.
</p>
<p>This is a variant of the <a class="ref" href="#hash">hash</a> muxer. Unlike that muxer, it
defaults to using the MD5 hash function.
</p>
<p>See also the <a class="ref" href="#hash">hash</a> and <a class="ref" href="#framemd5">framemd5</a> muxers.
</p>
<a name="Examples-13"></a>
<h4 class="subsection">4.58.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-13" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-13" aria-hidden="true">TOC</a></span></h4>
<ul class="itemize mark-bullet">
<li>To compute the MD5 hash of the input converted to raw
audio and video, and store it in the file <samp class="file">out.md5</samp>:
<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f md5 out.md5
</pre></div>

</li><li>To print the MD5 hash to stdout:
<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f md5 -
</pre></div>
</li></ul>

<a name="microdvd"></a>
<h3 class="section">4.59 microdvd<span class="pull-right"><a class="anchor hidden-xs" href="#microdvd" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-microdvd" aria-hidden="true">TOC</a></span></h3>
<p>MicroDVD subtitle format muxer.
</p>
<p>This muxer accepts a single &lsquo;<samp class="samp">microdvd</samp>&rsquo; subtitles stream.
</p>
<a name="mmf"></a>
<h3 class="section">4.60 mmf<span class="pull-right"><a class="anchor hidden-xs" href="#mmf" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mmf" aria-hidden="true">TOC</a></span></h3>
<p>Synthetic music Mobile Application Format (SMAF) format muxer.
</p>
<p>SMAF is a music data format specified by Yamaha for portable
electronic devices, such as mobile phones and personal digital
assistants.
</p>
<p>This muxer accepts a single &lsquo;<samp class="samp">adpcm_yamaha</samp>&rsquo; audio stream.
</p>
<a name="mp3"></a>
<h3 class="section">4.61 mp3<span class="pull-right"><a class="anchor hidden-xs" href="#mp3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mp3" aria-hidden="true">TOC</a></span></h3>

<p>The MP3 muxer writes a raw MP3 stream with the following optional features:
</p><ul class="itemize mark-bullet">
<li>An ID3v2 metadata header at the beginning (enabled by default). Versions 2.3 and
2.4 are supported, the <code class="code">id3v2_version</code> private option controls which one is
used (3 or 4). Setting <code class="code">id3v2_version</code> to 0 disables the ID3v2 header
completely.

<p>The muxer supports writing attached pictures (APIC frames) to the ID3v2 header.
The pictures are supplied to the muxer in form of a video stream with a single
packet. There can be any number of those streams, each will correspond to a
single APIC frame.  The stream metadata tags <var class="var">title</var> and <var class="var">comment</var> map
to APIC <var class="var">description</var> and <var class="var">picture type</var> respectively. See
<a class="url" href="http://id3.org/id3v2.4.0-frames">http://id3.org/id3v2.4.0-frames</a> for allowed picture types.
</p>
<p>Note that the APIC frames must be written at the beginning, so the muxer will
buffer the audio frames until it gets all the pictures. It is therefore advised
to provide the pictures as soon as possible to avoid excessive buffering.
</p>
</li><li>A Xing/LAME frame right after the ID3v2 header (if present). It is enabled by
default, but will be written only if the output is seekable. The
<code class="code">write_xing</code> private option can be used to disable it.  The frame contains
various information that may be useful to the decoder, like the audio duration
or encoder delay.

</li><li>A legacy ID3v1 tag at the end of the file (disabled by default). It may be
enabled with the <code class="code">write_id3v1</code> private option, but as its capabilities are
very limited, its usage is not recommended.
</li></ul>

<p>Examples:
</p>
<p>Write an mp3 with an ID3v2.3 header and an ID3v1 footer:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -id3v2_version 3 -write_id3v1 1 out.mp3
</pre></div>

<p>To attach a picture to an mp3 file select both the audio and the picture stream
with <code class="code">map</code>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i input.mp3 -i cover.png -c copy -map 0 -map 1
-metadata:s:v title=&quot;Album cover&quot; -metadata:s:v comment=&quot;Cover (Front)&quot; out.mp3
</pre></div>

<p>Write a &quot;clean&quot; MP3 without any extra features:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i input.wav -write_xing 0 -id3v2_version 0 out.mp3
</pre></div>

<a name="mpegts-1"></a>
<h3 class="section">4.62 mpegts<span class="pull-right"><a class="anchor hidden-xs" href="#mpegts-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mpegts-1" aria-hidden="true">TOC</a></span></h3>

<p>MPEG transport stream muxer.
</p>
<p>This muxer implements ISO 13818-1 and part of ETSI EN 300 468.
</p>
<p>The recognized metadata settings in mpegts muxer are <code class="code">service_provider</code>
and <code class="code">service_name</code>. If they are not set the default for
<code class="code">service_provider</code> is &lsquo;<samp class="samp">FFmpeg</samp>&rsquo; and the default for
<code class="code">service_name</code> is &lsquo;<samp class="samp">Service01</samp>&rsquo;.
</p>
<a name="Options-29"></a>
<h4 class="subsection">4.62.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-29" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-29" aria-hidden="true">TOC</a></span></h4>

<p>The muxer options are:
</p>
<dl class="table">
<dt><samp class="option">mpegts_transport_stream_id <var class="var">integer</var></samp></dt>
<dd><p>Set the &lsquo;<samp class="samp">transport_stream_id</samp>&rsquo;. This identifies a transponder in DVB.
Default is <code class="code">0x0001</code>.
</p>
</dd>
<dt><samp class="option">mpegts_original_network_id <var class="var">integer</var></samp></dt>
<dd><p>Set the &lsquo;<samp class="samp">original_network_id</samp>&rsquo;. This is unique identifier of a
network in DVB. Its main use is in the unique identification of a service
through the path &lsquo;<samp class="samp">Original_Network_ID, Transport_Stream_ID</samp>&rsquo;. Default
is <code class="code">0x0001</code>.
</p>
</dd>
<dt><samp class="option">mpegts_service_id <var class="var">integer</var></samp></dt>
<dd><p>Set the &lsquo;<samp class="samp">service_id</samp>&rsquo;, also known as program in DVB. Default is
<code class="code">0x0001</code>.
</p>
</dd>
<dt><samp class="option">mpegts_service_type <var class="var">integer</var></samp></dt>
<dd><p>Set the program &lsquo;<samp class="samp">service_type</samp>&rsquo;. Default is <code class="code">digital_tv</code>.
Accepts the following options:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">hex_value</samp>&rsquo;</dt>
<dd><p>Any hexadecimal value between <code class="code">0x01</code> and <code class="code">0xff</code> as defined in
ETSI 300 468.
</p></dd>
<dt>&lsquo;<samp class="samp">digital_tv</samp>&rsquo;</dt>
<dd><p>Digital TV service.
</p></dd>
<dt>&lsquo;<samp class="samp">digital_radio</samp>&rsquo;</dt>
<dd><p>Digital Radio service.
</p></dd>
<dt>&lsquo;<samp class="samp">teletext</samp>&rsquo;</dt>
<dd><p>Teletext service.
</p></dd>
<dt>&lsquo;<samp class="samp">advanced_codec_digital_radio</samp>&rsquo;</dt>
<dd><p>Advanced Codec Digital Radio service.
</p></dd>
<dt>&lsquo;<samp class="samp">mpeg2_digital_hdtv</samp>&rsquo;</dt>
<dd><p>MPEG2 Digital HDTV service.
</p></dd>
<dt>&lsquo;<samp class="samp">advanced_codec_digital_sdtv</samp>&rsquo;</dt>
<dd><p>Advanced Codec Digital SDTV service.
</p></dd>
<dt>&lsquo;<samp class="samp">advanced_codec_digital_hdtv</samp>&rsquo;</dt>
<dd><p>Advanced Codec Digital HDTV service.
</p></dd>
</dl>

</dd>
<dt><samp class="option">mpegts_pmt_start_pid <var class="var">integer</var></samp></dt>
<dd><p>Set the first PID for PMTs. Default is <code class="code">0x1000</code>, minimum is <code class="code">0x0020</code>,
maximum is <code class="code">0x1ffa</code>. This option has no effect in m2ts mode where the PMT
PID is fixed <code class="code">0x0100</code>.
</p>
</dd>
<dt><samp class="option">mpegts_start_pid <var class="var">integer</var></samp></dt>
<dd><p>Set the first PID for elementary streams. Default is <code class="code">0x0100</code>, minimum is
<code class="code">0x0020</code>, maximum is <code class="code">0x1ffa</code>. This option has no effect in m2ts mode
where the elementary stream PIDs are fixed.
</p>
</dd>
<dt><samp class="option">mpegts_m2ts_mode <var class="var">boolean</var></samp></dt>
<dd><p>Enable m2ts mode if set to <code class="code">1</code>. Default value is <code class="code">-1</code> which
disables m2ts mode.
</p>
</dd>
<dt><samp class="option">muxrate <var class="var">integer</var></samp></dt>
<dd><p>Set a constant muxrate. Default is VBR.
</p>
</dd>
<dt><samp class="option">pes_payload_size <var class="var">integer</var></samp></dt>
<dd><p>Set minimum PES packet payload in bytes. Default is <code class="code">2930</code>.
</p>
</dd>
<dt><samp class="option">mpegts_flags <var class="var">flags</var></samp></dt>
<dd><p>Set mpegts flags. Accepts the following options:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">resend_headers</samp>&rsquo;</dt>
<dd><p>Reemit PAT/PMT before writing the next packet.
</p></dd>
<dt>&lsquo;<samp class="samp">latm</samp>&rsquo;</dt>
<dd><p>Use LATM packetization for AAC.
</p></dd>
<dt>&lsquo;<samp class="samp">pat_pmt_at_frames</samp>&rsquo;</dt>
<dd><p>Reemit PAT and PMT at each video frame.
</p></dd>
<dt>&lsquo;<samp class="samp">system_b</samp>&rsquo;</dt>
<dd><p>Conform to System B (DVB) instead of System A (ATSC).
</p></dd>
<dt>&lsquo;<samp class="samp">initial_discontinuity</samp>&rsquo;</dt>
<dd><p>Mark the initial packet of each stream as discontinuity.
</p></dd>
<dt>&lsquo;<samp class="samp">nit</samp>&rsquo;</dt>
<dd><p>Emit NIT table.
</p></dd>
<dt>&lsquo;<samp class="samp">omit_rai</samp>&rsquo;</dt>
<dd><p>Disable writing of random access indicator.
</p></dd>
</dl>

</dd>
<dt><samp class="option">mpegts_copyts <var class="var">boolean</var></samp></dt>
<dd><p>Preserve original timestamps, if value is set to <code class="code">1</code>. Default value
is <code class="code">-1</code>, which results in shifting timestamps so that they start from 0.
</p>
</dd>
<dt><samp class="option">omit_video_pes_length <var class="var">boolean</var></samp></dt>
<dd><p>Omit the PES packet length for video packets. Default is <code class="code">1</code> (true).
</p>
</dd>
<dt><samp class="option">pcr_period <var class="var">integer</var></samp></dt>
<dd><p>Override the default PCR retransmission time in milliseconds. Default is
<code class="code">-1</code> which means that the PCR interval will be determined automatically:
20 ms is used for CBR streams, the highest multiple of the frame duration which
is less than 100 ms is used for VBR streams.
</p>
</dd>
<dt><samp class="option">pat_period <var class="var">duration</var></samp></dt>
<dd><p>Maximum time in seconds between PAT/PMT tables. Default is <code class="code">0.1</code>.
</p>
</dd>
<dt><samp class="option">sdt_period <var class="var">duration</var></samp></dt>
<dd><p>Maximum time in seconds between SDT tables. Default is <code class="code">0.5</code>.
</p>
</dd>
<dt><samp class="option">nit_period <var class="var">duration</var></samp></dt>
<dd><p>Maximum time in seconds between NIT tables. Default is <code class="code">0.5</code>.
</p>
</dd>
<dt><samp class="option">tables_version <var class="var">integer</var></samp></dt>
<dd><p>Set PAT, PMT, SDT and NIT version (default <code class="code">0</code>, valid values are from 0 to 31, inclusively).
This option allows updating stream structure so that standard consumer may
detect the change. To do so, reopen output <code class="code">AVFormatContext</code> (in case of API
usage) or restart <code class="command">ffmpeg</code> instance, cyclically changing
<samp class="option">tables_version</samp> value:
</p>
<div class="example">
<pre class="example-preformatted">ffmpeg -i source1.ts -codec copy -f mpegts -tables_version 0 udp://1.1.1.1:1111
ffmpeg -i source2.ts -codec copy -f mpegts -tables_version 1 udp://1.1.1.1:1111
...
ffmpeg -i source3.ts -codec copy -f mpegts -tables_version 31 udp://1.1.1.1:1111
ffmpeg -i source1.ts -codec copy -f mpegts -tables_version 0 udp://1.1.1.1:1111
ffmpeg -i source2.ts -codec copy -f mpegts -tables_version 1 udp://1.1.1.1:1111
...
</pre></div>
</dd>
</dl>

<a name="Example-8"></a>
<h4 class="subsection">4.62.2 Example<span class="pull-right"><a class="anchor hidden-xs" href="#Example-8" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Example-8" aria-hidden="true">TOC</a></span></h4>

<div class="example">
<pre class="example-preformatted">ffmpeg -i file.mpg -c copy \
     -mpegts_original_network_id 0x1122 \
     -mpegts_transport_stream_id 0x3344 \
     -mpegts_service_id 0x5566 \
     -mpegts_pmt_start_pid 0x1500 \
     -mpegts_start_pid 0x150 \
     -metadata service_provider=&quot;Some provider&quot; \
     -metadata service_name=&quot;Some Channel&quot; \
     out.ts
</pre></div>

<a name="mxf_002c-mxf_005fd10_002c-mxf_005fopatom"></a>
<h3 class="section">4.63 mxf, mxf_d10, mxf_opatom<span class="pull-right"><a class="anchor hidden-xs" href="#mxf_002c-mxf_005fd10_002c-mxf_005fopatom" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mxf_002c-mxf_005fd10_002c-mxf_005fopatom" aria-hidden="true">TOC</a></span></h3>

<p>MXF muxer.
</p>
<a name="Options-30"></a>
<h4 class="subsection">4.63.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-30" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-30" aria-hidden="true">TOC</a></span></h4>

<p>The muxer options are:
</p>
<dl class="table">
<dt><samp class="option">store_user_comments <var class="var">bool</var></samp></dt>
<dd><p>Set if user comments should be stored if available or never.
IRT D-10 does not allow user comments. The default is thus to write them for
mxf and mxf_opatom but not for mxf_d10
</p></dd>
</dl>

<a name="null"></a>
<h3 class="section">4.64 null<span class="pull-right"><a class="anchor hidden-xs" href="#null" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-null" aria-hidden="true">TOC</a></span></h3>

<p>Null muxer.
</p>
<p>This muxer does not generate any output file, it is mainly useful for
testing or benchmarking purposes.
</p>
<p>For example to benchmark decoding with <code class="command">ffmpeg</code> you can use the
command:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -benchmark -i INPUT -f null out.null
</pre></div>

<p>Note that the above command does not read or write the <samp class="file">out.null</samp>
file, but specifying the output file is required by the <code class="command">ffmpeg</code>
syntax.
</p>
<p>Alternatively you can write the command as:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -benchmark -i INPUT -f null -
</pre></div>

<a name="nut"></a>
<h3 class="section">4.65 nut<span class="pull-right"><a class="anchor hidden-xs" href="#nut" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-nut" aria-hidden="true">TOC</a></span></h3>

<dl class="table">
<dt><samp class="option">-syncpoints <var class="var">flags</var></samp></dt>
<dd><p>Change the syncpoint usage in nut:
</p><dl class="table">
<dt><samp class="option"><var class="var">default</var> use the normal low-overhead seeking aids.</samp></dt>
<dt><samp class="option"><var class="var">none</var> do not use the syncpoints at all, reducing the overhead but making the stream non-seekable;</samp></dt>
<dd><p>Use of this option is not recommended, as the resulting files are very damage
    sensitive and seeking is not possible. Also in general the overhead from
    syncpoints is negligible. Note, -<code class="code">write_index</code> 0 can be used to disable
    all growing data tables, allowing to mux endless streams with limited memory
    and without these disadvantages.
</p></dd>
<dt><samp class="option"><var class="var">timestamped</var> extend the syncpoint with a wallclock field.</samp></dt>
</dl>
<p>The <var class="var">none</var> and <var class="var">timestamped</var> flags are experimental.
</p></dd>
<dt><samp class="option">-write_index <var class="var">bool</var></samp></dt>
<dd><p>Write index at the end, the default is to write an index.
</p></dd>
</dl>

<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f_strict experimental -syncpoints none - | processor
</pre></div>

<a name="ogg"></a>
<h3 class="section">4.66 ogg<span class="pull-right"><a class="anchor hidden-xs" href="#ogg" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ogg" aria-hidden="true">TOC</a></span></h3>

<p>Ogg container muxer.
</p>
<dl class="table">
<dt><samp class="option">-page_duration <var class="var">duration</var></samp></dt>
<dd><p>Preferred page duration, in microseconds. The muxer will attempt to create
pages that are approximately <var class="var">duration</var> microseconds long. This allows the
user to compromise between seek granularity and container overhead. The default
is 1 second. A value of 0 will fill all segments, making pages as large as
possible. A value of 1 will effectively use 1 packet-per-page in most
situations, giving a small seek granularity at the cost of additional container
overhead.
</p></dd>
<dt><samp class="option">-serial_offset <var class="var">value</var></samp></dt>
<dd><p>Serial value from which to set the streams serial number.
Setting it to different and sufficiently large values ensures that the produced
ogg files can be safely chained.
</p>
</dd>
</dl>

<a class="anchor" id="rcwtenc"></a><a name="rcwt-1"></a>
<h3 class="section">4.67 rcwt<span class="pull-right"><a class="anchor hidden-xs" href="#rcwt-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-rcwt-1" aria-hidden="true">TOC</a></span></h3>

<p>RCWT (Raw Captions With Time) is a format native to ccextractor, a commonly
used open source tool for processing 608/708 Closed Captions (CC) sources.
It can be used to archive the original extracted CC bitstream and to produce
a source file for later processing or conversion. The format allows
for interoperability between ccextractor and FFmpeg, is simple to parse,
and can be used to create a backup of the CC presentation.
</p>
<p>This muxer implements the specification as of March 2024, which has
been stable and unchanged since April 2014.
</p>
<p>This muxer will have some nuances from the way that ccextractor muxes RCWT.
No compatibility issues when processing the output with ccextractor
have been observed as a result of this so far, but mileage may vary
and outputs will not be a bit-exact match.
</p>
<p>A free specification of RCWT can be found here:
<a class="url" href="https://github.com/CCExtractor/ccextractor/blob/master/docs/BINARY_FILE_FORMAT.TXT">https://github.com/CCExtractor/ccextractor/blob/master/docs/BINARY_FILE_FORMAT.TXT</a>
</p>
<a name="Examples-14"></a>
<h4 class="subsection">4.67.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-14" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-14" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Extract Closed Captions to RCWT using lavfi:
<div class="example">
<pre class="example-preformatted">ffmpeg -f lavfi -i &quot;movie=INPUT.mkv[out+subcc]&quot; -map 0:s:0 -c:s copy -f rcwt CC.rcwt.bin
</pre></div>
</li></ul>

<a class="anchor" id="segment"></a><a name="segment_002c-stream_005fsegment_002c-ssegment"></a>
<h3 class="section">4.68 segment, stream_segment, ssegment<span class="pull-right"><a class="anchor hidden-xs" href="#segment_002c-stream_005fsegment_002c-ssegment" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-segment_002c-stream_005fsegment_002c-ssegment" aria-hidden="true">TOC</a></span></h3>

<p>Basic stream segmenter.
</p>
<p>This muxer outputs streams to a number of separate files of nearly
fixed duration. Output filename pattern can be set in a fashion
similar to <a class="ref" href="#image2">image2</a>, or by using a <code class="code">strftime</code> template if
the <samp class="option">strftime</samp> option is enabled.
</p>
<p><code class="code">stream_segment</code> is a variant of the muxer used to write to
streaming output formats, i.e. which do not require global headers,
and is recommended for outputting e.g. to MPEG transport stream segments.
<code class="code">ssegment</code> is a shorter alias for <code class="code">stream_segment</code>.
</p>
<p>Every segment starts with a keyframe of the selected reference stream,
which is set through the <samp class="option">reference_stream</samp> option.
</p>
<p>Note that if you want accurate splitting for a video file, you need to
make the input key frames correspond to the exact splitting times
expected by the segmenter, or the segment muxer will start the new
segment with the key frame found next after the specified start
time.
</p>
<p>The segment muxer works best with a single constant frame rate video.
</p>
<p>Optionally it can generate a list of the created segments, by setting
the option <var class="var">segment_list</var>. The list type is specified by the
<var class="var">segment_list_type</var> option. The entry filenames in the segment
list are set by default to the basename of the corresponding segment
files.
</p>
<p>See also the <a class="ref" href="#hls">hls</a> muxer, which provides a more specific
implementation for HLS segmentation.
</p>
<a name="Options-31"></a>
<h4 class="subsection">4.68.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-31" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-31" aria-hidden="true">TOC</a></span></h4>

<p>The segment muxer supports the following options:
</p>
<dl class="table">
<dt><samp class="option">increment_tc <var class="var">1|0</var></samp></dt>
<dd><p>if set to <code class="code">1</code>, increment timecode between each segment
If this is selected, the input need to have
a timecode in the first video stream. Default value is
<code class="code">0</code>.
</p>
</dd>
<dt><samp class="option">reference_stream <var class="var">specifier</var></samp></dt>
<dd><p>Set the reference stream, as specified by the string <var class="var">specifier</var>.
If <var class="var">specifier</var> is set to <code class="code">auto</code>, the reference is chosen
automatically. Otherwise it must be a stream specifier (see the &ldquo;Stream
specifiers&rdquo; chapter in the ffmpeg manual) which specifies the
reference stream. The default value is <code class="code">auto</code>.
</p>
</dd>
<dt><samp class="option">segment_format <var class="var">format</var></samp></dt>
<dd><p>Override the inner container format, by default it is guessed by the filename
extension.
</p>
</dd>
<dt><samp class="option">segment_format_options <var class="var">options_list</var></samp></dt>
<dd><p>Set output format options using a :-separated list of key=value
parameters. Values containing the <code class="code">:</code> special character must be
escaped.
</p>
</dd>
<dt><samp class="option">segment_list <var class="var">name</var></samp></dt>
<dd><p>Generate also a listfile named <var class="var">name</var>. If not specified no
listfile is generated.
</p>
</dd>
<dt><samp class="option">segment_list_flags <var class="var">flags</var></samp></dt>
<dd><p>Set flags affecting the segment list generation.
</p>
<p>It currently supports the following flags:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">cache</samp>&rsquo;</dt>
<dd><p>Allow caching (only affects M3U8 list files).
</p>
</dd>
<dt>&lsquo;<samp class="samp">live</samp>&rsquo;</dt>
<dd><p>Allow live-friendly file generation.
</p></dd>
</dl>

</dd>
<dt><samp class="option">segment_list_size <var class="var">size</var></samp></dt>
<dd><p>Update the list file so that it contains at most <var class="var">size</var>
segments. If 0 the list file will contain all the segments. Default
value is 0.
</p>
</dd>
<dt><samp class="option">segment_list_entry_prefix <var class="var">prefix</var></samp></dt>
<dd><p>Prepend <var class="var">prefix</var> to each entry. Useful to generate absolute paths.
By default no prefix is applied.
</p>
</dd>
<dt><samp class="option">segment_list_type <var class="var">type</var></samp></dt>
<dd><p>Select the listing format.
</p>
<p>The following values are recognized:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">flat</samp>&rsquo;</dt>
<dd><p>Generate a flat list for the created segments, one segment per line.
</p>
</dd>
<dt>&lsquo;<samp class="samp">csv, ext</samp>&rsquo;</dt>
<dd><p>Generate a list for the created segments, one segment per line,
each line matching the format (comma-separated values):
</p><div class="example">
<pre class="example-preformatted"><var class="var">segment_filename</var>,<var class="var">segment_start_time</var>,<var class="var">segment_end_time</var>
</pre></div>

<p><var class="var">segment_filename</var> is the name of the output file generated by the
muxer according to the provided pattern. CSV escaping (according to
RFC4180) is applied if required.
</p>
<p><var class="var">segment_start_time</var> and <var class="var">segment_end_time</var> specify
the segment start and end time expressed in seconds.
</p>
<p>A list file with the suffix <code class="code">&quot;.csv&quot;</code> or <code class="code">&quot;.ext&quot;</code> will
auto-select this format.
</p>
<p>&lsquo;<samp class="samp">ext</samp>&rsquo; is deprecated in favor or &lsquo;<samp class="samp">csv</samp>&rsquo;.
</p>
</dd>
<dt>&lsquo;<samp class="samp">ffconcat</samp>&rsquo;</dt>
<dd><p>Generate an ffconcat file for the created segments. The resulting file
can be read using the FFmpeg <a class="ref" href="#concat">concat</a> demuxer.
</p>
<p>A list file with the suffix <code class="code">&quot;.ffcat&quot;</code> or <code class="code">&quot;.ffconcat&quot;</code> will
auto-select this format.
</p>
</dd>
<dt>&lsquo;<samp class="samp">m3u8</samp>&rsquo;</dt>
<dd><p>Generate an extended M3U8 file, version 3, compliant with
<a class="url" href="http://tools.ietf.org/id/draft-pantos-http-live-streaming">http://tools.ietf.org/id/draft-pantos-http-live-streaming</a>.
</p>
<p>A list file with the suffix <code class="code">&quot;.m3u8&quot;</code> will auto-select this format.
</p></dd>
</dl>

<p>If not specified the type is guessed from the list file name suffix.
</p>
</dd>
<dt><samp class="option">segment_time <var class="var">time</var></samp></dt>
<dd><p>Set segment duration to <var class="var">time</var>, the value must be a duration
specification. Default value is &quot;2&quot;. See also the
<samp class="option">segment_times</samp> option.
</p>
<p>Note that splitting may not be accurate, unless you force the
reference stream key-frames at the given time. See the introductory
notice and the examples below.
</p>
</dd>
<dt><samp class="option">min_seg_duration <var class="var">time</var></samp></dt>
<dd><p>Set minimum segment duration to <var class="var">time</var>, the value must be a duration
specification. This prevents the muxer ending segments at a duration below
this value. Only effective with <code class="code">segment_time</code>. Default value is &quot;0&quot;.
</p>
</dd>
<dt><samp class="option">segment_atclocktime <var class="var">1|0</var></samp></dt>
<dd><p>If set to &quot;1&quot; split at regular clock time intervals starting from 00:00
o&rsquo;clock. The <var class="var">time</var> value specified in <samp class="option">segment_time</samp> is
used for setting the length of the splitting interval.
</p>
<p>For example with <samp class="option">segment_time</samp> set to &quot;900&quot; this makes it possible
to create files at 12:00 o&rsquo;clock, 12:15, 12:30, etc.
</p>
<p>Default value is &quot;0&quot;.
</p>
</dd>
<dt><samp class="option">segment_clocktime_offset <var class="var">duration</var></samp></dt>
<dd><p>Delay the segment splitting times with the specified duration when using
<samp class="option">segment_atclocktime</samp>.
</p>
<p>For example with <samp class="option">segment_time</samp> set to &quot;900&quot; and
<samp class="option">segment_clocktime_offset</samp> set to &quot;300&quot; this makes it possible to
create files at 12:05, 12:20, 12:35, etc.
</p>
<p>Default value is &quot;0&quot;.
</p>
</dd>
<dt><samp class="option">segment_clocktime_wrap_duration <var class="var">duration</var></samp></dt>
<dd><p>Force the segmenter to only start a new segment if a packet reaches the muxer
within the specified duration after the segmenting clock time. This way you
can make the segmenter more resilient to backward local time jumps, such as
leap seconds or transition to standard time from daylight savings time.
</p>
<p>Default is the maximum possible duration which means starting a new segment
regardless of the elapsed time since the last clock time.
</p>
</dd>
<dt><samp class="option">segment_time_delta <var class="var">delta</var></samp></dt>
<dd><p>Specify the accuracy time when selecting the start time for a
segment, expressed as a duration specification. Default value is &quot;0&quot;.
</p>
<p>When delta is specified a key-frame will start a new segment if its
PTS satisfies the relation:
</p><div class="example">
<pre class="example-preformatted">PTS &gt;= start_time - time_delta
</pre></div>

<p>This option is useful when splitting video content, which is always
split at GOP boundaries, in case a key frame is found just before the
specified split time.
</p>
<p>In particular may be used in combination with the <samp class="file">ffmpeg</samp> option
<var class="var">force_key_frames</var>. The key frame times specified by
<var class="var">force_key_frames</var> may not be set accurately because of rounding
issues, with the consequence that a key frame time may result set just
before the specified time. For constant frame rate videos a value of
1/(2*<var class="var">frame_rate</var>) should address the worst case mismatch between
the specified time and the time set by <var class="var">force_key_frames</var>.
</p>
</dd>
<dt><samp class="option">segment_times <var class="var">times</var></samp></dt>
<dd><p>Specify a list of split points. <var class="var">times</var> contains a list of comma
separated duration specifications, in increasing order. See also
the <samp class="option">segment_time</samp> option.
</p>
</dd>
<dt><samp class="option">segment_frames <var class="var">frames</var></samp></dt>
<dd><p>Specify a list of split video frame numbers. <var class="var">frames</var> contains a
list of comma separated integer numbers, in increasing order.
</p>
<p>This option specifies to start a new segment whenever a reference
stream key frame is found and the sequential number (starting from 0)
of the frame is greater or equal to the next value in the list.
</p>
</dd>
<dt><samp class="option">segment_wrap <var class="var">limit</var></samp></dt>
<dd><p>Wrap around segment index once it reaches <var class="var">limit</var>.
</p>
</dd>
<dt><samp class="option">segment_start_number <var class="var">number</var></samp></dt>
<dd><p>Set the sequence number of the first segment. Defaults to <code class="code">0</code>.
</p>
</dd>
<dt><samp class="option">strftime <var class="var">1|0</var></samp></dt>
<dd><p>Use the <code class="code">strftime</code> function to define the name of the new
segments to write. If this is selected, the output segment name must
contain a <code class="code">strftime</code> function template. Default value is
<code class="code">0</code>.
</p>
</dd>
<dt><samp class="option">break_non_keyframes <var class="var">1|0</var></samp></dt>
<dd><p>If enabled, allow segments to start on frames other than keyframes. This
improves behavior on some players when the time between keyframes is
inconsistent, but may make things worse on others, and can cause some oddities
during seeking. Defaults to <code class="code">0</code>.
</p>
</dd>
<dt><samp class="option">reset_timestamps <var class="var">1|0</var></samp></dt>
<dd><p>Reset timestamps at the beginning of each segment, so that each segment
will start with near-zero timestamps. It is meant to ease the playback
of the generated segments. May not work with some combinations of
muxers/codecs. It is set to <code class="code">0</code> by default.
</p>
</dd>
<dt><samp class="option">initial_offset <var class="var">offset</var></samp></dt>
<dd><p>Specify timestamp offset to apply to the output packet timestamps. The
argument must be a time duration specification, and defaults to 0.
</p>
</dd>
<dt><samp class="option">write_empty_segments <var class="var">1|0</var></samp></dt>
<dd><p>If enabled, write an empty segment if there are no packets during the period a
segment would usually span. Otherwise, the segment will be filled with the next
packet written. Defaults to <code class="code">0</code>.
</p></dd>
</dl>

<p>Make sure to require a closed GOP when encoding and to set the GOP
size to fit your segment time constraint.
</p>
<a name="Examples-15"></a>
<h4 class="subsection">4.68.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-15" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-15" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Remux the content of file <samp class="file">in.mkv</samp> to a list of segments
<samp class="file">out-000.nut</samp>, <samp class="file">out-001.nut</samp>, etc., and write the list of
generated segments to <samp class="file">out.list</samp>:
<div class="example">
<pre class="example-preformatted">ffmpeg -i in.mkv -codec hevc -flags +cgop -g 60 -map 0 -f segment -segment_list out.list out%03d.nut
</pre></div>

</li><li>Segment input and set output format options for the output segments:
<div class="example">
<pre class="example-preformatted">ffmpeg -i in.mkv -f segment -segment_time 10 -segment_format_options movflags=+faststart out%03d.mp4
</pre></div>

</li><li>Segment the input file according to the split points specified by the
<var class="var">segment_times</var> option:
<div class="example">
<pre class="example-preformatted">ffmpeg -i in.mkv -codec copy -map 0 -f segment -segment_list out.csv -segment_times 1,2,3,5,8,13,21 out%03d.nut
</pre></div>

</li><li>Use the <code class="command">ffmpeg</code> <samp class="option">force_key_frames</samp>
option to force key frames in the input at the specified location, together
with the segment option <samp class="option">segment_time_delta</samp> to account for
possible roundings operated when setting key frame times.
<div class="example">
<pre class="example-preformatted">ffmpeg -i in.mkv -force_key_frames 1,2,3,5,8,13,21 -codec:v mpeg4 -codec:a pcm_s16le -map 0 \
-f segment -segment_list out.csv -segment_times 1,2,3,5,8,13,21 -segment_time_delta 0.05 out%03d.nut
</pre></div>
<p>In order to force key frames on the input file, transcoding is
required.
</p>
</li><li>Segment the input file by splitting the input file according to the
frame numbers sequence specified with the <samp class="option">segment_frames</samp> option:
<div class="example">
<pre class="example-preformatted">ffmpeg -i in.mkv -codec copy -map 0 -f segment -segment_list out.csv -segment_frames 100,200,300,500,800 out%03d.nut
</pre></div>

</li><li>Convert the <samp class="file">in.mkv</samp> to TS segments using the <code class="code">libx264</code>
and <code class="code">aac</code> encoders:
<div class="example">
<pre class="example-preformatted">ffmpeg -i in.mkv -map 0 -codec:v libx264 -codec:a aac -f ssegment -segment_list out.list out%03d.ts
</pre></div>

</li><li>Segment the input file, and create an M3U8 live playlist (can be used
as live HLS source):
<div class="example">
<pre class="example-preformatted">ffmpeg -re -i in.mkv -codec copy -map 0 -f segment -segment_list playlist.m3u8 \
-segment_list_flags +live -segment_time 10 out%03d.mkv
</pre></div>
</li></ul>

<a name="smoothstreaming"></a>
<h3 class="section">4.69 smoothstreaming<span class="pull-right"><a class="anchor hidden-xs" href="#smoothstreaming" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-smoothstreaming" aria-hidden="true">TOC</a></span></h3>

<p>Smooth Streaming muxer generates a set of files (Manifest, chunks) suitable for serving with conventional web server.
</p>
<dl class="table">
<dt><samp class="option">window_size</samp></dt>
<dd><p>Specify the number of fragments kept in the manifest. Default 0 (keep all).
</p>
</dd>
<dt><samp class="option">extra_window_size</samp></dt>
<dd><p>Specify the number of fragments kept outside of the manifest before removing from disk. Default 5.
</p>
</dd>
<dt><samp class="option">lookahead_count</samp></dt>
<dd><p>Specify the number of lookahead fragments. Default 2.
</p>
</dd>
<dt><samp class="option">min_frag_duration</samp></dt>
<dd><p>Specify the minimum fragment duration (in microseconds). Default 5000000.
</p>
</dd>
<dt><samp class="option">remove_at_exit</samp></dt>
<dd><p>Specify whether to remove all fragments when finished. Default 0 (do not remove).
</p>
</dd>
</dl>

<a class="anchor" id="streamhash"></a><a name="streamhash-1"></a>
<h3 class="section">4.70 streamhash<span class="pull-right"><a class="anchor hidden-xs" href="#streamhash" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-streamhash" aria-hidden="true">TOC</a></span></h3>

<p>Per stream hash testing format.
</p>
<p>This muxer computes and prints a cryptographic hash of all the input frames,
on a per-stream basis. This can be used for equality checks without having
to do a complete binary comparison.
</p>
<p>By default audio frames are converted to signed 16-bit raw audio and
video frames to raw video before computing the hash, but the output
of explicit conversions to other codecs can also be used. Timestamps
are ignored. It uses the SHA-256 cryptographic hash function by default,
but supports several other algorithms.
</p>
<p>The output of the muxer consists of one line per stream of the form:
<var class="var">streamindex</var>,<var class="var">streamtype</var>,<var class="var">algo</var>=<var class="var">hash</var>, where
<var class="var">streamindex</var> is the index of the mapped stream, <var class="var">streamtype</var> is a
single character indicating the type of stream, <var class="var">algo</var> is a short string
representing the hash function used, and <var class="var">hash</var> is a hexadecimal number
representing the computed hash.
</p>
<dl class="table">
<dt><samp class="option">hash <var class="var">algorithm</var></samp></dt>
<dd><p>Use the cryptographic hash function specified by the string <var class="var">algorithm</var>.
Supported values include <code class="code">MD5</code>, <code class="code">murmur3</code>, <code class="code">RIPEMD128</code>,
<code class="code">RIPEMD160</code>, <code class="code">RIPEMD256</code>, <code class="code">RIPEMD320</code>, <code class="code">SHA160</code>,
<code class="code">SHA224</code>, <code class="code">SHA256</code> (default), <code class="code">SHA512/224</code>, <code class="code">SHA512/256</code>,
<code class="code">SHA384</code>, <code class="code">SHA512</code>, <code class="code">CRC32</code> and <code class="code">adler32</code>.
</p>
</dd>
</dl>

<a name="Examples-16"></a>
<h4 class="subsection">4.70.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-16" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-16" aria-hidden="true">TOC</a></span></h4>

<p>To compute the SHA-256 hash of the input converted to raw audio and
video, and store it in the file <samp class="file">out.sha256</samp>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f streamhash out.sha256
</pre></div>

<p>To print an MD5 hash to stdout use the command:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f streamhash -hash md5 -
</pre></div>

<p>See also the <a class="ref" href="#hash">hash</a> and <a class="ref" href="#framehash">framehash</a> muxers.
</p>
<a class="anchor" id="tee"></a><a name="tee-1"></a>
<h3 class="section">4.71 tee<span class="pull-right"><a class="anchor hidden-xs" href="#tee" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-tee" aria-hidden="true">TOC</a></span></h3>

<p>The tee muxer can be used to write the same data to several outputs, such as files or streams.
It can be used, for example, to stream a video over a network and save it to disk at the same time.
</p>
<p>It is different from specifying several outputs to the <code class="command">ffmpeg</code>
command-line tool. With the tee muxer, the audio and video data will be encoded only once.
With conventional multiple outputs, multiple encoding operations in parallel are initiated,
which can be a very expensive process. The tee muxer is not useful when using the libavformat API
directly because it is then possible to feed the same packets to several muxers directly.
</p>
<p>Since the tee muxer does not represent any particular output format, ffmpeg cannot auto-select
output streams. So all streams intended for output must be specified using <code class="code">-map</code>. See
the examples below.
</p>
<p>Some encoders may need different options depending on the output format;
the auto-detection of this can not work with the tee muxer, so they need to be explicitly specified.
The main example is the <samp class="option">global_header</samp> flag.
</p>
<p>The slave outputs are specified in the file name given to the muxer,
separated by &rsquo;|&rsquo;. If any of the slave name contains the &rsquo;|&rsquo; separator,
leading or trailing spaces or any special character, those must be
escaped (see <a data-manual="ffmpeg-utils" href="./ffmpeg-utils.html#quoting_005fand_005fescaping">the &quot;Quoting and escaping&quot;
section in the ffmpeg-utils(1) manual</a>).
</p>
<a name="Options-32"></a>
<h4 class="subsection">4.71.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-32" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-32" aria-hidden="true">TOC</a></span></h4>

<dl class="table">
<dt><samp class="option">use_fifo <var class="var">bool</var></samp></dt>
<dd><p>If set to 1, slave outputs will be processed in separate threads using the <a class="ref" href="#fifo">fifo</a>
muxer. This allows to compensate for different speed/latency/reliability of
outputs and setup transparent recovery. By default this feature is turned off.
</p>
</dd>
<dt><samp class="option">fifo_options</samp></dt>
<dd><p>Options to pass to fifo pseudo-muxer instances. See <a class="ref" href="#fifo">fifo</a>.
</p>
</dd>
</dl>

<p>Muxer options can be specified for each slave by prepending them as a list of
<var class="var">key</var>=<var class="var">value</var> pairs separated by &rsquo;:&rsquo;, between square brackets. If
the options values contain a special character or the &rsquo;:&rsquo; separator, they
must be escaped; note that this is a second level escaping.
</p>
<p>The following special options are also recognized:
</p><dl class="table">
<dt><samp class="option">f</samp></dt>
<dd><p>Specify the format name. Required if it cannot be guessed from the
output URL.
</p>
</dd>
<dt><samp class="option">bsfs[/<var class="var">spec</var>]</samp></dt>
<dd><p>Specify a list of bitstream filters to apply to the specified
output.
</p>
<p>It is possible to specify to which streams a given bitstream filter
applies, by appending a stream specifier to the option separated by
<code class="code">/</code>. <var class="var">spec</var> must be a stream specifier (see <a class="ref" href="#Format-stream-specifiers">Format stream specifiers</a>).
</p>
<p>If the stream specifier is not specified, the bitstream filters will be
applied to all streams in the output. This will cause that output operation
to fail if the output contains streams to which the bitstream filter cannot
be applied e.g. <code class="code">h264_mp4toannexb</code> being applied to an output containing an audio stream.
</p>
<p>Options for a bitstream filter must be specified in the form of <code class="code">opt=value</code>.
</p>
<p>Several bitstream filters can be specified, separated by &quot;,&quot;.
</p>
</dd>
<dt><samp class="option">use_fifo <var class="var">bool</var></samp></dt>
<dd><p>This allows to override tee muxer use_fifo option for individual slave muxer.
</p>
</dd>
<dt><samp class="option">fifo_options</samp></dt>
<dd><p>This allows to override tee muxer fifo_options for individual slave muxer.
See <a class="ref" href="#fifo">fifo</a>.
</p>
</dd>
<dt><samp class="option">select</samp></dt>
<dd><p>Select the streams that should be mapped to the slave output,
specified by a stream specifier. If not specified, this defaults to
all the mapped streams. This will cause that output operation to fail
if the output format does not accept all mapped streams.
</p>
<p>You may use multiple stream specifiers separated by commas (<code class="code">,</code>) e.g.: <code class="code">a:0,v</code>
</p>
</dd>
<dt><samp class="option">onfail</samp></dt>
<dd><p>Specify behaviour on output failure. This can be set to either <code class="code">abort</code> (which is
default) or <code class="code">ignore</code>. <code class="code">abort</code> will cause whole process to fail in case of failure
on this slave output. <code class="code">ignore</code> will ignore failure on this output, so other outputs
will continue without being affected.
</p></dd>
</dl>

<a name="Examples-17"></a>
<h4 class="subsection">4.71.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-17" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-17" aria-hidden="true">TOC</a></span></h4>

<ul class="itemize mark-bullet">
<li>Encode something and both archive it in a WebM file and stream it
as MPEG-TS over UDP:
<div class="example">
<pre class="example-preformatted">ffmpeg -i ... -c:v libx264 -c:a mp2 -f tee -map 0:v -map 0:a
  &quot;archive-20121107.mkv|[f=mpegts]udp://10.0.1.255:1234/&quot;
</pre></div>

</li><li>As above, but continue streaming even if output to local file fails
(for example local drive fills up):
<div class="example">
<pre class="example-preformatted">ffmpeg -i ... -c:v libx264 -c:a mp2 -f tee -map 0:v -map 0:a
  &quot;[onfail=ignore]archive-20121107.mkv|[f=mpegts]udp://10.0.1.255:1234/&quot;
</pre></div>

</li><li>Use <code class="command">ffmpeg</code> to encode the input, and send the output
to three different destinations. The <code class="code">dump_extra</code> bitstream
filter is used to add extradata information to all the output video
keyframes packets, as requested by the MPEG-TS format. The select
option is applied to <samp class="file">out.aac</samp> in order to make it contain only
audio packets.
<div class="example">
<pre class="example-preformatted">ffmpeg -i ... -map 0 -flags +global_header -c:v libx264 -c:a aac
       -f tee &quot;[bsfs/v=dump_extra=freq=keyframe]out.ts|[movflags=+faststart]out.mp4|[select=a]out.aac&quot;
</pre></div>

</li><li>As above, but select only stream <code class="code">a:1</code> for the audio output. Note
that a second level escaping must be performed, as &quot;:&quot; is a special
character used to separate options.
<div class="example">
<pre class="example-preformatted">ffmpeg -i ... -map 0 -flags +global_header -c:v libx264 -c:a aac
       -f tee &quot;[bsfs/v=dump_extra=freq=keyframe]out.ts|[movflags=+faststart]out.mp4|[select=\'a:1\']out.aac&quot;
</pre></div>
</li></ul>

<a name="webm_005fchunk"></a>
<h3 class="section">4.72 webm_chunk<span class="pull-right"><a class="anchor hidden-xs" href="#webm_005fchunk" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-webm_005fchunk" aria-hidden="true">TOC</a></span></h3>

<p>WebM Live Chunk Muxer.
</p>
<p>This muxer writes out WebM headers and chunks as separate files which can be
consumed by clients that support WebM Live streams via DASH.
</p>
<a name="Options-33"></a>
<h4 class="subsection">4.72.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-33" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-33" aria-hidden="true">TOC</a></span></h4>

<p>This muxer supports the following options:
</p>
<dl class="table">
<dt><samp class="option">chunk_start_index</samp></dt>
<dd><p>Index of the first chunk (defaults to 0).
</p>
</dd>
<dt><samp class="option">header</samp></dt>
<dd><p>Filename of the header where the initialization data will be written.
</p>
</dd>
<dt><samp class="option">audio_chunk_duration</samp></dt>
<dd><p>Duration of each audio chunk in milliseconds (defaults to 5000).
</p></dd>
</dl>

<a name="Example-9"></a>
<h4 class="subsection">4.72.2 Example<span class="pull-right"><a class="anchor hidden-xs" href="#Example-9" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Example-9" aria-hidden="true">TOC</a></span></h4>
<div class="example">
<pre class="example-preformatted">ffmpeg -f v4l2 -i /dev/video0 \
       -f alsa -i hw:0 \
       -map 0:0 \
       -c:v libvpx-vp9 \
       -s 640x360 -keyint_min 30 -g 30 \
       -f webm_chunk \
       -header webm_live_video_360.hdr \
       -chunk_start_index 1 \
       webm_live_video_360_%d.chk \
       -map 1:0 \
       -c:a libvorbis \
       -b:a 128k \
       -f webm_chunk \
       -header webm_live_audio_128.hdr \
       -chunk_start_index 1 \
       -audio_chunk_duration 1000 \
       webm_live_audio_128_%d.chk
</pre></div>

<a name="webm_005fdash_005fmanifest"></a>
<h3 class="section">4.73 webm_dash_manifest<span class="pull-right"><a class="anchor hidden-xs" href="#webm_005fdash_005fmanifest" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-webm_005fdash_005fmanifest" aria-hidden="true">TOC</a></span></h3>

<p>WebM DASH Manifest muxer.
</p>
<p>This muxer implements the WebM DASH Manifest specification to generate the DASH
manifest XML. It also supports manifest generation for DASH live streams.
</p>
<p>For more information see:
</p>
<ul class="itemize mark-bullet">
<li>WebM DASH Specification: <a class="url" href="https://sites.google.com/a/webmproject.org/wiki/adaptive-streaming/webm-dash-specification">https://sites.google.com/a/webmproject.org/wiki/adaptive-streaming/webm-dash-specification</a>
</li><li>ISO DASH Specification: <a class="url" href="http://standards.iso.org/ittf/PubliclyAvailableStandards/c065274_ISO_IEC_23009-1_2014.zip">http://standards.iso.org/ittf/PubliclyAvailableStandards/c065274_ISO_IEC_23009-1_2014.zip</a>
</li></ul>

<a name="Options-34"></a>
<h4 class="subsection">4.73.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-34" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-34" aria-hidden="true">TOC</a></span></h4>

<p>This muxer supports the following options:
</p>
<dl class="table">
<dt><samp class="option">adaptation_sets</samp></dt>
<dd><p>This option has the following syntax: &quot;id=x,streams=a,b,c id=y,streams=d,e&quot; where x and y are the
unique identifiers of the adaptation sets and a,b,c,d and e are the indices of the corresponding
audio and video streams. Any number of adaptation sets can be added using this option.
</p>
</dd>
<dt><samp class="option">live</samp></dt>
<dd><p>Set this to 1 to create a live stream DASH Manifest. Default: 0.
</p>
</dd>
<dt><samp class="option">chunk_start_index</samp></dt>
<dd><p>Start index of the first chunk. This will go in the &lsquo;<samp class="samp">startNumber</samp>&rsquo; attribute
of the &lsquo;<samp class="samp">SegmentTemplate</samp>&rsquo; element in the manifest. Default: 0.
</p>
</dd>
<dt><samp class="option">chunk_duration_ms</samp></dt>
<dd><p>Duration of each chunk in milliseconds. This will go in the &lsquo;<samp class="samp">duration</samp>&rsquo;
attribute of the &lsquo;<samp class="samp">SegmentTemplate</samp>&rsquo; element in the manifest. Default: 1000.
</p>
</dd>
<dt><samp class="option">utc_timing_url</samp></dt>
<dd><p>URL of the page that will return the UTC timestamp in ISO format. This will go
in the &lsquo;<samp class="samp">value</samp>&rsquo; attribute of the &lsquo;<samp class="samp">UTCTiming</samp>&rsquo; element in the manifest.
Default: None.
</p>
</dd>
<dt><samp class="option">time_shift_buffer_depth</samp></dt>
<dd><p>Smallest time (in seconds) shifting buffer for which any Representation is
guaranteed to be available. This will go in the &lsquo;<samp class="samp">timeShiftBufferDepth</samp>&rsquo;
attribute of the &lsquo;<samp class="samp">MPD</samp>&rsquo; element. Default: 60.
</p>
</dd>
<dt><samp class="option">minimum_update_period</samp></dt>
<dd><p>Minimum update period (in seconds) of the manifest. This will go in the
&lsquo;<samp class="samp">minimumUpdatePeriod</samp>&rsquo; attribute of the &lsquo;<samp class="samp">MPD</samp>&rsquo; element. Default: 0.
</p>
</dd>
</dl>

<a name="Example-10"></a>
<h4 class="subsection">4.73.2 Example<span class="pull-right"><a class="anchor hidden-xs" href="#Example-10" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Example-10" aria-hidden="true">TOC</a></span></h4>
<div class="example">
<pre class="example-preformatted">ffmpeg -f webm_dash_manifest -i video1.webm \
       -f webm_dash_manifest -i video2.webm \
       -f webm_dash_manifest -i audio1.webm \
       -f webm_dash_manifest -i audio2.webm \
       -map 0 -map 1 -map 2 -map 3 \
       -c copy \
       -f webm_dash_manifest \
       -adaptation_sets &quot;id=0,streams=0,1 id=1,streams=2,3&quot; \
       manifest.xml
</pre></div>

<a class="anchor" id="whip"></a><a name="whip-1"></a>
<h3 class="section">4.74 whip<span class="pull-right"><a class="anchor hidden-xs" href="#whip" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-whip" aria-hidden="true">TOC</a></span></h3>

<p>WebRTC (Real-Time Communication) muxer that supports sub-second latency streaming according to
the WHIP (WebRTC-HTTP ingestion protocol) specification.
</p>
<p>This is an experimental feature.
</p>
<p>It uses HTTP as a signaling protocol to exchange SDP capabilities and ICE lite candidates. Then,
it uses STUN binding requests and responses to establish a session over UDP. Subsequently, it
initiates a DTLS handshake to exchange the SRTP encryption keys. Lastly, it splits video and
audio frames into RTP packets and encrypts them using SRTP.
</p>
<p>Ensure that you use H.264 without B frames and Opus for the audio codec. For example, to convert
an input file with <code class="command">ffmpeg</code> to WebRTC:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -re -i input.mp4 -acodec libopus -ar 48000 -ac 2 \
  -vcodec libx264 -profile:v baseline -tune zerolatency -threads 1 -bf 0 \
  -f whip &quot;http://localhost:1985/rtc/v1/whip/?app=live&amp;stream=livestream&quot;
</pre></div>

<p>For this example, we have employed low latency options, resulting in an end-to-end latency of
approximately 150ms.
</p>
<a name="Options-35"></a>
<h4 class="subsection">4.74.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-35" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-35" aria-hidden="true">TOC</a></span></h4>

<p>This muxer supports the following options:
</p>
<dl class="table">
<dt><samp class="option">handshake_timeout <var class="var">integer</var></samp></dt>
<dd><p>Set the timeout in milliseconds for ICE and DTLS handshake.
Default value is 5000.
</p>
</dd>
<dt><samp class="option">pkt_size <var class="var">integer</var></samp></dt>
<dd><p>Set the maximum size, in bytes, of RTP packets that send out.
Default value is 1500.
</p>
</dd>
<dt><samp class="option">authorization <var class="var">string</var></samp></dt>
<dd><p>The optional Bearer token for WHIP Authorization.
</p>
</dd>
<dt><samp class="option">cert_file <var class="var">string</var></samp></dt>
<dd><p>The optional certificate file path for DTLS.
</p>
</dd>
<dt><samp class="option">key_file <var class="var">string</var></samp></dt>
<dd><p>The optional private key file path for DTLS.
</p>
</dd>
</dl>

<a class="anchor" id="metadata"></a><a name="Metadata-2"></a>
<h2 class="chapter">5 Metadata<span class="pull-right"><a class="anchor hidden-xs" href="#Metadata-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Metadata-2" aria-hidden="true">TOC</a></span></h2>

<p>FFmpeg is able to dump metadata from media files into a simple UTF-8-encoded
INI-like text file and then load it back using the metadata muxer/demuxer.
</p>
<p>The file format is as follows:
</p><ol class="enumerate">
<li> A file consists of a header and a number of metadata tags divided into sections,
each on its own line.

</li><li> The header is a &lsquo;<samp class="samp">;FFMETADATA</samp>&rsquo; string, followed by a version number (now 1).

</li><li> Metadata tags are of the form &lsquo;<samp class="samp">key=value</samp>&rsquo;

</li><li> Immediately after header follows global metadata

</li><li> After global metadata there may be sections with per-stream/per-chapter
metadata.

</li><li> A section starts with the section name in uppercase (i.e. STREAM or CHAPTER) in
brackets (&lsquo;<samp class="samp">[</samp>&rsquo;, &lsquo;<samp class="samp">]</samp>&rsquo;) and ends with next section or end of file.

</li><li> At the beginning of a chapter section there may be an optional timebase to be
used for start/end values. It must be in form
&lsquo;<samp class="samp">TIMEBASE=<var class="var">num</var>/<var class="var">den</var></samp>&rsquo;, where <var class="var">num</var> and <var class="var">den</var> are
integers. If the timebase is missing then start/end times are assumed to
be in nanoseconds.

<p>Next a chapter section must contain chapter start and end times in form
&lsquo;<samp class="samp">START=<var class="var">num</var></samp>&rsquo;, &lsquo;<samp class="samp">END=<var class="var">num</var></samp>&rsquo;, where <var class="var">num</var> is a positive
integer.
</p>
</li><li> Empty lines and lines starting with &lsquo;<samp class="samp">;</samp>&rsquo; or &lsquo;<samp class="samp">#</samp>&rsquo; are ignored.

</li><li> Metadata keys or values containing special characters (&lsquo;<samp class="samp">=</samp>&rsquo;, &lsquo;<samp class="samp">;</samp>&rsquo;,
&lsquo;<samp class="samp">#</samp>&rsquo;, &lsquo;<samp class="samp">\</samp>&rsquo; and a newline) must be escaped with a backslash &lsquo;<samp class="samp">\</samp>&rsquo;.

</li><li> Note that whitespace in metadata (e.g. &lsquo;<samp class="samp">foo = bar</samp>&rsquo;) is considered to be
a part of the tag (in the example above key is &lsquo;<samp class="samp">foo </samp>&rsquo;, value is
&lsquo;<samp class="samp"> bar</samp>&rsquo;).
</li></ol>

<p>A ffmetadata file might look like this:
</p><div class="example">
<pre class="example-preformatted">;FFMETADATA1
title=bike\\shed
;this is a comment
artist=FFmpeg troll team

[CHAPTER]
TIMEBASE=1/1000
START=0
#chapter ends at 0:01:00
END=60000
title=chapter \#1
[STREAM]
title=multi\
line
</pre></div>

<p>By using the ffmetadata muxer and demuxer it is possible to extract
metadata from an input file to an ffmetadata file, and then transcode
the file into an output file with the edited ffmetadata file.
</p>
<p>Extracting an ffmetadata file with <samp class="file">ffmpeg</samp> goes as follows:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -f ffmetadata FFMETADATAFILE
</pre></div>

<p>Reinserting edited metadata information from the FFMETADATAFILE file can
be done as:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -i FFMETADATAFILE -map_metadata 1 -codec copy OUTPUT
</pre></div>


<a name="See-Also"></a>
<h2 class="chapter">6 See Also<span class="pull-right"><a class="anchor hidden-xs" href="#See-Also" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-See-Also" aria-hidden="true">TOC</a></span></h2>

<p><a class="url" href="ffmpeg.html">ffmpeg</a>, <a class="url" href="ffplay.html">ffplay</a>, <a class="url" href="ffprobe.html">ffprobe</a>,
<a class="url" href="libavformat.html">libavformat</a>
</p>

<a name="Authors"></a>
<h2 class="chapter">7 Authors<span class="pull-right"><a class="anchor hidden-xs" href="#Authors" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Authors" aria-hidden="true">TOC</a></span></h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
<code class="command">git log</code> in the FFmpeg source directory, or browsing the
online repository at <a class="url" href="https://git.ffmpeg.org/ffmpeg">https://git.ffmpeg.org/ffmpeg</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp class="file">MAINTAINERS</samp> in the source code tree.
</p>

      <p style="font-size: small;">
        This document was generated using <a class="uref" href="https://www.gnu.org/software/texinfo/"><em class="emph">makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
