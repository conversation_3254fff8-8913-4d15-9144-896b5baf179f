# PortAudio

PortAudio is a free, cross-platform, open-source, audio I/O library.  It lets you write simple audio programs in 'C' or C++ that will compile and run on many platforms including Windows, Macintosh OS X, and Unix (OSS/ALSA).

This repo provides `prebuilt libraries` and `include files` to add port audio instantly to your project.

Add it to your project with:
````bash
git submodule add https://github.com/shivang51/portaudio external/portaudio 
````
To get started with portaudio you can read these [PortAudio Tutorials](http://www.portaudio.com/docs/v19-doxydocs/tutorial_start.html).
