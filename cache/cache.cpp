// Copyright 2025 <Copyright Owner>

#include "cache.h"
#include "../common/lock_ordering.h"
#include "../debug/vector_debug.h"
#include "../debug/vector_debug.h"

#include <algorithm>
#include <chrono>
#include <cstring>
#include <fstream>
#include <stdexcept>

#include <fmt/core.h>
#include <fmt/format.h>
#include <spdlog/spdlog.h>

namespace x86_64 {
struct CacheException : std::runtime_error {
  explicit CacheException(const std::string &msg) : std::runtime_error(msg) {}
};

Cache::Cache(const Config &config, LowerMemory lowerMemory)
    : m_config(config), m_lowerMemory(lowerMemory) {
  ValidateConfig();
  InitializeCache();
  spdlog::info(fmt::format(
      "Cache {} initialized: size={}B, lineSize={}B, associativity={}",
      m_config.name, m_config.size, m_config.lineSize, m_config.associativity));
}

Cache::Cache(size_t sizeInBytes) {
  m_config.size = sizeInBytes;
  m_config.lineSize = 64;
  m_config.associativity = 4;
  m_config.writeBack = true;
  m_config.latencyCycles = 4;
  m_config.prefetch = false;
  m_config.prefetchStride = 1;
  m_config.name = "DefaultCache";
  ValidateConfig();
  InitializeCache();
  spdlog::info(fmt::format(
      "Cache {} initialized: size={}B, lineSize={}B, associativity={}",
      m_config.name, m_config.size, m_config.lineSize, m_config.associativity));
}

void Cache::ValidateConfig() {
  if (m_config.size == 0 || m_config.lineSize == 0 ||
      m_config.associativity == 0) {
    throw CacheException(
        "Cache size, line size, and associativity must be non-zero");
  }
  if (m_config.size % (m_config.lineSize * m_config.associativity) != 0) {
    throw CacheException(
        "Cache size must be divisible by (lineSize * associativity)");
  }
  if (m_config.prefetchStride == 0) {
    throw CacheException("Prefetch stride must be non-zero");
  }
}

void Cache::InitializeCache() {
  MEMORY_LOCK(m_mutex, "CacheMutex");
  m_numSets = m_config.size / (m_config.lineSize * m_config.associativity);
  m_setIndexBits = static_cast<size_t>(std::log2(m_numSets));
  m_tagBits =
      64 - m_setIndexBits - static_cast<size_t>(std::log2(m_config.lineSize));
  m_setMask = (1ULL << m_setIndexBits) - 1;
  m_tagMask = (1ULL << m_tagBits) - 1;

  m_sets.resize(m_numSets);
  for (auto &set : m_sets) {
    set.resize(m_config.associativity);
    for (auto &line : set) {
      line.valid = false;
      line.dirty = false;
      line.tag = 0;
      line.lastUsed = 0;
      line.data.resize(m_config.lineSize);
      line.state = CacheLine::State::Invalid;
    }
  }
  spdlog::trace("Cache initialized: {} sets, tagBits={}, setIndexBits={}",
                m_numSets, m_tagBits, m_setIndexBits);
}

std::pair<size_t, uint64_t> Cache::ExtractSetAndTag(uint64_t address) const {
  size_t offsetBits = static_cast<size_t>(std::log2(m_config.lineSize));
  size_t setIndex = (address >> offsetBits) & m_setMask;
  uint64_t tag = (address >> (offsetBits + m_setIndexBits)) & m_tagMask;
  return {setIndex, tag};
}

std::optional<size_t> Cache::FindLineInSet(size_t setIndex,
                                           uint64_t tag) const {
  // CRITICAL: Bounds check for set access
  if (setIndex >= m_sets.size()) {
    spdlog::error("Cache::FindLineInSet: setIndex {} >= m_sets.size() {}",
                  setIndex, m_sets.size());
    return std::nullopt;
  }

  const auto &set =
      CRITICAL_VECTOR_ACCESS(m_sets, setIndex, "Cache::FindLineInSet");
  for (size_t way = 0; way < m_config.associativity; ++way) {
    // CRITICAL: Bounds check for way access
    if (way >= set.size()) {
      spdlog::error("Cache::FindLineInSet: way {} >= set.size() {}", way,
                    set.size());
      break;
    }

    const auto &line =
        CRITICAL_VECTOR_ACCESS(set, way, "Cache::FindLineInSet way access");
    if (line.valid && line.tag == tag) {
      return way;
    }
  }
  return std::nullopt;
}

size_t Cache::FindLRUInSet(size_t setIndex) const {
  // CRITICAL: Bounds check for set access
  if (setIndex >= m_sets.size()) {
    spdlog::error("Cache::FindLRUInSet: setIndex {} >= m_sets.size() {}",
                  setIndex, m_sets.size());
    return 0; // Return safe default
  }

  const auto &set =
      CRITICAL_VECTOR_ACCESS(m_sets, setIndex, "Cache::FindLRUInSet");
  if (set.empty()) {
    spdlog::error("Cache::FindLRUInSet: set is empty");
    return 0;
  }

  size_t lruWay = 0;
  const auto &firstLine =
      CRITICAL_VECTOR_ACCESS(set, 0, "Cache::FindLRUInSet first line");
  uint64_t oldestTime = firstLine.lastUsed;

  for (size_t way = 1; way < m_config.associativity && way < set.size();
       ++way) {
    const auto &line =
        CRITICAL_VECTOR_ACCESS(set, way, "Cache::FindLRUInSet way access");
    if (line.lastUsed < oldestTime) {
      oldestTime = line.lastUsed;
      lruWay = way;
    }
  }
  return lruWay;
}

void Cache::EvictLine(size_t setIndex, size_t way) {
  // CRITICAL: Bounds check for set and way access
  if (setIndex >= m_sets.size()) {
    spdlog::error("Cache::EvictLine: setIndex {} >= m_sets.size() {}", setIndex,
                  m_sets.size());
    return;
  }

  auto &set =
      CRITICAL_VECTOR_ACCESS(m_sets, setIndex, "Cache::EvictLine set access");
  if (way >= set.size()) {
    spdlog::error("Cache::EvictLine: way {} >= set.size() {}", way, set.size());
    return;
  }

  auto &line = CRITICAL_VECTOR_ACCESS(set, way, "Cache::EvictLine line access");
  
  // CRITICAL: Data buffer size validation
  if (line.data.size() != m_config.lineSize) {
    spdlog::error("Cache::EvictLine: line.data.size() {} != m_config.lineSize {}", line.data.size(), m_config.lineSize);
    throw CacheException("EvictLine: Cache line data buffer size mismatch");
  }

  if (line.valid && line.dirty && m_config.writeBack && m_lowerMemory) {
    uint64_t address =
        (line.tag << (m_setIndexBits +
                      static_cast<size_t>(std::log2(m_config.lineSize)))) |
        (setIndex << static_cast<size_t>(std::log2(m_config.lineSize)));
    try {
      m_lowerMemory(address, line.data.data(), m_config.lineSize, true);
      m_stats.coherence_traffic += m_config.lineSize;
      spdlog::trace("Evicted dirty line at 0x{:x} and wrote back", address);
    } catch (const std::exception &e) {
      spdlog::error("Failed to write back cache line at 0x{:x}: {}", address,
                    e.what());
      throw CacheException("Write-back failure");
    }
  } else if (line.valid) {
    spdlog::trace("Evicted clean line at set {}, way {}", setIndex, way);
  }
  line.valid = false;
  line.dirty = false;
  line.state = CacheLine::State::Invalid;
  m_stats.evictions++;
}

void Cache::LoadLine(size_t setIndex, size_t way, uint64_t address,
                     const uint8_t *data) {
  // CRITICAL: Address alignment validation
  if (address & (m_config.lineSize - 1)) {
    spdlog::error("Cache::LoadLine: address 0x{:x} not aligned to line size {}", address, m_config.lineSize);
    throw CacheException("LoadLine: Address not aligned to cache line size");
  }

  // CRITICAL: Bounds check for set and way access
  if (setIndex >= m_sets.size()) {
    spdlog::error("Cache::LoadLine: setIndex {} >= m_sets.size() {}", setIndex,
                  m_sets.size());
    throw CacheException("LoadLine: Invalid set index");
  }

  auto &set =
      CRITICAL_VECTOR_ACCESS(m_sets, setIndex, "Cache::LoadLine set access");
  if (way >= set.size()) {
    spdlog::error("Cache::LoadLine: way {} >= set.size() {}", way, set.size());
    throw CacheException("LoadLine: Invalid way index");
  }

  auto &line = CRITICAL_VECTOR_ACCESS(set, way, "Cache::LoadLine line access");
  
  // CRITICAL: Data buffer size validation
  if (line.data.size() != m_config.lineSize) {
    spdlog::error("Cache::LoadLine: line.data.size() {} != m_config.lineSize {}", line.data.size(), m_config.lineSize);
    throw CacheException("LoadLine: Cache line data buffer size mismatch");
  }

  if (line.valid) {
    EvictLine(setIndex, way);
  }

  auto [_, tag] = ExtractSetAndTag(address);
  line.tag = tag;
  line.valid = true;
  line.dirty = false;
  line.lastUsed = m_currentCycle;
  line.state = CacheLine::State::Exclusive;

  try {
    if (data) {
      // CRITICAL: Add bounds check before memcpy
      if (line.data.size() >= m_config.lineSize) {
        std::memcpy(line.data.data(), data, m_config.lineSize);
        line.dirty = true;
        line.state = CacheLine::State::Modified;
      } else {
        spdlog::error("Cache::LoadLine: Line data size {} < config line size {}",
                     line.data.size(), m_config.lineSize);
        throw CacheException("LoadLine: Line data size insufficient");
      }
      spdlog::trace("Loaded line at 0x{:x} with provided data", address);
    } else if (m_lowerMemory) {
      uint64_t alignedAddr = address & ~(m_config.lineSize - 1);
      m_lowerMemory(alignedAddr, line.data.data(), m_config.lineSize, false);
      line.state = CacheLine::State::Shared;
      m_stats.coherence_traffic += m_config.lineSize;
      spdlog::trace("Loaded line at 0x{:x} from lower memory", address);
    } else {
      std::fill(line.data.begin(), line.data.end(), 0);
      spdlog::trace("Loaded zeroed line at 0x{:x}", address);
    }
  } catch (const std::exception &e) {
    spdlog::error("Failed to load cache line at 0x{:x}: {}", address, e.what());
    throw CacheException("Cache line load failure");
  }

  if (m_config.prefetch) {
    PrefetchWithStrategy(address);
  }
}

void Cache::HandleMESIState(size_t setIndex, size_t way, uint64_t address,
                            bool isWrite) {
  // CRITICAL: Bounds check for set and way access
  if (setIndex >= m_sets.size()) {
    spdlog::error("Cache::HandleMESIState: setIndex {} >= m_sets.size() {}",
                  setIndex, m_sets.size());
    throw CacheException("HandleMESIState: Invalid set index");
  }

  auto &set = CRITICAL_VECTOR_ACCESS(m_sets, setIndex,
                                     "Cache::HandleMESIState set access");
  if (way >= set.size()) {
    spdlog::error("Cache::HandleMESIState: way {} >= set.size() {}", way,
                  set.size());
    throw CacheException("HandleMESIState: Invalid way index");
  }

  auto &line =
      CRITICAL_VECTOR_ACCESS(set, way, "Cache::HandleMESIState line access");
  uint64_t alignedAddr = address & ~(m_config.lineSize - 1);
  line.coherenceTimestamp = m_coherenceTimestamp.fetch_add(1);

  switch (line.state) {
  case CacheLine::State::Invalid:
    if (isWrite) {
      line.state = CacheLine::State::Modified;
      for (Cache *other : otherCaches) {
        other->SendCoherenceMessage(CoherenceMessage::Invalidate, alignedAddr,
                                    this);
      }
    } else {
      bool foundInOther = false;
      for (Cache *other : otherCaches) {
        if (other->IsLineShared(alignedAddr)) {
          line.state = CacheLine::State::Shared;
          AddSharer(alignedAddr, m_cacheId);
          other->AddSharer(alignedAddr, m_cacheId);
          foundInOther = true;
          break;
        }
      }
      if (!foundInOther) {
        line.state = CacheLine::State::Exclusive;
      }
    }
    spdlog::trace("Set line at set {}, way {} to state {} due to {}", setIndex,
                  way, static_cast<int>(line.state),
                  isWrite ? "write" : "read");
    break;

  case CacheLine::State::Exclusive:
    if (isWrite) {
      line.state = CacheLine::State::Modified;
      spdlog::trace(
          "Transitioned line at set {}, way {} from Exclusive to Modified",
          setIndex, way);
    } else if (!otherCaches.empty()) { // If other caches exist, potential read
                                       // could transition to Shared
      line.state = CacheLine::State::Shared;
      AddSharer(alignedAddr, m_cacheId); // Assuming self-add for consistency
    }
    break;

  case CacheLine::State::Shared:
    if (isWrite) {
      line.state = CacheLine::State::Modified;
      RemoveSharer(alignedAddr, m_cacheId);
      for (Cache *other : otherCaches) {
        other->SendCoherenceMessage(CoherenceMessage::Invalidate, alignedAddr,
                                    this);
      }
      spdlog::trace(
          "Transitioned line at set {}, way {} from Shared to Modified",
          setIndex, way);
    }
    break;

  case CacheLine::State::Modified:
    // No change on local access, but ensure write-back on coherence events
    // handled elsewhere
    break;
  }
}

bool Cache::Read(uint64_t address, uint8_t *data, size_t size) {
  // First try with shared lock for cache hit case
  {
    MEMORY_SHARED_LOCK(m_mutex, "CacheMutex");
    m_stats.reads++;
    auto [setIndex, tag] = ExtractSetAndTag(address);
    auto wayOpt = FindLineInSet(setIndex, tag);

    if (wayOpt) {
      spdlog::debug("{}: Cache hit at 0x{:x}", m_config.name, address);
      size_t way = *wayOpt;

      // CRITICAL: Bounds check for set and way access
      if (setIndex >= m_sets.size()) {
        spdlog::error("Cache::Read: setIndex {} >= m_sets.size() {}", setIndex,
                      m_sets.size());
        return false;
      }

      auto &set = CRITICAL_VECTOR_ACCESS(m_sets, setIndex,
                                         "Cache::Read hit set access");
      if (way >= set.size()) {
        spdlog::error("Cache::Read: way {} >= set.size() {}", way, set.size());
        return false;
      }

      auto &line =
          CRITICAL_VECTOR_ACCESS(set, way, "Cache::Read hit line access");
      line.lastUsed = m_currentCycle;
      size_t offset = address % m_config.lineSize;
      size_t bytesToCopy = std::min(size, m_config.lineSize - offset);
      // CRITICAL: Add bounds check before memcpy
      if (offset < line.data.size() && (offset + bytesToCopy) <= line.data.size()) {
        std::memcpy(data, line.data.data() + offset, bytesToCopy);
      } else {
        spdlog::error("Cache::Read: Bounds check failed - offset={}, bytesToCopy={}, lineDataSize={}",
                     offset, bytesToCopy, line.data.size());
        return false;
      }
      m_stats.hits++;
      m_stats.perAddress[address].hits++;
      SimulateLatency();
      return true;
    }
  } // Shared lock released here

  // Cache miss - need exclusive lock for loading
  spdlog::debug("{}: Cache miss at 0x{:x}", m_config.name, address);
  MEMORY_LOCK(m_mutex, "CacheMutex");

  // Re-check after acquiring exclusive lock (double-checked locking pattern)
  auto [setIndex, tag] = ExtractSetAndTag(address);
  auto wayOpt = FindLineInSet(setIndex, tag);
  if (wayOpt) {
    // Another thread loaded it while we were waiting for the lock
    size_t way = *wayOpt;

    // CRITICAL: Bounds check for set and way access
    if (setIndex >= m_sets.size()) {
      spdlog::error("Cache::Read: setIndex {} >= m_sets.size() {}", setIndex,
                    m_sets.size());
      return false;
    }

    auto &set = CRITICAL_VECTOR_ACCESS(m_sets, setIndex,
                                       "Cache::Read double-check set access");
    if (way >= set.size()) {
      spdlog::error("Cache::Read: way {} >= set.size() {}", way, set.size());
      return false;
    }

    auto &line = CRITICAL_VECTOR_ACCESS(set, way,
                                        "Cache::Read double-check line access");
    line.lastUsed = m_currentCycle;
    size_t offset = address % m_config.lineSize;
    size_t bytesToCopy = std::min(size, m_config.lineSize - offset);
    std::memcpy(data, line.data.data() + offset, bytesToCopy);
    m_stats.hits++;
    m_stats.perAddress[address].hits++;
    SimulateLatency();
    return true;
  }

  size_t way = FindLRUInSet(setIndex);
  LoadLine(setIndex, way, address);
  HandleMESIState(setIndex, way, address, false);

  // CRITICAL: Bounds check for final line access
  if (setIndex >= m_sets.size()) {
    spdlog::error("Cache::Read: setIndex {} >= m_sets.size() {}", setIndex,
                  m_sets.size());
    return false;
  }

  auto &set =
      CRITICAL_VECTOR_ACCESS(m_sets, setIndex, "Cache::Read final set access");
  if (way >= set.size()) {
    spdlog::error("Cache::Read: way {} >= set.size() {}", way, set.size());
    return false;
  }

  auto &line =
      CRITICAL_VECTOR_ACCESS(set, way, "Cache::Read final line access");
  size_t offset = address % m_config.lineSize;
  size_t bytesToCopy = std::min(size, m_config.lineSize - offset);
  std::memcpy(data, line.data.data() + offset, bytesToCopy);
  m_stats.misses++;
  m_stats.perAddress[address].misses++;
  SimulateLatency();
  return false;
}

bool Cache::Write(uint64_t address, const uint8_t *data, size_t size) {
  MEMORY_LOCK(m_mutex, "CacheMutex");
  m_stats.writes++;
  auto [setIndex, tag] = ExtractSetAndTag(address);
  auto wayOpt = FindLineInSet(setIndex, tag);

  if (wayOpt) {
    spdlog::debug("{}: Cache hit during write at 0x{:x}", m_config.name,
                  address);
    size_t way = *wayOpt;

    // CRITICAL: Bounds check for set and way access
    if (setIndex >= m_sets.size()) {
      spdlog::error("Cache::Write: setIndex {} >= m_sets.size() {}", setIndex,
                    m_sets.size());
      return false;
    }

    auto &set =
        CRITICAL_VECTOR_ACCESS(m_sets, setIndex, "Cache::Write hit set access");
    if (way >= set.size()) {
      spdlog::error("Cache::Write: way {} >= set.size() {}", way, set.size());
      return false;
    }

    auto &line =
        CRITICAL_VECTOR_ACCESS(set, way, "Cache::Write hit line access");
    line.lastUsed = m_currentCycle;
    size_t offset = address % m_config.lineSize;
    size_t bytesToCopy = std::min(size, m_config.lineSize - offset);

    // Bounds check before memcpy
    if (data != nullptr && bytesToCopy > 0 &&
        offset < line.data.size() &&
        (offset + bytesToCopy) <= line.data.size()) {
      std::memcpy(line.data.data() + offset, data, bytesToCopy);
    } else {
      spdlog::error("Cache::Write: Invalid parameters for memcpy - data={}, bytesToCopy={}, offset={}, lineDataSize={}",
                   static_cast<const void*>(data), bytesToCopy, offset, line.data.size());
      return false;
    }
    if (m_config.writeBack) {
      line.dirty = true;
    } else if (m_lowerMemory) {
      try {
        uint64_t alignedAddr = address & ~(m_config.lineSize - 1);
        m_lowerMemory(alignedAddr, line.data.data(), m_config.lineSize, true);
        m_stats.coherence_traffic += m_config.lineSize;
        spdlog::trace("Write-through at 0x{:x}", alignedAddr);
      } catch (const std::exception &e) {
        spdlog::error("Write-through failed at 0x{:x}: {}", address, e.what());
        throw CacheException("Write-through failure");
      }
    }
    HandleMESIState(setIndex, way, address, true);
    m_stats.hits++;
    m_stats.perAddress[address].hits++;
    SimulateLatency();
    return true;
  }

  spdlog::debug("{}: Cache miss during write at 0x{:x}", m_config.name,
                address);
  size_t way = FindLRUInSet(setIndex);
  LoadLine(setIndex, way, address, data);
  HandleMESIState(setIndex, way, address, true);
  if (!m_config.writeBack && m_lowerMemory) {
    try {
      uint64_t alignedAddr = address & ~(m_config.lineSize - 1);

      // CRITICAL: Bounds check for write-through access
      if (setIndex >= m_sets.size()) {
        spdlog::error("Cache::Write: setIndex {} >= m_sets.size() {}", setIndex,
                      m_sets.size());
        throw CacheException("Write-through: Invalid set index");
      }

      auto &set = CRITICAL_VECTOR_ACCESS(
          m_sets, setIndex, "Cache::Write write-through set access");
      if (way >= set.size()) {
        spdlog::error("Cache::Write: way {} >= set.size() {}", way, set.size());
        throw CacheException("Write-through: Invalid way index");
      }

      auto &line = CRITICAL_VECTOR_ACCESS(
          set, way, "Cache::Write write-through line access");
      m_lowerMemory(alignedAddr, line.data.data(), m_config.lineSize, true);
      m_stats.coherence_traffic += m_config.lineSize;
      spdlog::trace("Write-through after load at 0x{:x}", alignedAddr);
    } catch (const std::exception &e) {
      spdlog::error("Write-through failed at 0x{:x}: {}", address, e.what());
      throw CacheException("Write-through failure");
    }
  }
  m_stats.misses++;
  m_stats.perAddress[address].misses++;
  SimulateLatency();
  return false;
}

void Cache::Invalidate(uint64_t address) {
  MEMORY_LOCK(m_mutex, "CacheMutex");
  auto [setIndex, tag] = ExtractSetAndTag(address);
  auto wayOpt = FindLineInSet(setIndex, tag);
  if (wayOpt) {
    size_t way = *wayOpt;

    // CRITICAL: Bounds check for set and way access
    if (setIndex >= m_sets.size()) {
      spdlog::error("Cache::Invalidate: setIndex {} >= m_sets.size() {}",
                    setIndex, m_sets.size());
      return;
    }

    auto &set = CRITICAL_VECTOR_ACCESS(m_sets, setIndex,
                                       "Cache::Invalidate set access");
    if (way >= set.size()) {
      spdlog::error("Cache::Invalidate: way {} >= set.size() {}", way,
                    set.size());
      return;
    }

    auto &line =
        CRITICAL_VECTOR_ACCESS(set, way, "Cache::Invalidate line access");
    if (line.valid && line.dirty && m_config.writeBack && m_lowerMemory) {
      uint64_t alignedAddr =
          (line.tag << (m_setIndexBits +
                        static_cast<size_t>(std::log2(m_config.lineSize)))) |
          (setIndex << static_cast<size_t>(std::log2(m_config.lineSize)));
      try {
        m_lowerMemory(alignedAddr, line.data.data(), m_config.lineSize, true);
        m_stats.coherence_traffic += m_config.lineSize;
        spdlog::trace("Invalidated dirty line at 0x{:x} and wrote back",
                      alignedAddr);
      } catch (const std::exception &e) {
        spdlog::error("Write-back failed at 0x{:x}: {}", alignedAddr, e.what());
        throw CacheException("Write-back failure");
      }
    } else if (line.valid) {
      spdlog::trace("Invalidated clean line at 0x{:x}", address);
    }
    line.valid = false;
    line.dirty = false;
    line.state = CacheLine::State::Invalid;
  }
}

void Cache::Flush() {
  MEMORY_LOCK(m_mutex, "CacheMutex");
  for (size_t setIndex = 0; setIndex < m_numSets; ++setIndex) {
    // CRITICAL: Bounds check for set access
    if (setIndex >= m_sets.size()) {
      spdlog::error("Cache::Flush: setIndex {} >= m_sets.size() {}", setIndex,
                    m_sets.size());
      continue;
    }

    auto &set =
        CRITICAL_VECTOR_ACCESS(m_sets, setIndex, "Cache::Flush set access");
    for (size_t way = 0; way < m_config.associativity && way < set.size();
         ++way) {
      auto &line = CRITICAL_VECTOR_ACCESS(set, way, "Cache::Flush line access");
      if (line.valid && line.dirty && m_config.writeBack && m_lowerMemory) {
        uint64_t address =
            (line.tag << (m_setIndexBits +
                          static_cast<size_t>(std::log2(m_config.lineSize)))) |
            (setIndex << static_cast<size_t>(std::log2(m_config.lineSize)));
        try {
          m_lowerMemory(address, line.data.data(), m_config.lineSize, true);
          m_stats.coherence_traffic += m_config.lineSize;
          spdlog::trace("Flushed dirty line at 0x{:x} and wrote back", address);
        } catch (const std::exception &e) {
          spdlog::error("Flush write-back failed at 0x{:x}: {}", address,
                        e.what());
          throw CacheException("Flush write-back failure");
        }
      }
      line.valid = false;
      line.dirty = false;
      line.state = CacheLine::State::Invalid;
    }
  }
  m_stats = Stats();
  spdlog::info("Cache {} flushed", m_config.name);
}

void Cache::Update(uint64_t cycles) {
  MEMORY_LOCK(m_mutex, "CacheMutex");
  m_currentCycle += cycles;
  m_stats.total_latency += cycles;
}

void Cache::SimulateLatency() {
  m_currentCycle += m_config.latencyCycles;
  m_stats.total_latency += m_config.latencyCycles;
}

Cache::Stats Cache::GetStats() const {
  MEMORY_SHARED_LOCK(m_mutex, "CacheMutex");
  return m_stats;
}

void Cache::LogStats() const {
  MEMORY_SHARED_LOCK(m_mutex, "CacheMutex");
  double hitRatio =
      m_stats.hits + m_stats.misses > 0
          ? static_cast<double>(m_stats.hits) / (m_stats.hits + m_stats.misses)
          : 0.0;
  spdlog::info(
      fmt::runtime(
          "Cache {} Stats: hits={}, misses={}, reads={}, writes={}, "
          "evictions={}, coherence_traffic={}B, total_latency={} cycles, "
          "hitRatio={:.2%}"),
      m_config.name, m_stats.hits, m_stats.misses, m_stats.reads,
      m_stats.writes, m_stats.evictions, m_stats.coherence_traffic,
      m_stats.total_latency, hitRatio);

  if (!m_stats.perAddress.empty()) {
    spdlog::debug("Per-address cache statistics for {}:", m_config.name);
    for (const auto &[addr, addrStats] : m_stats.perAddress) {
      if (addrStats.hits + addrStats.misses > 10) {
        double addrHitRatio = addrStats.hits + addrStats.misses > 0
                                  ? static_cast<double>(addrStats.hits) /
                                        (addrStats.hits + addrStats.misses)
                                  : 0.0;
        spdlog::debug("  Address 0x{:x}: hits={}, misses={}, hitRatio={:.2f}%",
                      addr, addrStats.hits, addrStats.misses,
                      addrHitRatio * 100.0);
      }
    }
  }
}

void Cache::SaveState(std::ostream &out) const {
  MEMORY_SHARED_LOCK(m_mutex, "CacheMutex");
  uint32_t version = 1;
  out.write(reinterpret_cast<const char *>(&version), sizeof(version));
  out.write(reinterpret_cast<const char *>(&m_currentCycle),
            sizeof(m_currentCycle));
  out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));
  for (const auto &set : m_sets) {
    for (const auto &line : set) {
      out.write(reinterpret_cast<const char *>(&line.valid),
                sizeof(line.valid));
      out.write(reinterpret_cast<const char *>(&line.dirty),
                sizeof(line.dirty));
      out.write(reinterpret_cast<const char *>(&line.tag), sizeof(line.tag));
      out.write(reinterpret_cast<const char *>(&line.lastUsed),
                sizeof(line.lastUsed));
      out.write(reinterpret_cast<const char *>(&line.state),
                sizeof(line.state));
      out.write(reinterpret_cast<const char *>(line.data.data()),
                line.data.size());
    }
  }
  spdlog::info("Cache {} state saved", m_config.name);
}

void Cache::LoadState(std::istream &in) {
  MEMORY_LOCK(m_mutex, "CacheMutex");
  uint32_t version;
  in.read(reinterpret_cast<char *>(&version), sizeof(version));
  if (version != 1) {
    spdlog::error("Unsupported cache state version: {}", version);
    throw CacheException("Invalid cache state version");
  }
  in.read(reinterpret_cast<char *>(&m_currentCycle), sizeof(m_currentCycle));
  in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));
  for (auto &set : m_sets) {
    for (auto &line : set) {
      in.read(reinterpret_cast<char *>(&line.valid), sizeof(line.valid));
      in.read(reinterpret_cast<char *>(&line.dirty), sizeof(line.dirty));
      in.read(reinterpret_cast<char *>(&line.tag), sizeof(line.tag));
      in.read(reinterpret_cast<char *>(&line.lastUsed), sizeof(line.lastUsed));
      in.read(reinterpret_cast<char *>(&line.state), sizeof(line.state));
      in.read(reinterpret_cast<char *>(line.data.data()), line.data.size());
    }
  }
  spdlog::info("Cache {} state loaded", m_config.name);
}

void Cache::RegisterOtherCache(Cache *cache) {
  MEMORY_LOCK(m_mutex, "CacheMutex");
  otherCaches.push_back(cache);
  spdlog::info("Registered cache {} for coherence with {}",
               cache->GetConfig().name, m_config.name);
}

void Cache::SetLatency(size_t cycles) {
  MEMORY_LOCK(m_mutex, "CacheMutex");
  m_config.latencyCycles = cycles;
  spdlog::info("Cache {} latency set to {} cycles", m_config.name, cycles);
}

void Cache::SetPrefetch(bool enable, size_t stride) {
  MEMORY_LOCK(m_mutex, "CacheMutex");
  m_config.prefetch = enable;
  m_config.prefetchStride = std::max<size_t>(stride, 1);
  spdlog::info("Cache {} prefetch: enabled={}, stride={}", m_config.name,
               enable, m_config.prefetchStride);
}

void Cache::BroadcastInvalidate(uint64_t address) {
  MEMORY_LOCK(m_mutex, "CacheMutex");
  for (Cache *other : otherCaches) {
    other->Invalidate(address);
    m_stats.coherence_traffic += sizeof(uint64_t);
  }
  spdlog::trace("Broadcast invalidate for address 0x{:x}", address);
}

// Enhanced coherence methods implementation

void Cache::SendCoherenceMessage(CoherenceMessage msg, uint64_t address,
                                 Cache *target) {
  if (target) {
    target->HandleCoherenceMessage(msg, address, this);
    m_stats.coherence_traffic += sizeof(CoherenceMessage) + sizeof(uint64_t);
    spdlog::trace("Sent coherence message {} for address 0x{:x}",
                  static_cast<int>(msg), address);
  }
}

void Cache::HandleCoherenceMessage(CoherenceMessage msg, uint64_t address,
                                   Cache *sender) {
  MEMORY_LOCK(m_mutex, "CacheMutex");
  auto [setIndex, tag] = ExtractSetAndTag(address);
  auto wayOpt = FindLineInSet(setIndex, tag);

  if (!wayOpt) {
    spdlog::trace("No line found for address 0x{:x} in coherence message",
                  address);
    return; // No line, nothing to do for most messages
  }

  size_t way = *wayOpt;

  // CRITICAL: Bounds check for set and way access
  if (setIndex >= m_sets.size()) {
    spdlog::error(
        "Cache::HandleCoherenceMessage: setIndex {} >= m_sets.size() {}",
        setIndex, m_sets.size());
    return;
  }

  auto &set = CRITICAL_VECTOR_ACCESS(
      m_sets, setIndex, "Cache::HandleCoherenceMessage set access");
  if (way >= set.size()) {
    spdlog::error("Cache::HandleCoherenceMessage: way {} >= set.size() {}", way,
                  set.size());
    return;
  }

  auto &line = CRITICAL_VECTOR_ACCESS(
      set, way, "Cache::HandleCoherenceMessage line access");

  switch (msg) {
  case CoherenceMessage::ReadRequest:
    if (line.state == CacheLine::State::Modified ||
        line.state == CacheLine::State::Exclusive) {
      line.state = CacheLine::State::Shared;
      AddSharer(address, m_cacheId);
      sender->AddSharer(address, sender->m_cacheId);
      spdlog::trace(
          "Handled ReadRequest: transitioned to Shared for address 0x{:x}",
          address);
    } else if (line.state == CacheLine::State::Shared) {
      // Already shared, no change or send acknowledgment if needed
      sender->SendCoherenceMessage(CoherenceMessage::Acknowledgment, address,
                                   this);
    }
    break;

  case CoherenceMessage::WriteRequest:
    if (line.state != CacheLine::State::Invalid) {
      if (line.state == CacheLine::State::Modified) {
        // Write back if necessary
        if (m_lowerMemory) {
          m_lowerMemory(address, line.data.data(), m_config.lineSize, true);
          m_stats.coherence_traffic += m_config.lineSize;
        }
      }
      line.state = CacheLine::State::Invalid;
      line.valid = false;
      RemoveSharer(address, m_cacheId);
      spdlog::trace("Handled WriteRequest: invalidated line for address 0x{:x}",
                    address);
    }
    break;

  case CoherenceMessage::Invalidate:
    if (line.state != CacheLine::State::Invalid) {
      if (line.state == CacheLine::State::Modified && m_lowerMemory) {
        m_lowerMemory(address, line.data.data(), m_config.lineSize, true);
        m_stats.coherence_traffic += m_config.lineSize;
      }
      line.state = CacheLine::State::Invalid;
      line.valid = false;
      RemoveSharer(address, m_cacheId);
      spdlog::trace("Handled Invalidate: invalidated line for address 0x{:x}",
                    address);
    }
    break;

  case CoherenceMessage::Intervention:
    // New: Handle intervention (e.g., supply data to another cache)
    if (line.state == CacheLine::State::Modified) {
      sender->LoadLine(
          sender->ExtractSetAndTag(address)
              .first, // Simplified, may need better set/way handling
          sender->FindLRUInSet(sender->ExtractSetAndTag(address).first),
          address, line.data.data());
      line.state = CacheLine::State::Shared; // Transition after supplying data
      AddSharer(address, m_cacheId);
      sender->AddSharer(address, sender->m_cacheId);
      spdlog::trace("Handled Intervention: supplied data for address 0x{:x}",
                    address);
    }
    break;

  case CoherenceMessage::Acknowledgment:
    // New: Handle acknowledgment (e.g., confirm invalidate or read)
    // For simplicity, just log or update sharers if needed
    spdlog::trace("Handled Acknowledgment for address 0x{:x}", address);
    break;

  case CoherenceMessage::Writeback:
    // Already handled in existing code, ensure it's correct
    if (line.state == CacheLine::State::Modified && m_lowerMemory) {
      m_lowerMemory(address, line.data.data(), m_config.lineSize, true);
      m_stats.coherence_traffic += m_config.lineSize;
      line.state = CacheLine::State::Invalid; // Transition after writeback
      spdlog::trace(
          "Handled Writeback: wrote back and invalidated for address 0x{:x}",
          address);
    }
    break;

  default:
    spdlog::debug("Unhandled coherence message {} for address 0x{:x}",
                  static_cast<int>(msg), address);
    break;
  }
}

bool Cache::IsLineShared(uint64_t address) const {
  MEMORY_SHARED_LOCK(m_mutex, "CacheMutex");
  auto [setIndex, tag] = ExtractSetAndTag(address);
  auto wayOpt = FindLineInSet(setIndex, tag);

  if (wayOpt) {
    size_t way = *wayOpt;

    // CRITICAL: Bounds check for set and way access
    if (setIndex >= m_sets.size()) {
      spdlog::error("Cache::IsLineShared: setIndex {} >= m_sets.size() {}",
                    setIndex, m_sets.size());
      return false;
    }

    const auto &set = CRITICAL_VECTOR_ACCESS(m_sets, setIndex,
                                             "Cache::IsLineShared set access");
    if (way >= set.size()) {
      spdlog::error("Cache::IsLineShared: way {} >= set.size() {}", way,
                    set.size());
      return false;
    }

    const auto &line =
        CRITICAL_VECTOR_ACCESS(set, way, "Cache::IsLineShared line access");
    return line.valid && (line.state == CacheLine::State::Shared ||
                          line.state == CacheLine::State::Modified ||
                          line.state == CacheLine::State::Exclusive);
  }
  return false;
}

void Cache::AddSharer(uint64_t address, size_t cacheId) {
  uint64_t alignedAddr = address & ~(m_config.lineSize - 1);
  auto &sharers = m_lineSharers[alignedAddr];
  if (std::find(sharers.begin(), sharers.end(), cacheId) == sharers.end()) {
    sharers.push_back(cacheId);
  }
}

void Cache::RemoveSharer(uint64_t address, size_t cacheId) {
  uint64_t alignedAddr = address & ~(m_config.lineSize - 1);
  auto it = m_lineSharers.find(alignedAddr);
  if (it != m_lineSharers.end()) {
    auto &sharers = it->second;
    sharers.erase(std::remove(sharers.begin(), sharers.end(), cacheId),
                  sharers.end());
    if (sharers.empty()) {
      m_lineSharers.erase(it);
    }
  }
}

// Advanced prefetching methods implementation

void Cache::UpdateStrideDetection(uint64_t address) {
  uint64_t alignedAddr = address & ~(m_config.lineSize - 1);

  // Update recent accesses
  m_recentAccesses.push_back(alignedAddr);
  if (m_recentAccesses.size() > 16) {
    m_recentAccesses.erase(m_recentAccesses.begin());
  }

  // Update stride table
  auto it = m_strideTable.find(alignedAddr);
  if (it != m_strideTable.end()) {
    StrideEntry &entry = it->second;
    if (entry.lastAddress != 0) {
      int64_t newStride = static_cast<int64_t>(alignedAddr) -
                          static_cast<int64_t>(entry.lastAddress);
      if (newStride == entry.stride) {
        entry.consecutiveHits++;
        entry.confidence =
            std::min(entry.confidence + 1, static_cast<size_t>(10));
      } else {
        entry.stride = newStride;
        entry.consecutiveHits = 1;
        entry.confidence = 1;
      }
    }
    entry.lastAddress = alignedAddr;
    entry.lastAccessTime = m_currentCycle;
  } else {
    // Add new entry, evict old ones if necessary
    if (m_strideTable.size() >= m_maxStrideEntries) {
      // Find oldest entry to evict
      auto oldestIt = std::min_element(
          m_strideTable.begin(), m_strideTable.end(),
          [](const auto &a, const auto &b) {
            return a.second.lastAccessTime < b.second.lastAccessTime;
          });
      m_strideTable.erase(oldestIt);
    }

    StrideEntry newEntry;
    newEntry.lastAddress = alignedAddr;
    newEntry.lastAccessTime = m_currentCycle;
    m_strideTable[alignedAddr] = newEntry;
  }
}

void Cache::AdaptivePrefetch(uint64_t address) {
  UpdateStrideDetection(address);

  uint64_t alignedAddr = address & ~(m_config.lineSize - 1);
  auto it = m_strideTable.find(alignedAddr);

  if (it != m_strideTable.end()) {
    const StrideEntry &entry = it->second;
    if (entry.confidence >= 3 && entry.consecutiveHits >= 2) {
      StridePrefetch(alignedAddr, entry.stride);
    }
  }
}

void Cache::StridePrefetch(uint64_t address, int64_t stride) {
  if (stride == 0)
    return;

  for (size_t i = 1; i <= m_config.prefetchDistance; ++i) {
    uint64_t prefetchAddr = address + (stride * static_cast<int64_t>(i));
    auto [setIndex, tag] = ExtractSetAndTag(prefetchAddr);

    if (!FindLineInSet(setIndex, tag)) {
      size_t way = FindLRUInSet(setIndex);
      try {
        LoadLine(setIndex, way, prefetchAddr);
        spdlog::trace("Stride prefetched line at 0x{:x}, stride={}",
                      prefetchAddr, stride);
      } catch (const std::exception &e) {
        spdlog::debug("Stride prefetch failed for 0x{:x}: {}", prefetchAddr,
                      e.what());
        break; // Stop prefetching on error
      }
    }
  }
}

int64_t Cache::DetectStride(uint64_t address) {
  if (m_recentAccesses.size() < 2)
    return 0;

  uint64_t alignedAddr = address & ~(m_config.lineSize - 1);
  uint64_t lastAddr = m_recentAccesses.back();

  return static_cast<int64_t>(alignedAddr) - static_cast<int64_t>(lastAddr);
}

void Cache::PrefetchWithStrategy(uint64_t address) {
  switch (m_config.prefetchStrategy) {
  case PrefetchStrategy::None:
    break;

  case PrefetchStrategy::NextLine:
    for (size_t i = 1; i <= m_config.prefetchStride; ++i) {
      uint64_t nextAddr =
          (address & ~(m_config.lineSize - 1)) + i * m_config.lineSize;
      auto [nextSet, nextTag] = ExtractSetAndTag(nextAddr);
      if (!FindLineInSet(nextSet, nextTag)) {
        size_t nextWay = FindLRUInSet(nextSet);
        try {
          LoadLine(nextSet, nextWay, nextAddr);
          spdlog::trace("Next-line prefetched line at 0x{:x}", nextAddr);
        } catch (const std::exception &e) {
          spdlog::debug("Next-line prefetch failed for 0x{:x}: {}", nextAddr,
                        e.what());
          break;
        }
      }
    }
    break;

  case PrefetchStrategy::Stride: {
    int64_t stride = DetectStride(address);
    if (stride != 0) {
      StridePrefetch(address, stride);
    }
  } break;

  case PrefetchStrategy::Adaptive:
    AdaptivePrefetch(address);
    break;
  }
}

} // namespace x86_64