#pragma once
#include <array>
#include <atomic>
#include <cstdint>
#include <functional>
#include <iostream>
#include <optional>
#include <queue>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <vector>

namespace x86_64 {
/**
 * @brief Cache simulator for x86_64 architecture with MESI coherence and
 * prefetching.
 */
class Cache {
public:
  /**
   * @brief Prefetching strategy options.
   */
  enum class PrefetchStrategy {
    None,     ///< No prefetching
    NextLine, ///< Simple next-line prefetching
    Stride,   ///< Stride-based prefetching
    Adaptive  ///< Adaptive stride detection
  };

  /**
   * @brief Configuration for cache initialization.
   */
  struct Config {
    size_t size = 0;     ///< Total cache size in bytes (e.g., 32768 for 32KB)
    size_t lineSize = 0; ///< Cache line size in bytes (e.g., 64)
    size_t associativity = 0;  ///< Number of ways (e.g., 4-way)
    bool writeBack = false;    ///< True for write-back, false for write-through
    size_t latencyCycles = 0;  ///< Simulated latency in CPU cycles
    bool prefetch = false;     ///< Enable prefetching (next-line or stride)
    size_t prefetchStride = 1; ///< Stride for prefetching (in lines)
    PrefetchStrategy prefetchStrategy =
        PrefetchStrategy::NextLine; ///< Prefetch strategy
    size_t prefetchDistance = 2;    ///< How far ahead to prefetch
    std::string name;               ///< Cache identifier (e.g., "L1 Data")
  };

  /**
   * @brief MESI coherence message types for inter-cache communication.
   */
  enum class CoherenceMessage {
    ReadRequest,   ///< Request to read a cache line
    WriteRequest,  ///< Request to write a cache line
    Invalidate,    ///< Invalidate cache line
    Intervention,  ///< Intervention (supply data)
    Writeback,     ///< Write back dirty data
    Acknowledgment ///< Acknowledge message
  };

  /**
   * @brief Stride detection entry for adaptive prefetching.
   */
  struct StrideEntry {
    uint64_t lastAddress = 0;    ///< Last accessed address
    int64_t stride = 0;          ///< Detected stride
    size_t confidence = 0;       ///< Confidence in stride prediction
    size_t consecutiveHits = 0;  ///< Consecutive stride hits
    uint64_t lastAccessTime = 0; ///< Last access timestamp
  };

  /**
   * @brief Cache line with enhanced MESI coherence state.
   */
  struct CacheLine {
    uint64_t tag = 0;          ///< Tag for address matching
    bool valid = false;        ///< Line validity
    bool dirty = false;        ///< Line modified (for write-back)
    std::vector<uint8_t> data; ///< Cached data
    uint64_t lastUsed = 0;     ///< LRU timestamp
    enum class State {
      Invalid,
      Modified,
      Exclusive,
      Shared
    } state = State::Invalid; ///< MESI state

    // Enhanced coherence tracking
    bool pendingInvalidation =
        false;                       ///< Pending invalidation from other caches
    uint64_t coherenceTimestamp = 0; ///< Timestamp for coherence ordering
    std::vector<size_t> sharers;     ///< List of caches sharing this line
  };

  /**
   * @brief Callback for lower-level memory access.
   */
  using LowerMemory =
      std::function<void(uint64_t, uint8_t *, size_t, bool isWrite)>;

  /**
   * @brief Constructs a cache with given configuration.
   * @param config Cache configuration.
   * @param lowerMemory Optional callback for lower memory access.
   * @throws CacheException on invalid configuration.
   */
  Cache() = default;
  explicit Cache(const Config &config, LowerMemory lowerMemory = nullptr);
  explicit Cache(size_t sizeInBytes);

  /**
   * @brief Reads data from cache.
   * @param address Memory address.
   * @param data Output buffer.
   * @param size Bytes to read.
   * @return True on cache hit, false on miss.
   */
  bool Read(uint64_t address, uint8_t *data, size_t size);

  /**
   * @brief Writes data to cache.
   * @param address Memory address.
   * @param data Input data.
   * @param size Bytes to write.
   * @return True on cache hit, false on miss.
   */
  bool Write(uint64_t address, const uint8_t *data, size_t size);

  /**
   * @brief Invalidates a cache line.
   * @param address Memory address.
   */
  void Invalidate(uint64_t address);

  /**
   * @brief Flushes entire cache.
   */
  void Flush();

  /**
   * @brief Updates cycle count.
   * @param cycles Cycles to advance.
   */
  void Update(uint64_t cycles);

  /**
   * @brief Cache statistics.
   */
  struct Stats {
    uint64_t hits = 0;
    uint64_t misses = 0;
    uint64_t evictions = 0;
    uint64_t writes = 0;
    uint64_t reads = 0;
    uint64_t coherence_traffic = 0; ///< Bytes transferred for coherence
    uint64_t total_latency = 0;     ///< Total latency in cycles
    struct AddressStats {
      uint64_t hits = 0;
      uint64_t misses = 0;
    };
    std::unordered_map<uint64_t, AddressStats>
        perAddress; ///< Per-address stats
    double hitRatio() const {
      return (hits + misses) > 0 ? static_cast<double>(hits) / (hits + misses)
                                 : 0.0;
    }
  };

  /**
   * @brief Gets current statistics.
   * @return Cache statistics.
   */
  Stats GetStats() const;

  /**
   * @brief Logs statistics via spdlog.
   */
  void LogStats() const;

  /**
   * @brief Gets cache configuration.
   * @return Configuration reference.
   */
  const Config &GetConfig() const { return m_config; }

  /**
   * @brief Logs statistics for a specific cache.
   * @param name Cache name.
   * @param hits Number of cache hits.
   * @param misses Number of cache misses.
   * @param accesses Total number of accesses.
   */
  void LogStats(const std::string &name, size_t hits, size_t misses,
                size_t accesses) const;

  /**
   * @brief Saves cache state to stream.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads cache state from stream.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Registers another cache for coherence.
   * @param cache Other cache instance.
   */
  void RegisterOtherCache(Cache *cache);

  /**
   * @brief Updates latency dynamically.
   * @param cycles New latency in cycles.
   */
  void SetLatency(size_t cycles);

  /**
   * @brief Enables/disables prefetching and sets stride.
   * @param enable Prefetching state.
   * @param stride Prefetch stride in lines.
   */
  void SetPrefetch(bool enable, size_t stride = 1);

  /**
   * @brief Enhanced coherence methods for multi-core MESI protocol.
   */
  void SendCoherenceMessage(CoherenceMessage msg, uint64_t address,
                            Cache *target);
  void HandleCoherenceMessage(CoherenceMessage msg, uint64_t address,
                              Cache *sender);
  void ProcessCoherenceQueue();
  bool IsLineShared(uint64_t address) const;
  void AddSharer(uint64_t address, size_t cacheId);
  void RemoveSharer(uint64_t address, size_t cacheId);

  /**
   * @brief Advanced prefetching methods.
   */
  void UpdateStrideDetection(uint64_t address);
  void AdaptivePrefetch(uint64_t address);
  void StridePrefetch(uint64_t address, int64_t stride);
  int64_t DetectStride(uint64_t address);
  void PrefetchWithStrategy(uint64_t address);

private:
  std::vector<std::vector<CacheLine>> m_sets; ///< Cache sets
  size_t m_numSets = 0;                       ///< Number of sets
  Config m_config;                            ///< Configuration
  uint64_t m_currentCycle = 0;                ///< Current cycle
  uint64_t m_setMask = 0;                     ///< Set index mask
  uint64_t m_tagMask = 0;                     ///< Tag mask
  size_t m_setIndexBits = 0;                  ///< Set index bits
  size_t m_tagBits = 0;                       ///< Tag bits
  mutable std::shared_mutex m_mutex;          ///< Thread safety
  Stats m_stats;                              ///< Statistics
  LowerMemory m_lowerMemory;                  ///< Lower memory callback
  std::vector<Cache *> otherCaches;           ///< Other caches for coherence

  // Enhanced coherence support
  std::queue<std::pair<CoherenceMessage, uint64_t>>
      m_coherenceQueue; ///< Coherence message queue
  std::unordered_map<uint64_t, std::vector<size_t>>
      m_lineSharers;                             ///< Line sharers tracking
  size_t m_cacheId = 0;                          ///< Unique cache ID
  std::atomic<uint64_t> m_coherenceTimestamp{0}; ///< Global coherence timestamp

  // Advanced prefetching support
  std::unordered_map<uint64_t, StrideEntry>
      m_strideTable;                      ///< Stride detection table
  std::vector<uint64_t> m_recentAccesses; ///< Recent access history
  size_t m_maxStrideEntries = 64;         ///< Max stride table entries

  void ValidateConfig();
  void InitializeCache();
  std::pair<size_t, uint64_t> ExtractSetAndTag(uint64_t address) const;
  std::optional<size_t> FindLineInSet(size_t setIndex, uint64_t tag) const;
  size_t FindLRUInSet(size_t setIndex) const;
  void EvictLine(size_t setIndex, size_t way);
  void LoadLine(size_t setIndex, size_t way, uint64_t address,
                const uint8_t *data = nullptr);
  void SimulateLatency();
  void HandleMESIState(size_t setIndex, size_t way, uint64_t address,
                       bool isWrite);
  void BroadcastInvalidate(uint64_t address);
};

} // namespace x86_64