#pragma once
#include <string>
#include <vector>

namespace ps4 {

class PS4Emulator;

class GameBrowser {
public:
    GameBrowser(PS4Emulator* emulator);
    void Render(bool* p_open);
    void Draw(bool* p_open) { Render(p_open); }

    std::string GetSelectedGamePath() const;
    void ClearSelectedGamePath();

private:
    PS4Emulator* emulator_;
    std::vector<std::string> game_list_;
    int selected_index_ = -1;

    void RefreshGameList();
};

} // namespace ps4
