#include "game_browser.h"
#include "ps4/ps4_emulator.h"
#include "imgui.h"
#include <filesystem>
#include <algorithm>
#include <future>
#include <spdlog/spdlog.h>

namespace ps4 {

GameBrowser::GameBrowser(PS4Emulator* emulator)
    : emulator_(emulator) {
    RefreshGameList();
}

void GameBrowser::RefreshGameList() {
    game_list_.clear();
    if (!emulator_) return;
    auto game_dir = emulator_->GetFilesystem().GetGameDirectory();
    if (game_dir.empty()) return;

    try {
        for (const auto& entry : std::filesystem::directory_iterator(game_dir)) {
            if (entry.is_regular_file()) {
                auto path = entry.path();
                auto ext = path.extension().string();
                std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
                if (ext == ".iso" || ext == ".bin" || ext == ".pkg") {
                    game_list_.push_back(path.filename().string());
                }
            }
        }
    } catch (const std::exception& e) {
        spdlog::error("Failed to list games in directory {}: {}", game_dir, e.what());
    }
}

void GameBrowser::Render(bool* p_open) {
    if (!ImGui::Begin("Game Browser", p_open)) {
        ImGui::End();
        return;
    }

    if (ImGui::Button("Refresh")) {
        RefreshGameList();
    }
    ImGui::Separator();

    if (game_list_.empty()) {
        ImGui::Text("No games found in directory.");
    } else {
        for (int i = 0; i < static_cast<int>(game_list_.size()); ++i) {
            // CRITICAL: Add bounds check for game list access
            if (i >= static_cast<int>(game_list_.size())) {
                break;
            }
            bool is_selected = (i == selected_index_);
            if (ImGui::Selectable(game_list_[i].c_str(), is_selected)) {
                selected_index_ = i;
                // Removed call to non-existent LaunchGame method
                // You may implement game launch logic here if needed
            }
        }
    }

    ImGui::End();
}

std::string GameBrowser::GetSelectedGamePath() const {
    if (selected_index_ >= 0 && selected_index_ < static_cast<int>(game_list_.size())) {
        if (!emulator_) return "";
        auto game_dir = emulator_->GetFilesystem().GetGameDirectory();
        return game_dir + "/" + game_list_[selected_index_];
    }
    return "";
}

void GameBrowser::ClearSelectedGamePath() {
    selected_index_ = -1;
}

} // namespace ps4
