#include "performance_overlay.h"
#include "ps4/ps4_emulator.h"
#include "imgui.h"

namespace ps4 {

PerformanceOverlay::PerformanceOverlay(PS4Emulator* emulator)
    : emulator_(emulator) {}

void PerformanceOverlay::Render() {
    ImGui::SetNextWindowBgAlpha(0.3f);
    ImGuiWindowFlags window_flags = ImGuiWindowFlags_NoDecoration | ImGuiWindowFlags_AlwaysAutoResize |
                                    ImGuiWindowFlags_NoFocusOnAppearing | ImGuiWindowFlags_NoNav;
    ImGui::Begin("Performance Overlay", nullptr, window_flags);

    if (!emulator_) {
        ImGui::Text("Emulator not initialized");
        ImGui::End();
        return;
    }

    auto stats = emulator_->GetStats();

    ImGui::Text("FPS: %.1f", ImGui::GetIO().Framerate);
    ImGui::Text("Instructions Executed: %llu", stats.instructionsExecuted.load());
    ImGui::Text("Total Cycles: %llu", stats.totalCycles.load());
    ImGui::Text("Total Latency: %llu us", stats.totalLatencyUs.load());

    // Add more in-game metrics as needed

    ImGui::End();
}

} // namespace ps4
