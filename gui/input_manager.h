#ifndef INPUT_MANAGER_H
#define INPUT_MANAGER_H

#include "ps4_controllers.h"
#include <cstdint>

namespace ps4
{
    struct InputStats
    {
        uint64_t eventCount;
        uint64_t totalLatencyUs;
    };

    class InputManager
    {
    public:
        explicit InputManager(PS4ControllerManager& controllerManager);
        ~InputManager();

        bool Initialize();
        void Update();
        void PollEvents();  // Added declaration
        InputStats GetStats() const;
        ControllerState GetControllerState(int controllerIndex) const;
        bool IsControllerConnected(int controllerIndex) const;

    private:
        PS4ControllerManager& m_controllerManager;
        uint64_t m_eventCount;
        uint64_t m_totalLatencyUs;
    };
} // namespace ps4

#endif // INPUT_MANAGER_H