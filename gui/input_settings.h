#pragma once
#include <string>
#include <vector>

namespace ps4 {

class PS4Emulator;

class InputSettings {
public:
    InputSettings(PS4Emulator* emulator);
    void Render(bool* p_open);
    void Draw(bool* p_open) { Render(p_open); }

private:
    PS4Emulator* emulator_;

    // Add data structures for input mapping here
    // For example, vector of mappings, selected mapping index, etc.

    void LoadMappings();
    void SaveMappings();
};

} // namespace ps4
