#ifndef IMGUI_SAFE_H
#define IMGUI_SAFE_H

#include "imgui.h"
#include "debug/vector_debug.h"

// Safe wrapper for ImVec2 access
inline float& SafeImVec2Access(ImVec2& vec, size_t idx, const char* context) {
    if (idx >= 2) {
        std::string error_msg = std::string("ImVec2 index out of range in ") + context + 
                               ": index " + std::to_string(idx) + " >= 2";
        spdlog::error("IMGUI_DEBUG: {}", error_msg);
        throw std::out_of_range(error_msg);
    }
    return ((float*)&vec)[idx];
}

inline const float& SafeImVec2Access(const ImVec2& vec, size_t idx, const char* context) {
    if (idx >= 2) {
        std::string error_msg = std::string("ImVec2 index out of range in ") + context + 
                               ": index " + std::to_string(idx) + " >= 2";
        spdlog::error("IMGUI_DEBUG: {}", error_msg);
        throw std::out_of_range(error_msg);
    }
    return ((const float*)&vec)[idx];
}

// Macro for safe ImVec2 access
#define SAFE_IMVEC2_ACCESS(vec, idx, context) SafeImVec2Access(vec, idx, context)

#endif // IMGUI_SAFE_H