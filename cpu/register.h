#ifndef REGISTER_H
#define REGISTER_H

#include <cstdint>

namespace x86_64 {

enum class Register : uint8_t {
  // 64-bit general purpose registers
  RAX,
  RCX,
  RDX,
  RBX,
  RSP,
  RBP,
  RSI,
  RDI,
  R8,
  R9,
  R10,
  R11,
  R12,
  R13,
  R14,
  R15,
  RIP,

  // 32-bit general purpose registers
  EAX,
  ECX,
  EDX,
  EBX,
  ESP,
  EBP,
  ESI,
  EDI,
  R8D,
  R9D,
  R10D,
  R11D,
  R12D,
  R13D,
  R14D,
  R15D,

  // 16-bit general purpose registers
  AX,
  CX,
  DX,
  BX,
  SP,
  BP,
  SI,
  DI,
  R8W,
  R9W,
  R10W,
  R11W,
  R12W,
  R13W,
  R14W,
  R15W,

  // 8-bit general purpose registers
  AL,
  CL,
  DL,
  BL,
  AH,
  CH,
  DH,
  BH,
  SPL,
  BPL,
  SIL,
  DIL,
  R8B,
  R9B,
  R10B,
  R11B,
  R12B,
  R13B,
  R14B,
  R15B,

  // Segment registers
  ES,
  CS,
  SS,
  DS,
  FS,
  GS,

  // Control registers
  CR0,
  CR1,
  CR2,
  CR3,
  CR4,
  CR5,
  CR6,
  CR7,
  CR8,
  CR9,
  CR10,
  CR11,
  CR12,
  CR13,
  CR14,
  CR15,

  // Debug registers
  DR0,
  DR1,
  DR2,
  DR3,
  DR4,
  DR5,
  DR6,
  DR7,

  // FPU stack registers
  ST0,
  ST1,
  ST2,
  ST3,
  ST4,
  ST5,
  ST6,
  ST7,

  // MMX registers
  MM0,
  MM1,
  MM2,
  MM3,
  MM4,
  MM5,
  MM6,
  MM7,

  // XMM registers
  XMM0,
  XMM1,
  XMM2,
  XMM3,
  XMM4,
  XMM5,
  XMM6,
  XMM7,
  XMM8,
  XMM9,
  XMM10,
  XMM11,
  XMM12,
  XMM13,
  XMM14,
  XMM15,
  XMM16,
  XMM17,
  XMM18,
  XMM19,
  XMM20,
  XMM21,
  XMM22,
  XMM23,
  XMM24,
  XMM25,
  XMM26,
  XMM27,
  XMM28,
  XMM29,
  XMM30,
  XMM31,

  // YMM registers
  YMM0,
  YMM1,
  YMM2,
  YMM3,
  YMM4,
  YMM5,
  YMM6,
  YMM7,
  YMM8,
  YMM9,
  YMM10,
  YMM11,
  YMM12,
  YMM13,
  YMM14,
  YMM15,
  YMM16,
  YMM17,
  YMM18,
  YMM19,
  YMM20,
  YMM21,
  YMM22,
  YMM23,
  YMM24,
  YMM25,
  YMM26,
  YMM27,
  YMM28,
  YMM29,
  YMM30,
  YMM31,

  // ZMM registers
  ZMM0,
  ZMM1,
  ZMM2,
  ZMM3,
  ZMM4,
  ZMM5,
  ZMM6,
  ZMM7,
  ZMM8,
  ZMM9,
  ZMM10,
  ZMM11,
  ZMM12,
  ZMM13,
  ZMM14,
  ZMM15,
  ZMM16,
  ZMM17,
  ZMM18,
  ZMM19,
  ZMM20,
  ZMM21,
  ZMM22,
  ZMM23,
  ZMM24,
  ZMM25,
  ZMM26,
  ZMM27,
  ZMM28,
  ZMM29,
  ZMM30,
  ZMM31,

  // AVX512 mask registers
  K0,
  K1,
  K2,
  K3,
  K4,
  K5,
  K6,
  K7,

  // Special registers
  EFLAGS,
  RFLAGS,
  MXCSR,

  // Additional special registers
  GDTR, // Global Descriptor Table Register
  IDTR, // Interrupt Descriptor Table Register
  LDTR, // Local Descriptor Table Register
  TR,   // Task Register

  NONE // Placeholder for invalid register
};

// MSR (Model Specific Register) enum
enum class MSR : uint32_t {
  // Extended Feature Enable Register
  EFER = 0xC0000080,

  // SYSCALL/SYSRET registers
  STAR = 0xC0000081,   // SYSCALL target CS/SS
  LSTAR = 0xC0000082,  // SYSCALL target RIP (64-bit mode)
  CSTAR = 0xC0000083,  // SYSCALL target RIP (compatibility mode)
  SFMASK = 0xC0000084, // SYSCALL RFLAGS mask

  // Segment base registers
  FS_BASE = 0xC0000100, // FS segment base
  GS_BASE = 0xC0000101, // GS segment base
  KERNEL_GS_BASE = 0xC0000102, // Kernel GS base (swapped with GS_BASE on SWAPGS)

  // Performance monitoring
  TSC = 0x10, // Time Stamp Counter
  TSC_AUX = 0xC0000103, // Auxiliary TSC

  // APIC
  APIC_BASE = 0x1B, // APIC Base Address

  // Additional common MSRs
  PAT = 0x277,           // Page Attribute Table
  MTRR_CAP = 0xFE,       // MTRR Capabilities
  MTRR_DEF_TYPE = 0x2FF, // MTRR Default Type
  MTRR_PHYSBASE0 = 0x200, // MTRR Physical Base 0
  MTRR_PHYSMASK0 = 0x201, // MTRR Physical Mask 0

  // Debug and performance
  DEBUGCTL = 0x1D9,         // Debug Control
  LASTBRANCHFROMIP = 0x1DB, // Last Branch From IP
  LASTBRANCHTOIP = 0x1DC,   // Last Branch To IP
  LASTINTFROMIP = 0x1DD,    // Last Interrupt From IP
  LASTINTTOIP = 0x1DE,      // Last Interrupt To IP

  // Virtualization
  VMX_BASIC = 0x480,          // VMX Basic Information
  VMX_PINBASED_CTLS = 0x481,  // VMX Pin-Based Controls
  VMX_PROCBASED_CTLS = 0x482, // VMX Processor-Based Controls
  VMX_EXIT_CTLS = 0x483,      // VMX Exit Controls
  VMX_ENTRY_CTLS = 0x484,     // VMX Entry Controls
  VMX_MISC = 0x485,           // VMX Miscellaneous Data
  VMX_CR0_FIXED0 = 0x486,     // VMX CR0 Fixed 0
  VMX_CR0_FIXED1 = 0x487,     // VMX CR0 Fixed 1
  VMX_CR4_FIXED0 = 0x488,     // VMX CR4 Fixed 0
  VMX_CR4_FIXED1 = 0x489,     // VMX CR4 Fixed 1
  VMX_PROCBASED_CTLS2 = 0x48B, // VMX Secondary Processor-Based Controls

  // Additional MSRs for system management
  SYSENTER_CS = 0x174,  // SYSENTER CS
  SYSENTER_ESP = 0x175, // SYSENTER ESP
  SYSENTER_EIP = 0x176, // SYSENTER EIP
};

// Function to convert register enum to string for debugging
const char *RegisterToString(Register reg);

// Function to convert MSR enum to string for debugging
const char *MSRToString(MSR msr);

} // namespace x86_64

#endif // REGISTER_H