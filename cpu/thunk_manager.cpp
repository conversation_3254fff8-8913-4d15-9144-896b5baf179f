#include <chrono>
#include <iostream>
#include <stdexcept>
#include <string>
#include <utility>
#include <vector>

#include <spdlog/spdlog.h>

#include "../common/lock_ordering.h"
#include "../debug/vector_debug.h"
#include "cpu/thunk_manager.h"

namespace ps4 {
struct ThunkException : std::runtime_error {
  explicit ThunkException(const std::string &msg) : std::runtime_error(msg) {}
};

ThunkManager::ThunkManager() : m_nextThunkId(1) {
  m_thunks.emplace_back(ThunkEntry{"invalid", nullptr, false, 0, 0});
  spdlog::info("ThunkManager constructed");
}

ThunkManager::~ThunkManager() {
  Shutdown();
  spdlog::info("ThunkManager destructed");
}

bool ThunkManager::Initialize() {
  COMPONENT_LOCK(m_thunkMutex, "ThunkMutex");
  m_stats.clear();
  spdlog::info("ThunkManager initialized");
  return true;
}

void ThunkManager::Shutdown() {
  COMPONENT_LOCK(m_thunkMutex, "ThunkMutex");
  for (auto &entry : m_thunks) {
    if (entry.active) {
      entry.active = false;
      entry.function = nullptr;
      m_thunkNameMap.erase(entry.name);
    }
  }
  m_thunks.clear();
  m_thunkNameMap.clear();
  m_stats.clear();
  m_nextThunkId = 1;
  spdlog::info("ThunkManager shutdown");
}

uint64_t ThunkManager::RegisterThunkImpl(const std::string &name,
                                         ThunkFunction function,
                                         uint32_t expectedArgs) {
  COMPONENT_LOCK(m_thunkMutex, "ThunkMutex");
  if (name.empty() || !function) {
    std::string functionStr = function ? "true" : "false";
    spdlog::error("Invalid thunk registration: name='{}', function={}", name,
                  functionStr);
    return 0;
  }

  auto it = m_thunkNameMap.find(name);
  if (it != m_thunkNameMap.end()) {
    uint64_t existingId = it->second;
    if (existingId < m_thunks.size()) {
      // CRITICAL: Add bounds check for thunk vector access
      auto &existingThunk = SAFE_VECTOR_ACCESS(m_thunks, existingId, "RegisterThunk existing thunk");
      if (existingThunk.active) {
        spdlog::warn("Thunk '{}' already registered with ID {}", name,
                     existingId);
        existingThunk.refCount++;
        return existingId;
      }
    }
  }

  uint64_t thunkId = GenerateThunkId();
  // CRITICAL: Bounds check for vector resize
  if (thunkId >= m_thunks.size()) {
    if (thunkId > 1000000) { // Reasonable upper limit
      spdlog::error("RegisterThunk: thunkId {} exceeds maximum limit", thunkId);
      return 0;
    }
    m_thunks.resize(thunkId + 1, {"", nullptr, false, 0, 0});
  }

  // CRITICAL: Add bounds check for thunk vector access
  if (thunkId < m_thunks.size()) {
    SAFE_VECTOR_ACCESS(m_thunks, thunkId, "RegisterThunk set thunk") = {name, function, true, 1, expectedArgs};
  } else {
    spdlog::error("RegisterThunk: thunkId {} out of bounds (size={})", thunkId, m_thunks.size());
    return 0;
  }
  m_thunkNameMap[name] = thunkId;
  m_stats[thunkId] =
      std::make_pair(0, 0); // Initialize stats: {call_count, total_cycles}
  spdlog::info("Registered thunk '{}' with ID {}, args={}", name, thunkId,
               expectedArgs);
  return thunkId;
}

uint64_t ThunkManager::GenerateThunkId() {
  for (size_t i = 1; i < m_thunks.size(); ++i) {
    // CRITICAL: Add bounds check for thunk vector access
    auto &thunk = SAFE_VECTOR_ACCESS(m_thunks, i, "GenerateThunkId thunk check");
    if (!thunk.active) {
      return i;
    }
  }
  return m_nextThunkId++;
}

bool ThunkManager::UnregisterThunk(uint64_t thunkId) {
  COMPONENT_LOCK(m_thunkMutex, "ThunkMutex");
  if (thunkId == 0 || thunkId >= m_thunks.size()) {
    spdlog::error("Invalid thunk ID for unregistration: {}", thunkId);
    return false;
  }

  // CRITICAL: Add bounds check for thunk vector access
  auto &entry = SAFE_VECTOR_ACCESS(m_thunks, thunkId, "UnregisterThunk thunk entry");
  if (!entry.active) {
    spdlog::error("Inactive thunk ID for unregistration: {}", thunkId);
    return false;
  }
  if (entry.refCount > 1) {
    spdlog::warn("Thunk ID {} has {} references, decrementing", thunkId,
                 entry.refCount);
    entry.refCount--;
    return false;
  }

  m_thunkNameMap.erase(entry.name);
  m_stats.erase(thunkId);
  entry.active = false;
  entry.function = nullptr;
  entry.expectedArgs = 0;
  spdlog::info("Unregistered thunk '{}'", entry.name);
  return true;
}

// Type-safe CallThunk implementation
std::pair<uint64_t, ThunkError>
ThunkManager::CallThunk(uint64_t thunkId, const ThunkArgumentList &args) {
  ThunkFunction function;
  std::string thunkName;
  {
    ORDERED_SHARED_LOCK(m_thunkMutex, ps4::LockLevel::COMPONENTS, "ThunkMutex");
    if (thunkId == 0 || thunkId >= m_thunks.size()) {
      spdlog::error("Invalid thunk ID: {}", thunkId);
      return {0, ThunkError::InvalidId};
    }
    if (!m_thunks[thunkId].active) {
      spdlog::error("Inactive thunk ID: {}", thunkId);
      return {0, ThunkError::Inactive};
    }
    if (args.size() != m_thunks[thunkId].expectedArgs) {
      spdlog::error(
          "Argument count mismatch for thunk ID {}: expected {}, got {}",
          thunkId, m_thunks[thunkId].expectedArgs, args.size());
      return {0, ThunkError::InvalidArgs};
    }
    function = m_thunks[thunkId].function;
    thunkName = m_thunks[thunkId].name;
  }

  try {
    auto start = std::chrono::steady_clock::now();
    uint64_t result = function(args);
    auto end = std::chrono::steady_clock::now();
    uint64_t cycles =
        std::chrono::duration_cast<std::chrono::nanoseconds>(end - start)
            .count() /
        10;

    COMPONENT_LOCK(m_thunkMutex, "ThunkMutex");
    m_stats[thunkId].first++;          // Increment call_count
    m_stats[thunkId].second += cycles; // Add to total_cycles
    spdlog::trace("Called thunk '{}', ID {}, result=0x{:x}, cycles={}",
                  thunkName, thunkId, result, cycles);
    return {result, ThunkError::Success};
  } catch (const std::exception &e) {
    spdlog::error("Exception in thunk '{}': {}", thunkName, e.what());
    return {0, ThunkError::Exception};
  }
}

// Legacy CallThunk implementation for backward compatibility
std::pair<uint64_t, ThunkError>
ThunkManager::CallThunk(uint64_t thunkId, uint64_t *args, uint32_t argCount) {
  // CRITICAL: Bounds check for args array access
  if (!args && argCount > 0) {
    spdlog::error("CallThunk: null args pointer with argCount {}", argCount);
    return {0, ThunkError::InvalidArgs};
  }
  
  // Convert legacy args to type-safe format
  ThunkArgumentList safeArgs;
  safeArgs.reserve(argCount);
  for (uint32_t i = 0; i < argCount; ++i) {
    safeArgs.emplace_back(args[i]);
  }
  return CallThunk(thunkId, safeArgs);
}

uint64_t ThunkManager::GetThunkId(const std::string &name) const {
  ORDERED_SHARED_LOCK(m_thunkMutex, ps4::LockLevel::COMPONENTS, "ThunkMutex");
  auto it = m_thunkNameMap.find(name);
  uint64_t id = it != m_thunkNameMap.end() ? it->second : 0;
  spdlog::trace("Queried thunk '{}', ID={}", name, id);
  return id;
}

std::string ThunkManager::GetThunkName(uint64_t thunkId) const {
  ORDERED_SHARED_LOCK(m_thunkMutex, ps4::LockLevel::COMPONENTS, "ThunkMutex");
  if (thunkId == 0 || thunkId >= m_thunks.size() || !m_thunks[thunkId].active) {
    spdlog::trace("Queried invalid thunk ID {}", thunkId);
    return "";
  }
  spdlog::trace("Queried thunk ID {}, name='{}'", thunkId,
                m_thunks[thunkId].name);
  return m_thunks[thunkId].name;
}

std::vector<std::string> ThunkManager::ListThunks() const {
  ORDERED_SHARED_LOCK(m_thunkMutex, ps4::LockLevel::COMPONENTS, "ThunkMutex");
  std::vector<std::string> thunkNames;
  for (const auto &entry : m_thunks) {
    if (entry.active) {
      thunkNames.push_back(entry.name);
    }
  }
  spdlog::trace("Listed {} active thunks", thunkNames.size());
  return thunkNames;
}

void ThunkManager::SaveState(std::ostream &out) const {
  ORDERED_SHARED_LOCK(m_thunkMutex, ps4::LockLevel::COMPONENTS, "ThunkMutex");
  uint64_t activeCount = 0;
  for (const auto &entry : m_thunks) {
    if (entry.active)
      activeCount++;
  }
  out.write(reinterpret_cast<const char *>(&activeCount), sizeof(activeCount));
  for (size_t i = 0; i < m_thunks.size(); ++i) {
    const auto &entry = m_thunks[i];
    if (entry.active) {
      uint32_t nameLen = static_cast<uint32_t>(entry.name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(uint32_t));
      out.write(entry.name.data(), nameLen);
      out.write(reinterpret_cast<const char *>(&entry.refCount),
                sizeof(entry.refCount));
      out.write(reinterpret_cast<const char *>(&entry.expectedArgs),
                sizeof(entry.expectedArgs));
      auto stats = m_stats.find(i);
      uint64_t callCount = stats != m_stats.end() ? stats->second.first : 0;
      uint64_t totalCycles = stats != m_stats.end() ? stats->second.second : 0;
      out.write(reinterpret_cast<const char *>(&callCount), sizeof(callCount));
      out.write(reinterpret_cast<const char *>(&totalCycles),
                sizeof(totalCycles));
    }
  }
  uint64_t regionCount = m_thunks.size();
  out.write(reinterpret_cast<const char *>(&regionCount), sizeof(regionCount));

  spdlog::info("ThunkManager state saved: {} active thunks", activeCount);
}

void ThunkManager::LoadState(std::istream &in) {
  COMPONENT_LOCK(m_thunkMutex, "ThunkMutex");
  uint64_t activeCount;
  in.read(reinterpret_cast<char *>(&activeCount), sizeof(activeCount));
  m_thunks.clear();
  m_thunkNameMap.clear();
  m_stats.clear();
  m_nextThunkId = 1;

  m_thunks.emplace_back(ThunkEntry{"invalid", nullptr, false, 0, 0});

  for (uint64_t i = 0; i < activeCount && in.good(); ++i) {
    uint32_t nameLen;
    in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
    
    // CRITICAL: String length bounds check
    if (nameLen > 1024) { // Reasonable string length limit
      spdlog::error("LoadState: nameLen {} exceeds maximum", nameLen);
      throw ThunkException("Invalid name length in state data");
    }
    
    std::string name(nameLen, '\0');
    in.read(name.data(), nameLen);
    uint32_t refCount = 0, expectedArgs = 0;
    in.read(reinterpret_cast<char *>(&refCount), sizeof(refCount));
    in.read(reinterpret_cast<char *>(&expectedArgs), sizeof(expectedArgs));
    uint64_t callCount, totalCycles;
    in.read(reinterpret_cast<char *>(&callCount), sizeof(callCount));
    in.read(reinterpret_cast<char *>(&totalCycles), sizeof(totalCycles));
    uint64_t thunkId = GenerateThunkId();
    if (thunkId >= m_thunks.size())
      m_thunks.resize(thunkId + 1, ThunkEntry{"", nullptr, false, 0, 0});
    m_thunks[thunkId] = {name, nullptr, true, refCount, expectedArgs};
    m_thunkNameMap[name] = thunkId;
    m_stats[thunkId] = std::make_pair(callCount, totalCycles);
    spdlog::trace("Loaded thunk '{}', ID {}, calls={}, cycles={}", name,
                  thunkId, callCount, totalCycles);
  }
  spdlog::info("ThunkManager state loaded: {} active thunks", activeCount);
}
} // namespace ps4