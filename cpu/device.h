#pragma once

#include <cstdint>
#include <string>
#include <istream>
#include <ostream>

namespace x86_64 {

/**
 * @brief Abstract base class for emulated devices.
 */
class Device {
public:
  virtual ~Device() = default;

  /**
   * @brief Writes data to the device.
   * @param address Device register address.
   * @param value Value to write.
   * @param size Size of the write operation in bytes.
   */
  virtual void Write(uint64_t address, uint64_t value, uint8_t size) = 0;

  /**
   * @brief Reads data from the device.
   * @param address Device register address.
   * @param size Size of the read operation in bytes.
   * @return The read value.
   */
  virtual uint64_t Read(uint64_t address, uint8_t size) = 0;

  /**
   * @brief Resets the device to its initial state.
   */
  virtual void Reset() = 0;

  /**
   * @brief Gets the device name.
   * @return Device name string.
   */
  virtual std::string GetName() const = 0;

  /**
   * @brief Saves device state to output stream.
   * @param out Output stream.
   */
  virtual void SaveState(std::ostream &out) const = 0;

  /**
   * @brief Loads device state from input stream.
   * @param in Input stream.
   */
  virtual void LoadState(std::istream &in) = 0;
};

} // namespace x86_64
