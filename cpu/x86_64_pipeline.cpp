// MultipleFiles/x86_64_pipeline.cpp
// Copyright 2025 <Copyright Owner>

// Add these includes at the top of the file
#include "x86_64_pipeline.h"
#include "../common/lock_ordering.h"
#include "../debug/vector_debug.h"
#include "decoded_instruction.h"
#include "cpu_diagnostics.h"
#include "register.h"

#include "../memory/ps4_mmu.h"
#include "../jit/jit_cache.h"
#include "../jit/jit_translator.h"
#include "../jit/x86_64_jit_compiler.h"
#include <spdlog/spdlog.h>

// All your pipeline methods should be within proper class scope:
#include "x86_64_cpu.h"

namespace x86_64 {

x86_64::BranchPredictor::BranchPredictor()
    : m_local_history_table(LOCAL_HISTORY_TABLE_SIZE, 0),
      m_local_pattern_table(LOCAL_PATTERN_TABLE_SIZE, 1), // Weakly not taken
      m_global_pattern_table(PATTERN_TABLE_SIZE, 1),
      m_choice_table(CHOICE_TABLE_SIZE, 1), // Initially prefer local
      m_global_history(0), m_btb(BTB_SIZE), m_ras(RAS_SIZE, 0), m_ras_top(0),
      m_hits(0), m_misses(0) {
  m_detailed_stats = {};
  spdlog::info(
      "Two-level adaptive branch predictor initialized: "
      "Local tables: {}, Global table: {}, Choice table: {}, BTB: {}, RAS: {}",
      LOCAL_HISTORY_TABLE_SIZE, PATTERN_TABLE_SIZE, CHOICE_TABLE_SIZE, BTB_SIZE,
      RAS_SIZE);
}

bool x86_64::BranchPredictor::PredictTaken(uint64_t pc) {
  size_t choice_index = GetChoiceIndex(pc);
  // 0,1 = local, 2,3 = global. So if < 2, use local.
  bool use_local = m_choice_table[choice_index] < 2;

  bool local_prediction = false;
  bool global_prediction = false;

  // Get local prediction
  size_t local_pattern_index = GetLocalPatternIndex(pc);
  if (local_pattern_index < m_local_pattern_table.size()) { // Bounds check
    local_prediction = m_local_pattern_table[local_pattern_index] >= 2;
  }

  // Get global prediction
  size_t global_pattern_index = GetGlobalPatternIndex();
  global_prediction = m_global_pattern_table[global_pattern_index] >= 2;

  // Update selection stats
  if (use_local) {
    m_detailed_stats.choice_local_selected++;
    return local_prediction;
  } else {
    m_detailed_stats.choice_global_selected++;
    return global_prediction;
  }
}

void x86_64::BranchPredictor::Update(uint64_t pc, bool taken) {
  size_t choice_index = GetChoiceIndex(pc);
  size_t local_pattern_index = GetLocalPatternIndex(pc);
  size_t global_pattern_index = GetGlobalPatternIndex();

  // CRITICAL: Bounds checks for array access
  if (choice_index >= m_choice_table.size() || 
      local_pattern_index >= m_local_pattern_table.size() ||
      global_pattern_index >= m_global_pattern_table.size()) {
    spdlog::error("BranchPredictor::Update: index out of bounds");
    return;
  }

  // Get predictions before update
  bool local_prediction = m_local_pattern_table[local_pattern_index] >= 2;
  bool global_prediction = m_global_pattern_table[global_pattern_index] >= 2;
  bool local_correct = (local_prediction == taken);
  bool global_correct = (global_prediction == taken);

  // Update choice predictor based on which was more accurate
  if (local_correct && !global_correct) {
    // Local was better, strengthen local preference
    if (m_choice_table[choice_index] > 0) {
      m_choice_table[choice_index]--;
    }
  } else if (!local_correct && global_correct) {
    // Global was better, strengthen global preference
    if (m_choice_table[choice_index] < 3) {
      m_choice_table[choice_index]++;
    }
  }

  // Update local predictor
  UpdateCounter(m_local_pattern_table[local_pattern_index], taken);

  // Update global predictor
  UpdateCounter(m_global_pattern_table[global_pattern_index], taken);

  // Update local history for this PC
  size_t local_history_index = GetLocalHistoryIndex(pc);
  m_local_history_table[local_history_index] =
      ((m_local_history_table[local_history_index] << 1) | (taken ? 1 : 0)) &
      ((1 << LOCAL_HISTORY_BITS) - 1);

  // Update global history
  m_global_history = ((m_global_history << 1) | (taken ? 1 : 0)) &
                     ((1 << GLOBAL_HISTORY_BITS) - 1);

  // Update statistics
  if (local_correct) {
    m_detailed_stats.local_correct++;
  } else {
    m_detailed_stats.local_incorrect++;
  }

  if (global_correct) {
    m_detailed_stats.global_correct++;
  } else {
    m_detailed_stats.global_incorrect++;
  }
}

uint64_t x86_64::BranchPredictor::PredictTarget(uint64_t pc,
                                        const DecodedInstruction &instr,
                                        uint64_t fallthrough) {
  // Handle different branch types
  using InstructionType = x86_64::InstructionType;
  switch (instr.instType) {
  case InstructionType::Ret: {
    // Use return address stack
    if (m_ras_top > 0) {
      // Pop from RAS, decrement m_ras_top
      m_ras_top--;
      size_t index = m_ras_top % RAS_SIZE; // Use m_ras_top after decrement
      // CRITICAL: Bounds check for RAS access
      if (index >= m_ras.size()) {
        spdlog::error("BranchPredictor: RAS index {} out of bounds", index);
        return 0;
      }
      return m_ras[index];
    }
    return 0; // Unknown return target, will likely cause a fault
  }

  case InstructionType::Call: {
    // Push return address onto RAS and predict call target
    size_t ras_index = m_ras_top % RAS_SIZE;
    // CRITICAL: Bounds check for RAS access
    if (ras_index >= m_ras.size()) {
      spdlog::error("BranchPredictor: RAS push index {} out of bounds", ras_index);
      return 0;
    }
    m_ras[ras_index] = fallthrough;
    m_ras_top++; // Increment m_ras_top after push

    // For direct calls, calculate target from immediate
    if (instr.operands[0].type ==
        DecodedInstruction::Operand::Type::IMMEDIATE) {
      return fallthrough + instr.operands[0].immediate;
    }

    // For indirect calls, check BTB
    size_t btb_index = GetBTBIndex(pc);
    // CRITICAL: Bounds check for BTB access
    if (btb_index >= m_btb.size()) {
      spdlog::error("BranchPredictor: BTB index {} out of bounds", btb_index);
      return 0;
    }
    auto &entry = m_btb[btb_index];
    // Check if BTB entry is valid, matches tag, and has sufficient confidence
    if (entry.valid && entry.tag == (pc & 0xFFFFF) && entry.confidence >= 2) {
      return entry.target;
    }
    return 0; // Unknown indirect target, will likely cause a fault
  }

  case InstructionType::Jump: {
    // Direct jump
    if (instr.operands[0].type ==
        DecodedInstruction::Operand::Type::IMMEDIATE) {
      return fallthrough + instr.operands[0].immediate;
    }

    // Indirect jump - check BTB
    size_t btb_index = GetBTBIndex(pc);
    // CRITICAL: Bounds check for BTB access
    if (btb_index >= m_btb.size()) {
      spdlog::error("BranchPredictor: BTB index {} out of bounds", btb_index);
      return 0;
    }
    auto &entry = m_btb[btb_index];
    if (entry.valid && entry.tag == (pc & 0xFFFFF) && entry.confidence >= 2) {
      return entry.target;
    }
    return 0; // Unknown indirect target, will likely cause a fault
  }

  case InstructionType::Jcc: {
    // Conditional branch - direct displacement
    // The prediction (taken/not taken) is handled by PredictTaken().
    // This function only provides the target if it *is* taken.
    return fallthrough + instr.operands[0].immediate;
  }

  default:
    return fallthrough; // Non-branch instructions fall through
  }
}

void x86_64::BranchPredictor::UpdateTarget(uint64_t pc, uint64_t actual_target) {
  size_t btb_index = GetBTBIndex(pc);
  auto &entry = m_btb[btb_index];

  if (entry.valid && entry.tag == (pc & 0xFFFFF)) {
    // Update existing entry
    if (entry.target == actual_target) {
      // Correct prediction, increase confidence
      if (entry.confidence < 3) { // Max confidence is 3 (2-bit counter)
        entry.confidence++;
      }
      m_detailed_stats.target_correct++;
    } else {
      // Incorrect prediction, update target and reset confidence
      entry.target = actual_target;
      entry.confidence = 1; // Reset to weakly taken/not taken
      m_detailed_stats.target_incorrect++;
    }
  } else {
    // Install new entry
    entry.valid = true;
    entry.tag = pc & 0xFFFFF; // Use lower bits of PC as tag
    entry.target = actual_target;
    entry.confidence = 1; // Initialize to weakly taken/not taken
  }
}

void x86_64::BranchPredictor::RecordPrediction(bool predicted, bool actual) {
  (predicted == actual) ? m_hits++ : m_misses++;
}

void x86_64::BranchPredictor::ResetStats() {
  m_hits = 0;
  m_misses = 0;
  // Reset pattern tables to weakly not taken (1) or weakly taken (2)
  // A common initialization is 2 (weakly taken) for 2-bit saturating counters.
  std::fill(m_local_pattern_table.begin(), m_local_pattern_table.end(), 2);
  std::fill(m_global_pattern_table.begin(), m_global_pattern_table.end(), 2);
  std::fill(m_choice_table.begin(), m_choice_table.end(),
            1); // Initially prefer local (1)
  m_global_history = 0;
  m_local_history_table.assign(LOCAL_HISTORY_TABLE_SIZE, 0);
  m_btb.assign(BTB_SIZE, BTBEntry{}); // Reset BTB entries
  m_ras.assign(RAS_SIZE, 0);          // Clear RAS
  m_ras_top = 0;                      // Reset RAS pointer
  m_detailed_stats = {};              // Reset detailed stats
}

// Helper methods for two-level predictor
size_t x86_64::BranchPredictor::GetLocalHistoryIndex(uint64_t pc) const {
  // Use lower bits of PC to index into local history table
  return (pc >> 2) & (LOCAL_HISTORY_TABLE_SIZE - 1);
}

size_t x86_64::BranchPredictor::GetGlobalPatternIndex() const {
  // Use global history register to index into global pattern table
  return m_global_history & (PATTERN_TABLE_SIZE - 1);
}

size_t x86_64::BranchPredictor::GetLocalPatternIndex(uint64_t pc) const {
  // Combine local history with PC to get pattern table index
  size_t local_history_index = GetLocalHistoryIndex(pc);
  uint16_t local_history = m_local_history_table[local_history_index];
  // Combine local history with lower bits of PC for better distribution
  return (local_history ^ (pc >> 2)) & (LOCAL_PATTERN_TABLE_SIZE - 1);
}

size_t x86_64::BranchPredictor::GetChoiceIndex(uint64_t pc) const {
  // Combine global history with lower bits of PC for choice table index
  return ((pc >> 2) ^ m_global_history) & (CHOICE_TABLE_SIZE - 1);
}

size_t x86_64::BranchPredictor::GetBTBIndex(uint64_t pc) const {
  // Use lower bits of PC to index into BTB
  return (pc >> 2) & (BTB_SIZE - 1);
}

void x86_64::BranchPredictor::UpdateCounter(uint8_t &counter, bool taken,
                                    uint8_t max_val) {
  if (taken) {
    if (counter < max_val) { // Use max_val here
      counter++;
    }
  } else {
    if (counter > 0) {
      counter--;
    }
  }
}

bool x86_64::BranchPredictor::IsIndirectBranch(const DecodedInstruction &instr) const {
  using InstructionType = x86_64::InstructionType;
  if (instr.instType == InstructionType::Jump ||
      instr.instType == InstructionType::Call) {
    // An indirect jump/call has a non-immediate operand (e.g., register,
    // memory)
    return instr.operands[0].type !=
           DecodedInstruction::Operand::Type::IMMEDIATE;
  }
  // RET is always an indirect branch (target from stack)
  return instr.instType == InstructionType::Ret;
}

void x86_64::BranchPredictor::PushReturnAddress(uint64_t return_addr) {
  m_ras[m_ras_top % RAS_SIZE] = return_addr;
  m_ras_top = (m_ras_top + 1) % RAS_SIZE;
}

uint64_t x86_64::BranchPredictor::PopReturnAddress() {
  if (m_ras_top > 0) {
    m_ras_top--;
    return m_ras[m_ras_top % RAS_SIZE];
  }
  return 0; // Empty stack
}

double x86_64::BranchPredictor::GetAccuracy() const {
  uint64_t total = m_hits + m_misses;
  if (total == 0) return 0.0;
  return static_cast<double>(m_hits) / total;
}

Pipeline::Pipeline(X86_64CPU &cpu_ref, ps4::PS4MMU& memory, jit::JitCache& jitCache, jit::JitTranslator& translator)
    : cpu(cpu_ref), memory_(memory), jitCache_(jitCache), 
      translator_(translator), cpuId_(0), pc_(0), decoder(),
      jit(std::make_unique<X86_64JITCompiler>(&cpu_ref)), branchPredictor(),
      reorderBuffer(), optimizationHints(),
      execution_units_count(4), contentionCounter(0), m_hits(0), m_misses(0) {
  ResetStats();
  fetchStage.reserve(8);
  decodeStage.reserve(8);
  executeStage.reserve(4);
  memoryStage.reserve(4);
  writeBackStage.reserve(4);

  InitializeExecutionUnits();
  InitializeOpcodeDispatchTable();
  InitializeRegisterDependencyTracking();
  InitializeReservationStations();

  spdlog::info("Pipeline initialized for CPU at 0x{:x} with {} execution units "
               "and register dependency tracking",
               reinterpret_cast<uintptr_t>(&cpu_ref), execution_units.size());
}

void Pipeline::InitializeExecutionUnits() {
  execution_units.clear();
  unit_map.clear();

  // Create execution units
  // Note: The number and type of units here define the structural hazards.
  // For example, two ALUs, one Load/Store unit, etc.
  execution_units.emplace_back(ExecutionUnitType::ALU);        // ALU 0
  execution_units.emplace_back(ExecutionUnitType::ALU);        // ALU 1
  execution_units.emplace_back(ExecutionUnitType::LOAD_STORE); // Load/Store
  execution_units.emplace_back(ExecutionUnitType::FPU);        // FPU
  execution_units.emplace_back(ExecutionUnitType::SIMD);       // SIMD
  execution_units.emplace_back(ExecutionUnitType::BRANCH);     // Branch
  execution_units.emplace_back(ExecutionUnitType::DIVIDE);     // Divide

  // Build unit mapping
  for (size_t i = 0; i < execution_units.size(); ++i) {
    // CRITICAL: Add bounds check for execution units access
    const auto &unit = SAFE_VECTOR_ACCESS(execution_units, i, "Pipeline execution unit mapping");
    unit_map[unit.type].push_back(i);
  }

  spdlog::info("Initialized {} execution units", execution_units.size());
}

void Pipeline::InitializeOpcodeDispatchTable() {
  using InstructionType = x86_64::InstructionType;

  // Initialize dispatch table with instruction handlers
  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Mov)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      // Use safe operand access for both operands
      const DecodedInstruction::Operand *dest = SafeGetOperand(exec.instr, 0);
      const DecodedInstruction::Operand *src = SafeGetOperand(exec.instr, 1);
      if (!dest || !src) {
        spdlog::error("MOV: Failed to access operands at PC 0x{:x}",
                      exec.instr.pc);
        return;
      }

      if (dest->type == DecodedInstruction::Operand::Type::REGISTER &&
          src->type == DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(dest->reg, cpu.GetRegister(src->reg));
      } else if (dest->type == DecodedInstruction::Operand::Type::REGISTER &&
                 src->type == DecodedInstruction::Operand::Type::IMMEDIATE) {
        cpu.SetRegister(dest->reg, src->immediate);
      }
      // Add more MOV variants as needed
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Add)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t op1 = cpu.GetRegister(exec.instr.operands[0].reg);
      uint64_t op2 = (exec.instr.operands[1].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[1].reg)
                         : exec.instr.operands[1].immediate;
      uint64_t result = op1 + op2;
      cpu.SetRegister(exec.instr.operands[0].reg, result);
      // Update flags as needed
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Sub)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t op1 = cpu.GetRegister(exec.instr.operands[0].reg);
      uint64_t op2 = (exec.instr.operands[1].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[1].reg)
                         : exec.instr.operands[1].immediate;
      uint64_t result = op1 - op2;
      cpu.SetRegister(exec.instr.operands[0].reg, result);
      // Update flags as needed
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Nop)] = [](ExecuteStage &exec) {
    // NOP does nothing
  };

  // Core Arithmetic Instructions
  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Mul)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 1) {
      uint64_t op1 = cpu.GetRegister(Register::RAX);
      uint64_t op2 = (exec.instr.operands[0].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[0].reg)
                         : exec.instr.operands[0].immediate;

      uint8_t size = exec.instr.operands[0].size;
      if (size == 8) {
        uint16_t result = static_cast<uint8_t>(op1) * static_cast<uint8_t>(op2);
        cpu.SetRegister(
            Register::RAX,
            (cpu.GetRegister(Register::RAX) & 0xFFFFFFFFFFFF0000ULL) | result);
        cpu.SetFlag(FLAG_CF, result > 0xFF);
        cpu.SetFlag(FLAG_OF, result > 0xFF);
      } else if (size == 16) {
        uint32_t result =
            static_cast<uint16_t>(op1) * static_cast<uint16_t>(op2);
        cpu.SetRegister(Register::RAX, (cpu.GetRegister(Register::RAX) &
                                        0xFFFFFFFFFFFF0000ULL) |
                                           (result & 0xFFFF));
        cpu.SetRegister(Register::RDX, (cpu.GetRegister(Register::RDX) &
                                        0xFFFFFFFFFFFF0000ULL) |
                                           ((result >> 16) & 0xFFFF));
        cpu.SetFlag(FLAG_CF, (result >> 16) != 0);
        cpu.SetFlag(FLAG_OF, (result >> 16) != 0);
      } else if (size == 32) {
        uint64_t result =
            static_cast<uint32_t>(op1) * static_cast<uint32_t>(op2);
        cpu.SetRegister(Register::RAX, result & 0xFFFFFFFF);
        cpu.SetRegister(Register::RDX, (result >> 32) & 0xFFFFFFFF);
        cpu.SetFlag(FLAG_CF, (result >> 32) != 0);
        cpu.SetFlag(FLAG_OF, (result >> 32) != 0);
      } else { // 64-bit
        // Use 64-bit multiplication with overflow detection
        uint64_t high = 0;
        uint64_t low = op1 * op2;

        // Check for overflow by doing partial multiplication
        if (op1 != 0 && low / op1 != op2) {
          // Overflow occurred, calculate high part
          uint64_t op1_high = op1 >> 32;
          uint64_t op1_low = op1 & 0xFFFFFFFF;
          uint64_t op2_high = op2 >> 32;
          uint64_t op2_low = op2 & 0xFFFFFFFF;

          uint64_t cross1 = op1_low * op2_high;
          uint64_t cross2 = op1_high * op2_low;
          uint64_t middle = cross1 + cross2;

          high = op1_high * op2_high + (middle >> 32);
          if (middle < cross1)
            high++; // Carry from middle addition

          uint64_t low_part = (middle << 32) + (op1_low * op2_low);
          if (low_part < (middle << 32))
            high++; // Carry from low addition
          low = low_part;
        }

        cpu.SetRegister(Register::RAX, low);
        cpu.SetRegister(Register::RDX, high);
        cpu.SetFlag(FLAG_CF, high != 0);
        cpu.SetFlag(FLAG_OF, high != 0);
      }
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Imul)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount == 1) {
      // Single operand IMUL (signed multiply with RAX)
      int64_t op1 = static_cast<int64_t>(cpu.GetRegister(Register::RAX));
      int64_t op2 =
          (exec.instr.operands[0].type ==
           DecodedInstruction::Operand::Type::REGISTER)
              ? static_cast<int64_t>(
                    cpu.GetRegister(exec.instr.operands[0].reg))
              : static_cast<int64_t>(exec.instr.operands[0].immediate);

      uint8_t size = exec.instr.operands[0].size;
      if (size == 8) {
        int16_t result = static_cast<int8_t>(op1) * static_cast<int8_t>(op2);
        cpu.SetRegister(Register::RAX, (cpu.GetRegister(Register::RAX) &
                                        0xFFFFFFFFFFFF0000ULL) |
                                           (result & 0xFFFF));
        cpu.SetFlag(FLAG_CF, result != static_cast<int8_t>(result));
        cpu.SetFlag(FLAG_OF, result != static_cast<int8_t>(result));
      } else if (size == 16) {
        int32_t result = static_cast<int16_t>(op1) * static_cast<int16_t>(op2);
        cpu.SetRegister(Register::RAX, (cpu.GetRegister(Register::RAX) &
                                        0xFFFFFFFFFFFF0000ULL) |
                                           (result & 0xFFFF));
        cpu.SetRegister(Register::RDX, (cpu.GetRegister(Register::RDX) &
                                        0xFFFFFFFFFFFF0000ULL) |
                                           ((result >> 16) & 0xFFFF));
        cpu.SetFlag(FLAG_CF, result != static_cast<int16_t>(result));
        cpu.SetFlag(FLAG_OF, result != static_cast<int16_t>(result));
      } else if (size == 32) {
        int64_t result = static_cast<int32_t>(op1) * static_cast<int32_t>(op2);
        cpu.SetRegister(Register::RAX, result & 0xFFFFFFFF);
        cpu.SetRegister(Register::RDX, (result >> 32) & 0xFFFFFFFF);
        cpu.SetFlag(FLAG_CF, result != static_cast<int32_t>(result));
        cpu.SetFlag(FLAG_OF, result != static_cast<int32_t>(result));
      } else { // 64-bit
        // Use signed 64-bit multiplication with overflow detection
        int64_t sop1 = static_cast<int64_t>(op1);
        int64_t sop2 = static_cast<int64_t>(op2);

        // Simple overflow check for signed multiplication
        bool overflow = false;
        if (sop1 != 0) {
          if ((sop2 > 0 && sop1 > INT64_MAX / sop2) ||
              (sop2 < 0 && sop1 < INT64_MAX / sop2)) {
            overflow = true;
          }
        }

        int64_t result = sop1 * sop2;
        cpu.SetRegister(Register::RAX, static_cast<uint64_t>(result));
        cpu.SetRegister(Register::RDX,
                        overflow ? (result < 0 ? 0xFFFFFFFFFFFFFFFFULL : 0)
                                 : 0);
        cpu.SetFlag(FLAG_CF, overflow);
        cpu.SetFlag(FLAG_OF, overflow);
      }
    } else if (exec.instr.operandCount == 2) {
      // Two operand IMUL (reg = reg * imm/reg)
      int64_t op1 =
          (exec.instr.operands[0].type ==
           DecodedInstruction::Operand::Type::REGISTER)
              ? static_cast<int64_t>(
                    cpu.GetRegister(exec.instr.operands[0].reg))
              : static_cast<int64_t>(exec.instr.operands[0].immediate);
      int64_t op2 =
          (exec.instr.operands[1].type ==
           DecodedInstruction::Operand::Type::REGISTER)
              ? static_cast<int64_t>(
                    cpu.GetRegister(exec.instr.operands[1].reg))
              : static_cast<int64_t>(exec.instr.operands[1].immediate);

      int64_t result = op1 * op2;
      cpu.SetRegister(exec.instr.operands[0].reg,
                      static_cast<uint64_t>(result));

      // Set CF and OF if result doesn't fit in operand size
      uint8_t size = exec.instr.operands[0].size;
      bool overflow = false;
      if (size == 8)
        overflow = (result != static_cast<int8_t>(result));
      else if (size == 16)
        overflow = (result != static_cast<int16_t>(result));
      else if (size == 32)
        overflow = (result != static_cast<int32_t>(result));

      cpu.SetFlag(FLAG_CF, overflow);
      cpu.SetFlag(FLAG_OF, overflow);
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Div)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 1) {
      // Use safe operand access
      const DecodedInstruction::Operand *operand =
          SafeGetOperand(exec.instr, 0);
      if (!operand) {
        spdlog::error("DIV: Failed to access operand 0 at PC 0x{:x}",
                      exec.instr.pc);
        return;
      }

      uint64_t divisor =
          (operand->type == DecodedInstruction::Operand::Type::REGISTER)
              ? cpu.GetRegister(operand->reg)
              : operand->immediate;

      if (divisor == 0) {
        // Division by zero exception
        cpu.TriggerInterrupt(EXC_DE, 0, false);
        return;
      }

      uint8_t size = operand->size;
      if (size == 8) {
        uint16_t dividend = cpu.GetRegister(Register::RAX) & 0xFFFF;
        uint8_t quotient = dividend / static_cast<uint8_t>(divisor);
        uint8_t remainder = dividend % static_cast<uint8_t>(divisor);
        cpu.SetRegister(
            Register::RAX,
            (cpu.GetRegister(Register::RAX) & 0xFFFFFFFFFFFF0000ULL) |
                (static_cast<uint16_t>(remainder) << 8) | quotient);
      } else if (size == 16) {
        uint32_t dividend = ((cpu.GetRegister(Register::RDX) & 0xFFFF) << 16) |
                            (cpu.GetRegister(Register::RAX) & 0xFFFF);
        uint16_t quotient = dividend / static_cast<uint16_t>(divisor);
        uint16_t remainder = dividend % static_cast<uint16_t>(divisor);
        cpu.SetRegister(Register::RAX, (cpu.GetRegister(Register::RAX) &
                                        0xFFFFFFFFFFFF0000ULL) |
                                           quotient);
        cpu.SetRegister(Register::RDX, (cpu.GetRegister(Register::RDX) &
                                        0xFFFFFFFFFFFF0000ULL) |
                                           remainder);
      } else if (size == 32) {
        uint64_t dividend =
            (static_cast<uint64_t>(cpu.GetRegister(Register::RDX) & 0xFFFFFFFF)
             << 32) |
            (cpu.GetRegister(Register::RAX) & 0xFFFFFFFF);
        uint32_t quotient = dividend / static_cast<uint32_t>(divisor);
        uint32_t remainder = dividend % static_cast<uint32_t>(divisor);
        cpu.SetRegister(Register::RAX, quotient);
        cpu.SetRegister(Register::RDX, remainder);
      } else { // 64-bit
        // For 64-bit division, we need to handle 128-bit dividend
        uint64_t dividend_high = cpu.GetRegister(Register::RDX);
        uint64_t dividend_low = cpu.GetRegister(Register::RAX);

        // Simple case: if high part is 0, just do regular division
        if (dividend_high == 0) {
          uint64_t quotient = dividend_low / divisor;
          uint64_t remainder = dividend_low % divisor;
          cpu.SetRegister(Register::RAX, quotient);
          cpu.SetRegister(Register::RDX, remainder);
        } else {
          // Complex case: implement 128-bit division
          // For now, trigger division overflow exception if high part is
          // non-zero and would cause quotient to exceed 64 bits
          if (dividend_high >= divisor) {
            cpu.TriggerInterrupt(EXC_DE, 0, false);
            return;
          }

          // Simplified 128-bit division (not fully accurate but functional)
          uint64_t quotient = dividend_low / divisor;
          uint64_t remainder = dividend_low % divisor;
          cpu.SetRegister(Register::RAX, quotient);
          cpu.SetRegister(Register::RDX, remainder);
        }
      }
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Idiv)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 1) {
      // Use safe operand access
      const DecodedInstruction::Operand *operand =
          SafeGetOperand(exec.instr, 0);
      if (!operand) {
        spdlog::error("IDIV: Failed to access operand 0 at PC 0x{:x}",
                      exec.instr.pc);
        return;
      }

      int64_t divisor =
          (operand->type == DecodedInstruction::Operand::Type::REGISTER)
              ? static_cast<int64_t>(cpu.GetRegister(operand->reg))
              : static_cast<int64_t>(operand->immediate);

      if (divisor == 0) {
        // Division by zero exception
        cpu.TriggerInterrupt(EXC_DE, 0, false);
        return;
      }

      uint8_t size = operand->size;
      if (size == 8) {
        int16_t dividend =
            static_cast<int16_t>(cpu.GetRegister(Register::RAX) & 0xFFFF);
        int8_t quotient = dividend / static_cast<int8_t>(divisor);
        int8_t remainder = dividend % static_cast<int8_t>(divisor);
        cpu.SetRegister(
            Register::RAX,
            (cpu.GetRegister(Register::RAX) & 0xFFFFFFFFFFFF0000ULL) |
                (static_cast<uint16_t>(static_cast<uint8_t>(remainder)) << 8) |
                static_cast<uint8_t>(quotient));
      } else if (size == 16) {
        int32_t dividend = static_cast<int32_t>(
            ((cpu.GetRegister(Register::RDX) & 0xFFFF) << 16) |
            (cpu.GetRegister(Register::RAX) & 0xFFFF));
        int16_t quotient = dividend / static_cast<int16_t>(divisor);
        int16_t remainder = dividend % static_cast<int16_t>(divisor);
        cpu.SetRegister(Register::RAX, (cpu.GetRegister(Register::RAX) &
                                        0xFFFFFFFFFFFF0000ULL) |
                                           static_cast<uint16_t>(quotient));
        cpu.SetRegister(Register::RDX, (cpu.GetRegister(Register::RDX) &
                                        0xFFFFFFFFFFFF0000ULL) |
                                           static_cast<uint16_t>(remainder));
      } else if (size == 32) {
        int64_t dividend = static_cast<int64_t>(
            (static_cast<uint64_t>(cpu.GetRegister(Register::RDX) & 0xFFFFFFFF)
             << 32) |
            (cpu.GetRegister(Register::RAX) & 0xFFFFFFFF));
        int32_t quotient = dividend / static_cast<int32_t>(divisor);
        int32_t remainder = dividend % static_cast<int32_t>(divisor);
        cpu.SetRegister(Register::RAX, static_cast<uint32_t>(quotient));
        cpu.SetRegister(Register::RDX, static_cast<uint32_t>(remainder));
      } else { // 64-bit
        // For 64-bit signed division, we need to handle 128-bit signed dividend
        int64_t dividend_high =
            static_cast<int64_t>(cpu.GetRegister(Register::RDX));
        int64_t dividend_low =
            static_cast<int64_t>(cpu.GetRegister(Register::RAX));

        // Simple case: if high part is 0 or -1 (sign extension), do regular
        // division
        if (dividend_high == 0 || (dividend_high == -1 && dividend_low < 0)) {
          int64_t quotient = dividend_low / divisor;
          int64_t remainder = dividend_low % divisor;
          cpu.SetRegister(Register::RAX, static_cast<uint64_t>(quotient));
          cpu.SetRegister(Register::RDX, static_cast<uint64_t>(remainder));
        } else {
          // Complex case: check for overflow
          if ((dividend_high > 0 && dividend_high >= divisor) ||
              (dividend_high < -1)) {
            cpu.TriggerInterrupt(EXC_DE, 0, false);
            return;
          }

          // Simplified signed division
          int64_t quotient = dividend_low / divisor;
          int64_t remainder = dividend_low % divisor;
          cpu.SetRegister(Register::RAX, static_cast<uint64_t>(quotient));
          cpu.SetRegister(Register::RDX, static_cast<uint64_t>(remainder));
        }
      }
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Inc)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 1) {
      uint64_t op = (exec.instr.operands[0].type ==
                     DecodedInstruction::Operand::Type::REGISTER)
                        ? cpu.GetRegister(exec.instr.operands[0].reg)
                        : exec.instr.operands[0].immediate;
      uint64_t result = op + 1;

      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg, result);
      }

      // INC does not affect CF, but affects other arithmetic flags
      bool oldCF = cpu.GetFlag(FLAG_CF);
      cpu.UpdateArithmeticFlags(op, 1, result, exec.instr.operands[0].size,
                                false);
      cpu.SetFlag(FLAG_CF, oldCF); // Preserve CF
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Dec)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 1) {
      uint64_t op = (exec.instr.operands[0].type ==
                     DecodedInstruction::Operand::Type::REGISTER)
                        ? cpu.GetRegister(exec.instr.operands[0].reg)
                        : exec.instr.operands[0].immediate;
      uint64_t result = op - 1;

      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg, result);
      }

      // DEC does not affect CF, but affects other arithmetic flags
      bool oldCF = cpu.GetFlag(FLAG_CF);
      cpu.UpdateArithmeticFlags(op, 1, result, exec.instr.operands[0].size,
                                true);
      cpu.SetFlag(FLAG_CF, oldCF); // Preserve CF
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Neg)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 1) {
      uint64_t op = (exec.instr.operands[0].type ==
                     DecodedInstruction::Operand::Type::REGISTER)
                        ? cpu.GetRegister(exec.instr.operands[0].reg)
                        : exec.instr.operands[0].immediate;

      uint8_t size = exec.instr.operands[0].size;
      uint64_t mask = (size == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << size) - 1;
      uint64_t result = (~op + 1) & mask;

      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg, result);
      }

      // NEG sets CF if operand is not 0, OF if operand is most negative number
      cpu.SetFlag(FLAG_CF, op != 0);
      cpu.SetFlag(FLAG_OF, op == (1ULL << (size - 1)));

      // Update other flags based on result
      cpu.SetFlag(FLAG_ZF, result == 0);
      cpu.SetFlag(FLAG_SF, (result & (1ULL << (size - 1))) != 0);

      // Calculate parity flag (even parity of low 8 bits)
      uint8_t byte = result & 0xFF;
      int count = 0;
      for (int i = 0; i < 8; ++i) {
        if ((byte >> i) & 1)
          count++;
      }
      cpu.SetFlag(FLAG_PF, (count % 2 == 0));
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Cmp)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t op1 = (exec.instr.operands[0].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[0].reg)
                         : exec.instr.operands[0].immediate;
      uint64_t op2 = (exec.instr.operands[1].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[1].reg)
                         : exec.instr.operands[1].immediate;

      uint64_t result =
          op1 - op2; // CMP performs subtraction but discards result
      cpu.UpdateArithmeticFlags(op1, op2, result, exec.instr.operands[0].size,
                                true);
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Adc)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t op1 = (exec.instr.operands[0].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[0].reg)
                         : exec.instr.operands[0].immediate;
      uint64_t op2 = (exec.instr.operands[1].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[1].reg)
                         : exec.instr.operands[1].immediate;

      uint64_t carry = cpu.GetFlag(FLAG_CF) ? 1 : 0;
      uint64_t result = op1 + op2 + carry;

      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg, result);
      }

      // For ADC, we need to handle carry from the addition of op1 + op2 + carry
      uint8_t size = exec.instr.operands[0].size;
      uint64_t mask = (size == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << size) - 1;

      // Check for carry out
      bool carryOut = false;
      if (size == 64) {
        // For 64-bit, check if addition overflowed
        carryOut = (result < op1) || (result < op2) ||
                   (carry && (op1 == UINT64_MAX || op2 == UINT64_MAX));
      } else {
        carryOut = result > mask;
      }

      cpu.SetFlag(FLAG_CF, carryOut);

      // Update other flags
      result &= mask;
      cpu.SetFlag(FLAG_ZF, result == 0);
      cpu.SetFlag(FLAG_SF, (result & (1ULL << (size - 1))) != 0);

      // Calculate parity flag (even parity of low 8 bits)
      uint8_t byte = result & 0xFF;
      int count = 0;
      for (int i = 0; i < 8; ++i) {
        if ((byte >> i) & 1)
          count++;
      }
      cpu.SetFlag(FLAG_PF, (count % 2 == 0));

      // Overflow flag: set if sign of result differs from sign of operands when
      // they have same sign
      bool s1 = (op1 & (1ULL << (size - 1))) != 0;
      bool s2 = (op2 & (1ULL << (size - 1))) != 0;
      bool sr = (result & (1ULL << (size - 1))) != 0;
      cpu.SetFlag(FLAG_OF, s1 == s2 && s1 != sr);

      // Auxiliary carry flag
      cpu.SetFlag(FLAG_AF, ((op1 & 0xF) + (op2 & 0xF) + carry) > 0xF);
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Sbb)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t op1 = (exec.instr.operands[0].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[0].reg)
                         : exec.instr.operands[0].immediate;
      uint64_t op2 = (exec.instr.operands[1].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[1].reg)
                         : exec.instr.operands[1].immediate;

      uint64_t borrow = cpu.GetFlag(FLAG_CF) ? 1 : 0;
      uint64_t result = op1 - op2 - borrow;

      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg, result);
      }

      // For SBB, check for borrow
      uint8_t size = exec.instr.operands[0].size;
      uint64_t mask = (size == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << size) - 1;

      // Check for borrow (carry flag set if borrow occurred)
      bool borrowOut = (op1 < op2) || (op1 == op2 && borrow);
      cpu.SetFlag(FLAG_CF, borrowOut);

      // Update other flags
      result &= mask;
      cpu.SetFlag(FLAG_ZF, result == 0);
      cpu.SetFlag(FLAG_SF, (result & (1ULL << (size - 1))) != 0);

      // Calculate parity flag (even parity of low 8 bits)
      uint8_t byte2 = result & 0xFF;
      int count2 = 0;
      for (int i = 0; i < 8; ++i) {
        if ((byte2 >> i) & 1)
          count2++;
      }
      cpu.SetFlag(FLAG_PF, (count2 % 2 == 0));

      // Overflow flag: set if sign change is unexpected
      bool s1 = (op1 & (1ULL << (size - 1))) != 0;
      bool s2 = (op2 & (1ULL << (size - 1))) != 0;
      bool sr = (result & (1ULL << (size - 1))) != 0;
      cpu.SetFlag(FLAG_OF, (s1 && !s2 && !sr) || (!s1 && s2 && sr));

      // Auxiliary carry flag
      cpu.SetFlag(FLAG_AF, (op1 & 0xF) < ((op2 & 0xF) + borrow));
    }
  };

  // Logical Instructions
  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::And)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t op1 = (exec.instr.operands[0].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[0].reg)
                         : exec.instr.operands[0].immediate;
      uint64_t op2 = (exec.instr.operands[1].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[1].reg)
                         : exec.instr.operands[1].immediate;

      uint64_t result = op1 & op2;

      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg, result);
      }

      // Update logical flags (CF and OF are cleared for logical operations)
      uint8_t size = exec.instr.operands[0].size;
      uint64_t mask = (size == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << size) - 1;
      result &= mask;

      cpu.SetFlag(FLAG_CF, false);
      cpu.SetFlag(FLAG_OF, false);
      cpu.SetFlag(FLAG_ZF, result == 0);
      cpu.SetFlag(FLAG_SF, (result & (1ULL << (size - 1))) != 0);

      // Calculate parity flag (even parity of low 8 bits)
      uint8_t byte = result & 0xFF;
      int count = 0;
      for (int i = 0; i < 8; ++i) {
        if ((byte >> i) & 1)
          count++;
      }
      cpu.SetFlag(FLAG_PF, (count % 2 == 0));
      // AF is undefined for logical operations
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Or)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t op1 = (exec.instr.operands[0].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[0].reg)
                         : exec.instr.operands[0].immediate;
      uint64_t op2 = (exec.instr.operands[1].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[1].reg)
                         : exec.instr.operands[1].immediate;

      uint64_t result = op1 | op2;

      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg, result);
      }

      // Update logical flags (CF and OF are cleared for logical operations)
      uint8_t size = exec.instr.operands[0].size;
      uint64_t mask = (size == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << size) - 1;
      result &= mask;

      cpu.SetFlag(FLAG_CF, false);
      cpu.SetFlag(FLAG_OF, false);
      cpu.SetFlag(FLAG_ZF, result == 0);
      cpu.SetFlag(FLAG_SF, (result & (1ULL << (size - 1))) != 0);

      // Calculate parity flag (even parity of low 8 bits)
      uint8_t byte = result & 0xFF;
      int count = 0;
      for (int i = 0; i < 8; ++i) {
        if ((byte >> i) & 1)
          count++;
      }
      cpu.SetFlag(FLAG_PF, (count % 2 == 0));
      // AF is undefined for logical operations
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Xor)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t op1 = (exec.instr.operands[0].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[0].reg)
                         : exec.instr.operands[0].immediate;
      uint64_t op2 = (exec.instr.operands[1].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[1].reg)
                         : exec.instr.operands[1].immediate;

      uint64_t result = op1 ^ op2;

      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg, result);
      }

      // Update logical flags (CF and OF are cleared for logical operations)
      uint8_t size = exec.instr.operands[0].size;
      uint64_t mask = (size == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << size) - 1;
      result &= mask;

      cpu.SetFlag(FLAG_CF, false);
      cpu.SetFlag(FLAG_OF, false);
      cpu.SetFlag(FLAG_ZF, result == 0);
      cpu.SetFlag(FLAG_SF, (result & (1ULL << (size - 1))) != 0);

      // Calculate parity flag (even parity of low 8 bits)
      uint8_t byte = result & 0xFF;
      int count = 0;
      for (int i = 0; i < 8; ++i) {
        if ((byte >> i) & 1)
          count++;
      }
      cpu.SetFlag(FLAG_PF, (count % 2 == 0));
      // AF is undefined for logical operations
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Not)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 1) {
      uint64_t op = (exec.instr.operands[0].type ==
                     DecodedInstruction::Operand::Type::REGISTER)
                        ? cpu.GetRegister(exec.instr.operands[0].reg)
                        : exec.instr.operands[0].immediate;

      uint8_t size = exec.instr.operands[0].size;
      uint64_t mask = (size == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << size) - 1;
      uint64_t result = (~op) & mask;

      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg, result);
      }

      // NOT does not affect any flags
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Test)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t op1 = (exec.instr.operands[0].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[0].reg)
                         : exec.instr.operands[0].immediate;
      uint64_t op2 = (exec.instr.operands[1].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[1].reg)
                         : exec.instr.operands[1].immediate;

      uint64_t result = op1 & op2; // TEST performs AND but doesn't store result

      // Update logical flags (CF and OF are cleared for logical operations)
      uint8_t size = exec.instr.operands[0].size;
      uint64_t mask = (size == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << size) - 1;
      result &= mask;

      cpu.SetFlag(FLAG_CF, false);
      cpu.SetFlag(FLAG_OF, false);
      cpu.SetFlag(FLAG_ZF, result == 0);
      cpu.SetFlag(FLAG_SF, (result & (1ULL << (size - 1))) != 0);

      // Calculate parity flag (even parity of low 8 bits)
      uint8_t byte = result & 0xFF;
      int count = 0;
      for (int i = 0; i < 8; ++i) {
        if ((byte >> i) & 1)
          count++;
      }
      cpu.SetFlag(FLAG_PF, (count % 2 == 0));
      // AF is undefined for logical operations
    }
  };

  // Control Flow Instructions
  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Jmp)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 1) {
      uint64_t target = 0;

      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::IMMEDIATE) {
        // Direct jump with relative offset
        int64_t offset = static_cast<int64_t>(exec.instr.operands[0].immediate);
        target = cpu.GetRegister(Register::RIP) + offset;
      } else if (exec.instr.operands[0].type ==
                 DecodedInstruction::Operand::Type::REGISTER) {
        // Indirect jump through register
        target = cpu.GetRegister(exec.instr.operands[0].reg);
      } else if (exec.instr.operands[0].type ==
                 DecodedInstruction::Operand::Type::MEMORY) {
        // Indirect jump through memory
        uint64_t addr = cpu.CalculateMemoryAddress(exec.instr.operands[0]);
        uint64_t value = 0;
        cpu.GetMMU().ReadVirtual(addr, &value, 8, cpu.GetProcessId());
        target = value;
      }

      // Update branch predictor
      branchPredictor.UpdateTarget(cpu.GetRegister(Register::RIP), target);

      // Set new instruction pointer
      cpu.SetRegister(Register::RIP, target);

      // Flush pipeline due to control flow change
      Flush();
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Call)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 1) {
      uint64_t target = 0;
      uint64_t returnAddress =
          cpu.GetRegister(Register::RIP) + exec.instr.length;

      // Push return address onto stack
      uint64_t rsp = cpu.GetRegister(Register::RSP) - 8;
      cpu.GetMMU().WriteVirtual(rsp, &returnAddress, 8, cpu.GetProcessId());
      cpu.SetRegister(Register::RSP, rsp);

      // Use safe operand access
      const DecodedInstruction::Operand *operand =
          SafeGetOperand(exec.instr, 0);
      if (!operand) {
        spdlog::error("CALL: Failed to access operand 0 at PC 0x{:x}",
                      exec.instr.pc);
        return;
      }

      if (operand->type == DecodedInstruction::Operand::Type::IMMEDIATE) {
        // Direct call with relative offset
        int64_t offset = static_cast<int64_t>(operand->immediate);
        target = cpu.GetRegister(Register::RIP) + offset;
      } else if (operand->type == DecodedInstruction::Operand::Type::REGISTER) {
        // Indirect call through register
        target = cpu.GetRegister(operand->reg);
      } else if (operand->type == DecodedInstruction::Operand::Type::MEMORY) {
        // Indirect call through memory
        uint64_t addr = cpu.CalculateMemoryAddress(*operand);
        uint64_t value = 0;
        cpu.GetMMU().ReadVirtual(addr, &value, 8, cpu.GetProcessId());
        target = value;
      }

      // Update branch predictor and return address stack
      branchPredictor.UpdateTarget(cpu.GetRegister(Register::RIP), target);

      // Set new instruction pointer
      cpu.SetRegister(Register::RIP, target);

      // Flush pipeline due to control flow change
      Flush();
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Ret)] = [this](ExecuteStage &exec) {
    // Pop return address from stack
    uint64_t rsp = cpu.GetRegister(Register::RSP);
    uint64_t returnAddress = 0;
    cpu.GetMMU().ReadVirtual(rsp, &returnAddress, 8, cpu.GetProcessId());
    cpu.SetRegister(Register::RSP, rsp + 8);

    // Handle immediate operand (stack adjustment)
    if (exec.instr.operandCount >= 1 &&
        exec.instr.operands[0].type ==
            DecodedInstruction::Operand::Type::IMMEDIATE) {
      uint16_t stackAdjust =
          static_cast<uint16_t>(exec.instr.operands[0].immediate);
      cpu.SetRegister(Register::RSP,
                      cpu.GetRegister(Register::RSP) + stackAdjust);
    }

    // Update branch predictor
    branchPredictor.UpdateTarget(cpu.GetRegister(Register::RIP), returnAddress);

    // Set new instruction pointer
    cpu.SetRegister(Register::RIP, returnAddress);

    // Flush pipeline due to control flow change
    Flush();
  };

  // Conditional Jump Instructions (Jcc)
  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Jcc)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 1) {
      bool conditionMet =
          cpu.CheckCondition(static_cast<uint8_t>(exec.instr.conditionCode));
      uint64_t currentRIP = cpu.GetRegister(Register::RIP);

      if (conditionMet) {
        // Take the branch
        int64_t offset = static_cast<int64_t>(exec.instr.operands[0].immediate);
        uint64_t target = currentRIP + offset;

        // Update branch predictor
        branchPredictor.Update(currentRIP, true);
        branchPredictor.UpdateTarget(currentRIP, target);

        // Set new instruction pointer
        cpu.SetRegister(Register::RIP, target);

        // Flush pipeline due to control flow change
        Flush();
      } else {
        // Don't take the branch, continue to next instruction
        branchPredictor.Update(currentRIP, false);
        // RIP will be updated normally by the pipeline
      }
    }
  };

  // Data Movement Instructions
  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Movsx)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t sourceValue = 0;
      uint8_t sourceSize = exec.instr.operands[1].size;
      uint8_t destSize = exec.instr.operands[0].size;

      // Get source value
      if (exec.instr.operands[1].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        sourceValue = cpu.GetRegister(exec.instr.operands[1].reg);
      } else if (exec.instr.operands[1].type ==
                 DecodedInstruction::Operand::Type::MEMORY) {
        uint64_t addr = cpu.CalculateMemoryAddress(exec.instr.operands[1]);
        cpu.GetMMU().ReadVirtual(addr, &sourceValue, sourceSize / 8,
                                 cpu.GetProcessId());
      } else {
        sourceValue = exec.instr.operands[1].immediate;
      }

      // Perform sign extension
      uint64_t result = 0;
      if (sourceSize == 8) {
        int8_t signedValue = static_cast<int8_t>(sourceValue & 0xFF);
        result = static_cast<uint64_t>(static_cast<int64_t>(signedValue));
      } else if (sourceSize == 16) {
        int16_t signedValue = static_cast<int16_t>(sourceValue & 0xFFFF);
        result = static_cast<uint64_t>(static_cast<int64_t>(signedValue));
      } else if (sourceSize == 32) {
        int32_t signedValue = static_cast<int32_t>(sourceValue & 0xFFFFFFFF);
        result = static_cast<uint64_t>(static_cast<int64_t>(signedValue));
      }

      // Mask result to destination size
      if (destSize == 16) {
        result &= 0xFFFF;
      } else if (destSize == 32) {
        result &= 0xFFFFFFFF;
      }
      // 64-bit result is already correct

      // Store result
      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg, result);
      }

      // MOVSX does not affect flags
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Movzx)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t sourceValue = 0;
      uint8_t sourceSize = exec.instr.operands[1].size;
      uint8_t destSize = exec.instr.operands[0].size;

      // Get source value
      if (exec.instr.operands[1].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        sourceValue = cpu.GetRegister(exec.instr.operands[1].reg);
      } else if (exec.instr.operands[1].type ==
                 DecodedInstruction::Operand::Type::MEMORY) {
        uint64_t addr = cpu.CalculateMemoryAddress(exec.instr.operands[1]);
        cpu.GetMMU().ReadVirtual(addr, &sourceValue, sourceSize / 8,
                                 cpu.GetProcessId());
      } else {
        sourceValue = exec.instr.operands[1].immediate;
      }

      // Perform zero extension (just mask to source size)
      uint64_t result = 0;
      if (sourceSize == 8) {
        result = sourceValue & 0xFF;
      } else if (sourceSize == 16) {
        result = sourceValue & 0xFFFF;
      } else if (sourceSize == 32) {
        result = sourceValue & 0xFFFFFFFF;
      }

      // Store result (zero extension automatically clears upper bits)
      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg, result);
      }

      // MOVZX does not affect flags
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Xchg)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      // Use safe operand access for both operands
      const DecodedInstruction::Operand *operand0 =
          SafeGetOperand(exec.instr, 0);
      const DecodedInstruction::Operand *operand1 =
          SafeGetOperand(exec.instr, 1);
      if (!operand0 || !operand1) {
        spdlog::error("XCHG: Failed to access operands at PC 0x{:x}",
                      exec.instr.pc);
        return;
      }

      uint64_t value1 = 0, value2 = 0;

      // Get first operand value
      if (operand0->type == DecodedInstruction::Operand::Type::REGISTER) {
        value1 = cpu.GetRegister(operand0->reg);
      } else if (operand0->type == DecodedInstruction::Operand::Type::MEMORY) {
        uint64_t addr = cpu.CalculateMemoryAddress(*operand0);
        cpu.GetMMU().ReadVirtual(addr, &value1, operand0->size / 8,
                                 cpu.GetProcessId());
      }

      // Get second operand value
      if (operand1->type == DecodedInstruction::Operand::Type::REGISTER) {
        value2 = cpu.GetRegister(operand1->reg);
      } else if (operand1->type == DecodedInstruction::Operand::Type::MEMORY) {
        uint64_t addr = cpu.CalculateMemoryAddress(*operand1);
        cpu.GetMMU().ReadVirtual(addr, &value2, operand1->size / 8,
                                 cpu.GetProcessId());
      }

      // Exchange values
      if (operand0->type == DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(operand0->reg, value2);
      } else if (operand0->type == DecodedInstruction::Operand::Type::MEMORY) {
        uint64_t addr = cpu.CalculateMemoryAddress(*operand0);
        cpu.GetMMU().WriteVirtual(addr, &value2, operand0->size / 8,
                                  cpu.GetProcessId());
      }

      if (operand1->type == DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(operand1->reg, value1);
      } else if (operand1->type == DecodedInstruction::Operand::Type::MEMORY) {
        uint64_t addr = cpu.CalculateMemoryAddress(*operand1);
        cpu.GetMMU().WriteVirtual(addr, &value1, operand1->size / 8,
                                  cpu.GetProcessId());
      }

      // XCHG does not affect flags
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Lea)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      // LEA calculates effective address without accessing memory
      if (exec.instr.operands[1].type ==
          DecodedInstruction::Operand::Type::MEMORY) {
        uint64_t effectiveAddress =
            cpu.CalculateMemoryAddress(exec.instr.operands[1]);

        // Store effective address in destination register
        if (exec.instr.operands[0].type ==
            DecodedInstruction::Operand::Type::REGISTER) {
          cpu.SetRegister(exec.instr.operands[0].reg, effectiveAddress);
        }
      }

      // LEA does not affect flags
    }
  };

  // Basic SIMD Instructions
  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Movaps)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      // Use simple 128-bit data structure for XMM registers
      uint64_t sourceValue[2] = {0, 0}; // 128 bits = 2 x 64-bit values

      // Get source value
      if (exec.instr.operands[1].type ==
          DecodedInstruction::Operand::Type::XMM) {
        // For now, use a simple approach - get register as two 64-bit values
        uint8_t regNum = static_cast<uint8_t>(exec.instr.operands[1].reg);
        sourceValue[0] = cpu.GetRegister(static_cast<Register>(
            static_cast<int>(Register::XMM0) + regNum * 2));
        sourceValue[1] = cpu.GetRegister(static_cast<Register>(
            static_cast<int>(Register::XMM0) + regNum * 2 + 1));
      } else if (exec.instr.operands[1].type ==
                 DecodedInstruction::Operand::Type::MEMORY) {
        uint64_t addr = cpu.CalculateMemoryAddress(exec.instr.operands[1]);
        // Check 16-byte alignment for MOVAPS
        if (addr % 16 != 0) {
          cpu.TriggerInterrupt(EXC_GP, 0, false); // General Protection Fault
          return;
        }
        // Read 128 bits (16 bytes) from memory
        cpu.GetMMU().ReadVirtual(addr, sourceValue, 16, cpu.GetProcessId());
      }

      // Store result
      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::XMM) {
        uint8_t regNum = static_cast<uint8_t>(exec.instr.operands[0].reg);
        cpu.SetRegister(static_cast<Register>(static_cast<int>(Register::XMM0) +
                                              regNum * 2),
                        sourceValue[0]);
        cpu.SetRegister(static_cast<Register>(static_cast<int>(Register::XMM0) +
                                              regNum * 2 + 1),
                        sourceValue[1]);
      } else if (exec.instr.operands[0].type ==
                 DecodedInstruction::Operand::Type::MEMORY) {
        uint64_t addr = cpu.CalculateMemoryAddress(exec.instr.operands[0]);
        // Check 16-byte alignment for MOVAPS
        if (addr % 16 != 0) {
          cpu.TriggerInterrupt(EXC_GP, 0, false); // General Protection Fault
          return;
        }
        // Write 128 bits (16 bytes) to memory
        cpu.GetMMU().WriteVirtual(addr, sourceValue, 16, cpu.GetProcessId());
      }

      // MOVAPS does not affect flags
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Addps)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t dest[2] = {0, 0};
      uint64_t source[2] = {0, 0};

      // Get destination value
      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::XMM) {
        uint8_t regNum = static_cast<uint8_t>(exec.instr.operands[0].reg);
        dest[0] = cpu.GetRegister(static_cast<Register>(
            static_cast<int>(Register::XMM0) + regNum * 2));
        dest[1] = cpu.GetRegister(static_cast<Register>(
            static_cast<int>(Register::XMM0) + regNum * 2 + 1));
      }

      // Get source value
      if (exec.instr.operands[1].type ==
          DecodedInstruction::Operand::Type::XMM) {
        uint8_t regNum = static_cast<uint8_t>(exec.instr.operands[1].reg);
        source[0] = cpu.GetRegister(static_cast<Register>(
            static_cast<int>(Register::XMM0) + regNum * 2));
        source[1] = cpu.GetRegister(static_cast<Register>(
            static_cast<int>(Register::XMM0) + regNum * 2 + 1));
      } else if (exec.instr.operands[1].type ==
                 DecodedInstruction::Operand::Type::MEMORY) {
        uint64_t addr = cpu.CalculateMemoryAddress(exec.instr.operands[1]);
        cpu.GetMMU().ReadVirtual(addr, source, 16, cpu.GetProcessId());
      }

      // Perform packed single-precision addition
      // Treat each 64-bit value as two 32-bit floats
      float *destFloats = reinterpret_cast<float *>(dest);
      float *sourceFloats = reinterpret_cast<float *>(source);

      for (int i = 0; i < 4; ++i) {
        // CRITICAL: Add bounds check for float array access
        if (destFloats == nullptr || sourceFloats == nullptr) {
          spdlog::error("ExecuteAddpsInstruction: Null pointer in float arrays");
          break;
        }
        destFloats[i] = destFloats[i] + sourceFloats[i];
      }

      // Store result
      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::XMM) {
        uint8_t regNum = static_cast<uint8_t>(exec.instr.operands[0].reg);
        cpu.SetRegister(static_cast<Register>(static_cast<int>(Register::XMM0) +
                                              regNum * 2),
                        dest[0]);
        cpu.SetRegister(static_cast<Register>(static_cast<int>(Register::XMM0) +
                                              regNum * 2 + 1),
                        dest[1]);
      }

      // ADDPS does not affect flags
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Subps)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t dest[2] = {0, 0};
      uint64_t source[2] = {0, 0};

      // Get destination value
      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::XMM) {
        uint8_t regNum = static_cast<uint8_t>(exec.instr.operands[0].reg);
        dest[0] = cpu.GetRegister(static_cast<Register>(
            static_cast<int>(Register::XMM0) + regNum * 2));
        dest[1] = cpu.GetRegister(static_cast<Register>(
            static_cast<int>(Register::XMM0) + regNum * 2 + 1));
      }

      // Get source value
      if (exec.instr.operands[1].type ==
          DecodedInstruction::Operand::Type::XMM) {
        uint8_t regNum = static_cast<uint8_t>(exec.instr.operands[1].reg);
        source[0] = cpu.GetRegister(static_cast<Register>(
            static_cast<int>(Register::XMM0) + regNum * 2));
        source[1] = cpu.GetRegister(static_cast<Register>(
            static_cast<int>(Register::XMM0) + regNum * 2 + 1));
      } else if (exec.instr.operands[1].type ==
                 DecodedInstruction::Operand::Type::MEMORY) {
        uint64_t addr = cpu.CalculateMemoryAddress(exec.instr.operands[1]);
        cpu.GetMMU().ReadVirtual(addr, source, 16, cpu.GetProcessId());
      }

      // Perform packed single-precision subtraction
      // Treat each 64-bit value as two 32-bit floats
      float *destFloats = reinterpret_cast<float *>(dest);
      float *sourceFloats = reinterpret_cast<float *>(source);

      for (int i = 0; i < 4; ++i) {
        destFloats[i] = destFloats[i] - sourceFloats[i];
      }

      // Store result
      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::XMM) {
        uint8_t regNum = static_cast<uint8_t>(exec.instr.operands[0].reg);
        cpu.SetRegister(static_cast<Register>(static_cast<int>(Register::XMM0) +
                                              regNum * 2),
                        dest[0]);
        cpu.SetRegister(static_cast<Register>(static_cast<int>(Register::XMM0) +
                                              regNum * 2 + 1),
                        dest[1]);
      }

      // SUBPS does not affect flags
    }
  };

  // Add MULPS instruction
  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Mulps)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t dest[2] = {0, 0};
      uint64_t source[2] = {0, 0};

      // Get destination value
      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::XMM) {
        uint8_t regNum = static_cast<uint8_t>(exec.instr.operands[0].reg);
        dest[0] = cpu.GetRegister(static_cast<Register>(
            static_cast<int>(Register::XMM0) + regNum * 2));
        dest[1] = cpu.GetRegister(static_cast<Register>(
            static_cast<int>(Register::XMM0) + regNum * 2 + 1));
      }

      // Get source value
      if (exec.instr.operands[1].type ==
          DecodedInstruction::Operand::Type::XMM) {
        uint8_t regNum = static_cast<uint8_t>(exec.instr.operands[1].reg);
        source[0] = cpu.GetRegister(static_cast<Register>(
            static_cast<int>(Register::XMM0) + regNum * 2));
        source[1] = cpu.GetRegister(static_cast<Register>(
            static_cast<int>(Register::XMM0) + regNum * 2 + 1));
      } else if (exec.instr.operands[1].type ==
                 DecodedInstruction::Operand::Type::MEMORY) {
        uint64_t addr = cpu.CalculateMemoryAddress(exec.instr.operands[1]);
        cpu.GetMMU().ReadVirtual(addr, source, 16, cpu.GetProcessId());
      }

      // Perform packed single-precision multiplication
      // Treat each 64-bit value as two 32-bit floats
      float *destFloats = reinterpret_cast<float *>(dest);
      float *sourceFloats = reinterpret_cast<float *>(source);

      for (int i = 0; i < 4; ++i) {
        destFloats[i] = destFloats[i] * sourceFloats[i];
      }

      // Store result
      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::XMM) {
        uint8_t regNum = static_cast<uint8_t>(exec.instr.operands[0].reg);
        cpu.SetRegister(static_cast<Register>(static_cast<int>(Register::XMM0) +
                                              regNum * 2),
                        dest[0]);
        cpu.SetRegister(static_cast<Register>(static_cast<int>(Register::XMM0) +
                                              regNum * 2 + 1),
                        dest[1]);
      }

      // MULPS does not affect flags
    }
  };

  // Shift and Rotate Instructions
  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Shl)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t op = (exec.instr.operands[0].type ==
                     DecodedInstruction::Operand::Type::REGISTER)
                        ? cpu.GetRegister(exec.instr.operands[0].reg)
                        : exec.instr.operands[0].immediate;
      uint8_t count =
          static_cast<uint8_t>((exec.instr.operands[1].type ==
                                DecodedInstruction::Operand::Type::REGISTER)
                                   ? cpu.GetRegister(exec.instr.operands[1].reg)
                                   : exec.instr.operands[1].immediate);
      uint8_t size = exec.instr.operands[0].size;
      uint64_t mask = (size == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << size) - 1;

      if (count == 0) {
        return; // No operation if count is 0
      }

      if (count >= size) {
        // Shift by more than operand size results in 0
        if (exec.instr.operands[0].type ==
            DecodedInstruction::Operand::Type::REGISTER) {
          cpu.SetRegister(exec.instr.operands[0].reg, 0);
        }
        cpu.SetFlag(FLAG_CF, false);
        cpu.SetFlag(FLAG_OF, false);
        cpu.SetFlag(FLAG_ZF, true);
        cpu.SetFlag(FLAG_SF, false);
        cpu.SetFlag(FLAG_PF, true); // Parity of 0 is even
        return;
      }

      uint64_t result = (op << count) & mask;
      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg, result);
      }

      // Set carry flag to last bit shifted out
      cpu.SetFlag(FLAG_CF, (op >> (size - count)) & 1);

      // Set overflow flag only for single-bit shifts
      if (count == 1) {
        cpu.SetFlag(FLAG_OF,
                    ((result >> (size - 1)) & 1) != ((op >> (size - 1)) & 1));
      } else {
        cpu.SetFlag(FLAG_OF, false); // Undefined for multi-bit shifts
      }

      // Update other flags
      cpu.SetFlag(FLAG_ZF, result == 0);
      cpu.SetFlag(FLAG_SF, (result & (1ULL << (size - 1))) != 0);

      // Calculate parity flag (even parity of low 8 bits)
      uint8_t byte = result & 0xFF;
      int parity_count = 0;
      for (int i = 0; i < 8; ++i) {
        if ((byte >> i) & 1)
          parity_count++;
      }
      cpu.SetFlag(FLAG_PF, (parity_count % 2 == 0));
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Shr)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t op = (exec.instr.operands[0].type ==
                     DecodedInstruction::Operand::Type::REGISTER)
                        ? cpu.GetRegister(exec.instr.operands[0].reg)
                        : exec.instr.operands[0].immediate;
      uint8_t count =
          static_cast<uint8_t>((exec.instr.operands[1].type ==
                                DecodedInstruction::Operand::Type::REGISTER)
                                   ? cpu.GetRegister(exec.instr.operands[1].reg)
                                   : exec.instr.operands[1].immediate);
      uint8_t size = exec.instr.operands[0].size;
      uint64_t mask = (size == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << size) - 1;

      op &= mask; // Mask operand to size

      if (count == 0) {
        return; // No operation if count is 0
      }

      if (count >= size) {
        // Shift by more than operand size results in 0
        if (exec.instr.operands[0].type ==
            DecodedInstruction::Operand::Type::REGISTER) {
          cpu.SetRegister(exec.instr.operands[0].reg, 0);
        }
        cpu.SetFlag(FLAG_CF, false);
        cpu.SetFlag(FLAG_OF, false);
        cpu.SetFlag(FLAG_ZF, true);
        cpu.SetFlag(FLAG_SF, false);
        cpu.SetFlag(FLAG_PF, true); // Parity of 0 is even
        return;
      }

      uint64_t result = op >> count;
      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg, result);
      }

      // Set carry flag to last bit shifted out
      cpu.SetFlag(FLAG_CF, (op >> (count - 1)) & 1);

      // Set overflow flag only for single-bit shifts
      if (count == 1) {
        cpu.SetFlag(FLAG_OF, (op >> (size - 1)) & 1); // MSB of original operand
      } else {
        cpu.SetFlag(FLAG_OF, false); // Undefined for multi-bit shifts
      }

      // Update other flags
      cpu.SetFlag(FLAG_ZF, result == 0);
      cpu.SetFlag(FLAG_SF, (result & (1ULL << (size - 1))) != 0);

      // Calculate parity flag (even parity of low 8 bits)
      uint8_t byte = result & 0xFF;
      int parity_count = 0;
      for (int i = 0; i < 8; ++i) {
        if ((byte >> i) & 1)
          parity_count++;
      }
      cpu.SetFlag(FLAG_PF, (parity_count % 2 == 0));
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Sar)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t op = (exec.instr.operands[0].type ==
                     DecodedInstruction::Operand::Type::REGISTER)
                        ? cpu.GetRegister(exec.instr.operands[0].reg)
                        : exec.instr.operands[0].immediate;
      uint8_t count =
          static_cast<uint8_t>((exec.instr.operands[1].type ==
                                DecodedInstruction::Operand::Type::REGISTER)
                                   ? cpu.GetRegister(exec.instr.operands[1].reg)
                                   : exec.instr.operands[1].immediate);
      uint8_t size = exec.instr.operands[0].size;
      uint64_t mask = (size == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << size) - 1;

      op &= mask; // Mask operand to size

      if (count == 0) {
        return; // No operation if count is 0
      }

      // For SAR, we need to perform arithmetic (signed) right shift
      uint64_t result;
      bool sign_bit = (op & (1ULL << (size - 1))) != 0;

      if (count >= size) {
        // Shift by more than operand size: result is 0 or all 1s based on sign
        result = sign_bit ? mask : 0;
        if (exec.instr.operands[0].type ==
            DecodedInstruction::Operand::Type::REGISTER) {
          cpu.SetRegister(exec.instr.operands[0].reg, result);
        }
        cpu.SetFlag(FLAG_CF, sign_bit);
        cpu.SetFlag(FLAG_OF, false);
      } else {
        result = op >> count;
        // Fill with sign bits
        if (sign_bit) {
          uint64_t sign_fill = mask << (size - count);
          result |= sign_fill;
        }
        result &= mask;

        if (exec.instr.operands[0].type ==
            DecodedInstruction::Operand::Type::REGISTER) {
          cpu.SetRegister(exec.instr.operands[0].reg, result);
        }

        // Set carry flag to last bit shifted out
        cpu.SetFlag(FLAG_CF, (op >> (count - 1)) & 1);

        // Overflow flag is always 0 for SAR
        cpu.SetFlag(FLAG_OF, false);
      }

      // Update other flags
      cpu.SetFlag(FLAG_ZF, result == 0);
      cpu.SetFlag(FLAG_SF, (result & (1ULL << (size - 1))) != 0);

      // Calculate parity flag (even parity of low 8 bits)
      uint8_t byte = result & 0xFF;
      int parity_count = 0;
      for (int i = 0; i < 8; ++i) {
        if ((byte >> i) & 1)
          parity_count++;
      }
      cpu.SetFlag(FLAG_PF, (parity_count % 2 == 0));
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Rol)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t op = (exec.instr.operands[0].type ==
                     DecodedInstruction::Operand::Type::REGISTER)
                        ? cpu.GetRegister(exec.instr.operands[0].reg)
                        : exec.instr.operands[0].immediate;
      uint8_t count =
          static_cast<uint8_t>((exec.instr.operands[1].type ==
                                DecodedInstruction::Operand::Type::REGISTER)
                                   ? cpu.GetRegister(exec.instr.operands[1].reg)
                                   : exec.instr.operands[1].immediate);
      uint8_t size = exec.instr.operands[0].size;
      uint64_t mask = (size == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << size) - 1;

      op &= mask;    // Mask operand to size
      count %= size; // Reduce count modulo operand size

      if (count == 0) {
        return; // No operation if count is 0
      }

      // Rotate left: bits shifted out on left come back on right
      uint64_t result = ((op << count) | (op >> (size - count))) & mask;
      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg, result);
      }

      // Set carry flag to LSB of result (bit that was rotated from MSB)
      cpu.SetFlag(FLAG_CF, result & 1);

      // Set overflow flag only for single-bit rotates
      if (count == 1) {
        cpu.SetFlag(FLAG_OF, ((result >> (size - 1)) & 1) != (result & 1));
      } else {
        cpu.SetFlag(FLAG_OF, false); // Undefined for multi-bit rotates
      }

      // ROL does not affect ZF, SF, PF, AF
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Ror)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 2) {
      uint64_t op = (exec.instr.operands[0].type ==
                     DecodedInstruction::Operand::Type::REGISTER)
                        ? cpu.GetRegister(exec.instr.operands[0].reg)
                        : exec.instr.operands[0].immediate;
      uint8_t count =
          static_cast<uint8_t>((exec.instr.operands[1].type ==
                                DecodedInstruction::Operand::Type::REGISTER)
                                   ? cpu.GetRegister(exec.instr.operands[1].reg)
                                   : exec.instr.operands[1].immediate);
      uint8_t size = exec.instr.operands[0].size;
      uint64_t mask = (size == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << size) - 1;

      op &= mask;    // Mask operand to size
      count %= size; // Reduce count modulo operand size

      if (count == 0) {
        return; // No operation if count is 0
      }

      // Rotate right: bits shifted out on right come back on left
      uint64_t result = ((op >> count) | (op << (size - count))) & mask;
      if (exec.instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg, result);
      }

      // Set carry flag to MSB of result (bit that was rotated from LSB)
      cpu.SetFlag(FLAG_CF, (result >> (size - 1)) & 1);

      // Set overflow flag only for single-bit rotates
      if (count == 1) {
        cpu.SetFlag(FLAG_OF, ((result >> (size - 1)) & 1) !=
                                 ((result >> (size - 2)) & 1));
      } else {
        cpu.SetFlag(FLAG_OF, false); // Undefined for multi-bit rotates
      }

      // ROR does not affect ZF, SF, PF, AF
    }
  };

  // Stack Operations
  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Push)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 1 && exec.instr.operandCount <= 4) {
      // Use safe operand access
      const DecodedInstruction::Operand *operand =
          SafeGetOperand(exec.instr, 0);
      if (!operand) {
        spdlog::error("PUSH: Failed to access operand 0 at PC 0x{:x}",
                      exec.instr.pc);
        return;
      }

      uint64_t value = 0;
      uint8_t size = operand->size / 8; // Convert bits to bytes

      // Validate operand size
      if (size == 0) {
        spdlog::error("PUSH: Invalid operand size 0 at PC 0x{:x}",
                      exec.instr.pc);
        return;
      }

      // Get value to push
      if (operand->type == DecodedInstruction::Operand::Type::REGISTER) {
        value = cpu.GetRegister(operand->reg);
      } else if (operand->type ==
                 DecodedInstruction::Operand::Type::IMMEDIATE) {
        value = operand->immediate;
      } else if (operand->type == DecodedInstruction::Operand::Type::MEMORY) {
        uint64_t addr = cpu.CalculateMemoryAddress(*operand);
        cpu.GetMMU().ReadVirtual(addr, &value, size, cpu.GetProcessId());
      }

      // Mask value to operand size
      if (size == 2) {
        value &= 0xFFFF;
      } else if (size == 4) {
        value &= 0xFFFFFFFF;
      }

      // Push value onto stack
      cpu.Push(value, size);

      // PUSH does not affect flags
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Pop)] = [this](ExecuteStage &exec) {
    if (exec.instr.operandCount >= 1 && exec.instr.operandCount <= 4) {
      // Use safe operand access
      const DecodedInstruction::Operand *operand =
          SafeGetOperand(exec.instr, 0);
      if (!operand) {
        spdlog::error("POP: Failed to access operand 0 at PC 0x{:x}",
                      exec.instr.pc);
        return;
      }

      uint8_t size = operand->size / 8; // Convert bits to bytes

      // Validate operand size
      if (size == 0) {
        spdlog::error("POP: Invalid operand size 0 at PC 0x{:x}",
                      exec.instr.pc);
        return;
      }

      // Pop value from stack
      uint64_t value = cpu.Pop(size);

      // Store popped value
      if (operand->type == DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(operand->reg, value);
      } else if (operand->type == DecodedInstruction::Operand::Type::MEMORY) {
        uint64_t addr = cpu.CalculateMemoryAddress(*operand);
        cpu.GetMMU().WriteVirtual(addr, &value, size, cpu.GetProcessId());
      }

      // POP does not affect flags
    }
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Pusha)] = [this](ExecuteStage &exec) {
    // PUSHA pushes all general-purpose registers in order:
    // AX, CX, DX, BX, original SP, BP, SI, DI (16-bit mode)
    // EAX, ECX, EDX, EBX, original ESP, EBP, ESI, EDI (32-bit mode)
    // Note: PUSHA is not available in 64-bit mode, but we'll implement 32-bit
    // version

    uint64_t original_esp = cpu.GetRegister(Register::RSP);

    // Push registers in order (32-bit values)
    cpu.Push(cpu.GetRegister(Register::RAX) & 0xFFFFFFFF, 4); // EAX
    cpu.Push(cpu.GetRegister(Register::RCX) & 0xFFFFFFFF, 4); // ECX
    cpu.Push(cpu.GetRegister(Register::RDX) & 0xFFFFFFFF, 4); // EDX
    cpu.Push(cpu.GetRegister(Register::RBX) & 0xFFFFFFFF, 4); // EBX
    cpu.Push(original_esp & 0xFFFFFFFF, 4);                   // Original ESP
    cpu.Push(cpu.GetRegister(Register::RBP) & 0xFFFFFFFF, 4); // EBP
    cpu.Push(cpu.GetRegister(Register::RSI) & 0xFFFFFFFF, 4); // ESI
    cpu.Push(cpu.GetRegister(Register::RDI) & 0xFFFFFFFF, 4); // EDI

    // PUSHA does not affect flags
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Popa)] = [this](ExecuteStage &exec) {
    // POPA pops all general-purpose registers in reverse order:
    // DI, SI, BP, discard SP, BX, DX, CX, AX (16-bit mode)
    // EDI, ESI, EBP, discard ESP, EBX, EDX, ECX, EAX (32-bit mode)

    // Pop registers in reverse order (32-bit values)
    uint64_t edi = cpu.Pop(4);
    uint64_t esi = cpu.Pop(4);
    uint64_t ebp = cpu.Pop(4);
    cpu.Pop(4); // Discard original ESP value
    uint64_t ebx = cpu.Pop(4);
    uint64_t edx = cpu.Pop(4);
    uint64_t ecx = cpu.Pop(4);
    uint64_t eax = cpu.Pop(4);

    // Set registers (preserving upper 32 bits for 64-bit mode)
    cpu.SetRegister(Register::RDI,
                    (cpu.GetRegister(Register::RDI) & 0xFFFFFFFF00000000ULL) |
                        edi);
    cpu.SetRegister(Register::RSI,
                    (cpu.GetRegister(Register::RSI) & 0xFFFFFFFF00000000ULL) |
                        esi);
    cpu.SetRegister(Register::RBP,
                    (cpu.GetRegister(Register::RBP) & 0xFFFFFFFF00000000ULL) |
                        ebp);
    cpu.SetRegister(Register::RBX,
                    (cpu.GetRegister(Register::RBX) & 0xFFFFFFFF00000000ULL) |
                        ebx);
    cpu.SetRegister(Register::RDX,
                    (cpu.GetRegister(Register::RDX) & 0xFFFFFFFF00000000ULL) |
                        edx);
    cpu.SetRegister(Register::RCX,
                    (cpu.GetRegister(Register::RCX) & 0xFFFFFFFF00000000ULL) |
                        ecx);
    cpu.SetRegister(Register::RAX,
                    (cpu.GetRegister(Register::RAX) & 0xFFFFFFFF00000000ULL) |
                        eax);

    // POPA does not affect flags
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Pushf)] = [this](ExecuteStage &exec) {
    // PUSHF pushes the lower 16 bits of EFLAGS
    uint64_t flags = cpu.GetRflags() & 0xFFFF;
    cpu.Push(flags, 2);

    // PUSHF does not affect flags
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Pushfd)] = [this](ExecuteStage &exec) {
    // PUSHFD pushes the lower 32 bits of EFLAGS
    uint64_t flags = cpu.GetRflags() & 0xFFFFFFFF;
    cpu.Push(flags, 4);

    // PUSHFD does not affect flags
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Pushfq)] = [this](ExecuteStage &exec) {
    // PUSHFQ pushes the full 64-bit RFLAGS (64-bit mode)
    uint64_t flags = cpu.GetRflags();
    cpu.Push(flags, 8);

    // PUSHFQ does not affect flags
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Popf)] = [this](ExecuteStage &exec) {
    // POPF pops 16 bits into the lower 16 bits of EFLAGS
    uint64_t flags = cpu.Pop(2);
    uint64_t current_flags = cpu.GetRflags();

    // Preserve upper bits, update lower 16 bits
    // Some flags cannot be modified by POPF (like RF, VM, etc.)
    uint64_t modifiable_mask = 0x0000FCFF; // Typical modifiable flags mask
    uint64_t new_flags =
        (current_flags & ~modifiable_mask) | (flags & modifiable_mask);
    cpu.SetRflags(new_flags);
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Popfd)] = [this](ExecuteStage &exec) {
    // POPFD pops 32 bits into the lower 32 bits of EFLAGS
    uint64_t flags = cpu.Pop(4);
    uint64_t current_flags = cpu.GetRflags();

    // Preserve upper 32 bits, update lower 32 bits
    // Some flags cannot be modified by POPFD
    uint64_t modifiable_mask =
        0x00FCFFFF; // Typical modifiable flags mask for 32-bit
    uint64_t new_flags =
        (current_flags & ~modifiable_mask) | (flags & modifiable_mask);
    cpu.SetRflags(new_flags);
  };

  opcodeDispatchTable[static_cast<uint16_t>(InstructionType::Popfq)] = [this](ExecuteStage &exec) {
    // POPFQ pops 64 bits into RFLAGS (64-bit mode)
    uint64_t flags = cpu.Pop(8);

    // Some flags cannot be modified by POPFQ
    uint64_t modifiable_mask = 0x00FCFFFF; // Typical modifiable flags mask
    uint64_t current_flags = cpu.GetRflags();
    uint64_t new_flags =
        (current_flags & ~modifiable_mask) | (flags & modifiable_mask);
    cpu.SetRflags(new_flags);
  };

  // Add more instruction handlers as needed
  spdlog::info("Initialized opcode dispatch table with {} handlers",
               opcodeDispatchTable.size());
}

// Enhanced hazard detection implementation
HazardInfo Pipeline::DetectDataHazard(const DecodedInstruction &instr,
                                      uint64_t pc) const {
  HazardInfo hazard;

  // Check for RAW hazards
  if (HasRAWHazard(instr, pc)) {
    hazard.type = HazardType::RAW;
    hazard.description = "Read-After-Write hazard detected";
    hazard.cycles_remaining = 1;
    return hazard;
  }

  if (HasWAWHazard(instr, pc)) {
    hazard.type = HazardType::WAW;
    hazard.description = "Write-After-Write hazard detected";
    hazard.cycles_remaining = 1;
    return hazard;
  }

  if (HasWARHazard(instr, pc)) {
    hazard.type = HazardType::WAR;
    hazard.description = "Write-After-Read hazard detected";
    hazard.cycles_remaining = 1;
    return hazard;
  }

  return hazard; // No hazard
}

HazardInfo
Pipeline::DetectStructuralHazard(const DecodedInstruction &instr) const {
  HazardInfo hazard;

  ExecutionUnitType required_unit = GetRequiredExecutionUnit(instr);

  // Check if any unit of the required type is available
  bool unit_found = false;
  uint64_t earliest_available_cycle = UINT64_MAX;
  uint64_t blocking_pc_candidate = 0;

  auto it = unit_map.find(required_unit);
  if (it != unit_map.end()) {
    for (size_t unit_idx : it->second) {
      // CRITICAL: Add bounds check for execution unit access
      const ExecutionUnit &unit = SAFE_VECTOR_ACCESS(execution_units, unit_idx, "Pipeline execution unit check");
      if (!unit.busy || unit.available_cycle <= stats.cycles) {
        unit_found = true; // An available unit exists
        break;
      } else {
        // Unit is busy, track the earliest it will be free
        if (unit.available_cycle < earliest_available_cycle) {
          earliest_available_cycle = unit.available_cycle;
          blocking_pc_candidate = unit.instruction_pc;
        }
      }
    }
  } else {
    // Should not happen if GetRequiredExecutionUnit is correct
    spdlog::error("Requested execution unit type {} not found in unit_map.",
                  static_cast<int>(required_unit));
    hazard.type = HazardType::Structural;
    hazard.description = "Requested execution unit type not configured.";
    hazard.cycles_remaining = 1;
    return hazard;
  }

  if (!unit_found) {
    hazard.type = HazardType::Structural;
    hazard.description = "Execution unit unavailable: " +
                         std::to_string(static_cast<int>(required_unit));
    hazard.blocking_pc = blocking_pc_candidate;
    hazard.cycles_remaining = earliest_available_cycle - stats.cycles;
    if (hazard.cycles_remaining == 0)
      hazard.cycles_remaining = 1; // At least 1 cycle stall
  }

  return hazard;
}

bool Pipeline::HasRAWHazard(const DecodedInstruction &instr,
                            uint64_t pc) const {
  // Check if the current instruction reads a register that an instruction
  // in the execute or memory stage will write.
  // The first operand is typically the destination (write).
  // Subsequent operands are typically sources (read).

  // Iterate through source operands of the current instruction
  for (int i = 0; i < instr.operandCount; ++i) {
    // CRITICAL: Add bounds check for operand access
    if (i >= static_cast<int>(sizeof(instr.operands)/sizeof(instr.operands[0]))) {
      spdlog::error("CheckRAWHazard: Operand index {} out of bounds", i);
      break;
    }
    const auto &current_operand = instr.operands[i];
    // If the current operand is a register and it's a source (not the first
    // operand, or if it's a read-modify-write) For simplicity, assume any
    // operand after the first is a source, and the first is a destination. A
    // more robust check would involve instruction semantics (e.g., MOV reg, reg
    // - both are sources for RAW if first is dest). For now, let's assume if
    // it's a register and it's read. A simple heuristic: if it's not the first
    // operand, it's likely a read. If it's the first operand, it's a
    // read-modify-write (e.g., ADD RAX, RBX reads RAX, writes RAX).
    bool is_read_operand =
        (i > 0) || (instr.isReadModifyWrite &&
                    i == 0); // Assuming isReadModifyWrite flag exists

    if ((current_operand.type == DecodedInstruction::Operand::Type::REGISTER ||
         current_operand.type == DecodedInstruction::Operand::Type::XMM) &&
        is_read_operand) {
      uint32_t read_reg_id = static_cast<uint32_t>(current_operand.reg);

      // Check execute stage
      for (const auto &stage : executeStage) {
        if (!stage.valid)
          continue;
        // Check if the instruction in execute stage writes to the register
        if (stage.instr.operandCount > 0) {
          const auto &exec_dest_operand =
              stage.instr.operands[0]; // Assume first operand is destination
          if ((exec_dest_operand.type ==
                   DecodedInstruction::Operand::Type::REGISTER ||
               exec_dest_operand.type ==
                   DecodedInstruction::Operand::Type::XMM) &&
              static_cast<uint32_t>(exec_dest_operand.reg) == read_reg_id) {
            spdlog::trace("RAW hazard: PC 0x{:x} reads reg {} written by PC "
                          "0x{:x} in execute stage",
                          pc, read_reg_id, stage.pc);
            return true;
              }
            }
            }

      // Check memory stage
            for (const auto &stage : memoryStage) {
              if (!stage.valid)
              continue;
            // Check if the instruction in memory stage writes to the register
            if (stage.instr.operandCount > 0) {
              const auto &mem_dest_operand =
                stage.instr.operands[0]; // Assume first operand is destination
            if ((mem_dest_operand.type ==
                     DecodedInstruction::Operand::Type::REGISTER ||
            mem_dest_operand.type ==
                DecodedInstruction::Operand::Type::XMM) &&
            static_cast<uint32_t>(mem_dest_operand.reg) == read_reg_id) {
            spdlog::trace("RAW hazard: PC 0x{:x} reads reg {} written by PC "
                          "0x{:x} in memory stage",
            pc, read_reg_id, stage.pc);
            return true;
            }
            }
            }
    }
  }
  return false;
}

bool Pipeline::HasWAWHazard(const DecodedInstruction &instr, uint64_t pc) const {
  // Check Write-After-Write hazards
  if (instr.operandCount > 0) {
    const auto &current_dest_operand = instr.operands[0];
    if (current_dest_operand.type == DecodedInstruction::Operand::Type::REGISTER ||
        current_dest_operand.type == DecodedInstruction::Operand::Type::XMM) {

      // Check against execute stage
      for (const auto &stage : executeStage) {
        if (stage.instr.operandCount > 0) {
          const auto &exec_dest_operand = stage.instr.operands[0];

          if (exec_dest_operand.type == DecodedInstruction::Operand::Type::REGISTER ||
              exec_dest_operand.type == DecodedInstruction::Operand::Type::XMM) {

            if (current_dest_operand.reg == exec_dest_operand.reg) {
              spdlog::debug("WAW hazard detected at PC 0x{:x}", pc);
              return true;
            }
          }
        }
      }

      // Check against memory stage
      for (const auto &stage : memoryStage) {
        if (stage.instr.operandCount > 0) {
          const auto &mem_dest_operand = stage.instr.operands[0];

          if (mem_dest_operand.type == DecodedInstruction::Operand::Type::REGISTER ||
              mem_dest_operand.type == DecodedInstruction::Operand::Type::XMM) {

            if (current_dest_operand.reg == mem_dest_operand.reg) {
              spdlog::debug("WAW hazard detected at PC 0x{:x}", pc);
              return true;
            }
          }
        }
      }
    }
  }
  return false;
}

bool Pipeline::HasWARHazard(const DecodedInstruction &instr, uint64_t pc) const {
  // Check Write-After-Read hazards
  if (instr.operandCount > 0) {
    const auto &current_dest_operand = instr.operands[0];
    if (current_dest_operand.type == DecodedInstruction::Operand::Type::REGISTER ||
        current_dest_operand.type == DecodedInstruction::Operand::Type::XMM) {

      // Check against execute stage source operands
      for (const auto &stage : executeStage) {
        for (uint8_t i = 1; i < stage.instr.operandCount; ++i) {
          // CRITICAL: Add bounds check for operand access
          if (i >= sizeof(stage.instr.operands)/sizeof(stage.instr.operands[0])) {
            spdlog::error("CheckRAWHazard: Execute stage operand index {} out of bounds", i);
            break;
          }
          const auto &exec_operand = stage.instr.operands[i];

          if (exec_operand.type == DecodedInstruction::Operand::Type::REGISTER ||
              exec_operand.type == DecodedInstruction::Operand::Type::XMM) {

            if (current_dest_operand.reg == exec_operand.reg) {
              spdlog::debug("WAR hazard detected at PC 0x{:x}", pc);
              return true;
            }
          }
        }
      }

      // Check against memory stage source operands
      for (const auto &stage : memoryStage) {
        for (uint8_t i = 1; i < stage.instr.operandCount; ++i) {
          const auto &mem_operand = stage.instr.operands[i];

          if (mem_operand.type == DecodedInstruction::Operand::Type::REGISTER ||
              mem_operand.type == DecodedInstruction::Operand::Type::XMM) {

            if (current_dest_operand.reg == mem_operand.reg) {
              spdlog::debug("WAR hazard detected at PC 0x{:x}", pc);
              return true;
            }
          }
        }
      }
    }
  }
  return false;
}

ExecutionUnitType Pipeline::GetRequiredExecutionUnit(const DecodedInstruction &instr) const {
  // Map instruction types to execution units
  switch (instr.instType) {
    // Integer ALU operations
    case InstructionType::Add:
    case InstructionType::Sub:
    case InstructionType::And:
    case InstructionType::Or:
    case InstructionType::Xor:
    case InstructionType::Not:
    case InstructionType::Neg:
    case InstructionType::Cmp:
    case InstructionType::Test:
    case InstructionType::Inc:
    case InstructionType::Dec:
    case InstructionType::Shl:
    case InstructionType::Shr:
    case InstructionType::Sar:
    case InstructionType::Mov:
    case InstructionType::Lea:
      return ExecutionUnitType::ALU;

    // Integer multiplication/division
    case InstructionType::Mul:
    case InstructionType::Imul:
    case InstructionType::Div:
    case InstructionType::Idiv:
      return ExecutionUnitType::DIVIDE;

    // Memory operations
    case InstructionType::Push:
    case InstructionType::Pop:
      return ExecutionUnitType::LOAD_STORE;

    // Branch operations
    case InstructionType::Jump:
    case InstructionType::Jcc:
    case InstructionType::Call:
    case InstructionType::Ret:
      return ExecutionUnitType::BRANCH;

    // SIMD operations
    case InstructionType::Addps:
    case InstructionType::Subps:
    case InstructionType::Mulps:
    case InstructionType::Movaps:
      return ExecutionUnitType::SIMD;

    default:
      return ExecutionUnitType::ALU; // Default fallback
  }
}

int Pipeline::GetInstructionLatency(const DecodedInstruction &instr) const {
  // Map instruction types to latencies
  switch (instr.instType) {
    // Fast integer ALU operations (1 cycle)
    case InstructionType::Add:
    case InstructionType::Sub:
    case InstructionType::And:
    case InstructionType::Or:
    case InstructionType::Xor:
    case InstructionType::Not:
    case InstructionType::Neg:
    case InstructionType::Cmp:
    case InstructionType::Test:
    case InstructionType::Inc:
    case InstructionType::Dec:
    case InstructionType::Mov:
    case InstructionType::Lea:
      return 1;

    // Shift operations (1-2 cycles)
    case InstructionType::Shl:
    case InstructionType::Shr:
    case InstructionType::Sar:
      return 2;

    // Integer multiplication (3-4 cycles)
    case InstructionType::Mul:
    case InstructionType::Imul:
      return 3;

    // Integer division (much slower)
    case InstructionType::Div:
    case InstructionType::Idiv:
      return 20;

    // Memory operations
    case InstructionType::Push:
    case InstructionType::Pop:
      return 2;

    // Branch operations
    case InstructionType::Jump:
    case InstructionType::Jcc:
    case InstructionType::Call:
    case InstructionType::Ret:
      return 1;

    // SIMD operations
    case InstructionType::Addps:
    case InstructionType::Subps:
    case InstructionType::Mulps:
    case InstructionType::Movaps:
      return 4;

    default:
      return 1; // Default latency
  }
}

bool Pipeline::IsExecutionUnitAvailable(ExecutionUnitType type) const {
  auto it = unit_map.find(type);
  if (it == unit_map.end()) {
    return false; // Unit type not configured
  }

  for (size_t unit_idx : it->second) {
    if (!execution_units[unit_idx].busy ||
        execution_units[unit_idx].available_cycle <= stats.cycles) {
      return true; // Found an available unit of this type
    }
  }

  return false; // No unit of this type is available
}

ExecutionUnit *Pipeline::AllocateExecutionUnit(ExecutionUnitType type) {
  auto it = unit_map.find(type);
  if (it == unit_map.end()) {
    return nullptr; // Unit type not configured
  }

  for (size_t unit_idx : it->second) {
    ExecutionUnit &unit = execution_units[unit_idx];
    if (!unit.busy || unit.available_cycle <= stats.cycles) {
      unit.busy = true;
      unit.available_cycle =
          stats.cycles + 1; // Placeholder, will be updated with actual latency
      return &unit;
    }
  }

  return nullptr; // No unit of this type is available
}

void Pipeline::ReleaseExecutionUnit(ExecutionUnit *unit) {
  if (unit) {
    unit->busy = false;
    unit->available_cycle = 0;
    unit->instruction_pc = 0;
  }
}

bool Pipeline::HasDataHazard(const DecodedInstruction &instr,
                             uint64_t pc) const {
  HazardInfo hazard = DetectDataHazard(instr, pc);
  return hazard.type != HazardType::None;
}

bool Pipeline::HasStructuralHazard() const {
  // This function is less useful for *detecting* a structural hazard for a
  // *specific* instruction, but rather for checking if the pipeline *as a
  // whole* is structurally constrained. The `DetectStructuralHazard(instr)` is
  // more precise. This implementation checks if *any* unit type is fully
  // occupied.
  for (const auto &pair : unit_map) {
    bool any_available = false;
    for (size_t unit_idx : pair.second) {
      if (!execution_units[unit_idx].busy ||
          execution_units[unit_idx].available_cycle <= stats.cycles) {
        any_available = true;
        break;
      }
    }
    if (!any_available) {
      // If we reach here, it means for 'pair.first' unit type, all units are
      // busy and will not be available by the current cycle. This indicates a
      // potential structural bottleneck.
      return true;
    }
  }
  return false;
}

void Pipeline::HandleBranchMisprediction(uint64_t actual_target,
                                         uint64_t predicted_target,
                                         uint64_t branch_pc) {
  if (actual_target != predicted_target) {
    spdlog::info(
        "Branch misprediction at 0x{:x}: predicted=0x{:x}, actual=0x{:x}",
        branch_pc, predicted_target, actual_target);

    stats.branch_mispredictions++;

    // Update branch predictor with the correct target
    branchPredictor.UpdateTarget(branch_pc, actual_target);

    // Flush pipeline and redirect to the correct target
    RecoverFromMisprediction(actual_target);
  } else {
    stats.branch_hits++;
  }
}

void Pipeline::RecoverFromMisprediction(uint64_t correct_pc) {
  spdlog::debug("Recovering from misprediction, flushing pipeline and "
                "redirecting to 0x{:x}",
                correct_pc);
  // Flush all stages after fetch (decode, execute, memory, writeback)
  // Fetch stage might contain instructions from the wrong path, so it also
  // needs to be cleared. The new fetch will start from `correct_pc`.
  fetchStage.clear();
  decodeStage.clear();
  executeStage.clear();
  memoryStage.clear();
  writeBackStage.clear();

  // Update PC to correct target
  try {
    cpu.SetRegister(Register::RIP, correct_pc);
    spdlog::debug("Pipeline recovered from misprediction, PC set to 0x{:x}",
                  correct_pc);
  } catch (const std::exception &e) {
    spdlog::error("Failed to set PC during misprediction recovery: {}",
                  e.what());
    // If PC cannot be set, the emulator is in a bad state. Consider halting.
    throw PipelineException("Failed to set RIP during misprediction recovery");
  }

  // Clear any pending execution unit allocations that were for the mispredicted
  // path
  for (auto &unit : execution_units) {
    // Only clear units that are busy and whose instruction_pc matches the
    // mispredicted path or if they are simply busy and not yet available
    // (meaning they were allocated for a future cycle) A simpler approach is to
    // just clear all busy units, assuming they were all part of the flushed
    // path.
    if (unit.busy) { // && unit.instruction_pc >= mispredicted_branch_pc (more
                     // complex check)
      unit.busy = false;
      unit.available_cycle = 0;
      unit.instruction_pc = 0;
    }
  }
}

void Pipeline::Execute() {
  // Check if execute stage is full or decode stage is empty
  if (executeStage.size() >= execution_units_count ||
      decodeStage.empty()) { // Limit by available execution units
    spdlog::trace(
        "Execute stage full ({} units busy) or decode empty, skipping execute",
        executeStage.size());
    stats.stalls++;
    return;
  }

  // Find an instruction in decode stage that can be executed (no hazards)
  auto it = decodeStage.begin();
  while (it != decodeStage.end()) {
    if (!it->valid) {
      it = decodeStage.erase(it); // Remove invalid entries
      continue;
    }

    HazardInfo data_hazard = DetectDataHazard(it->instr, it->pc);
    HazardInfo structural_hazard = DetectStructuralHazard(it->instr);

    if (data_hazard.type != HazardType::None) {
      spdlog::trace("Data hazard prevented execution of PC 0x{:x}: {}", it->pc,
                    data_hazard.description);
      stats.data_hazard_stalls++;
      ++it; // Move to next instruction in decode stage
      continue;
    }

    if (structural_hazard.type != HazardType::None) {
      spdlog::trace("Structural hazard prevented execution of PC 0x{:x}: {}",
                    it->pc, structural_hazard.description);
      stats.structural_hazard_stalls++;
      ++it; // Move to next instruction in decode stage
      continue;
    }

    // If no hazards, this instruction can be moved to execute stage
    break;
  }

  if (it == decodeStage.end()) {
    spdlog::trace("No executable instruction found in decode stage due to "
                  "hazards or empty stage.");
    stats.stalls++; // Stall if no instruction could be moved
    return;
  }

  // Move the instruction from decode to execute stage
  DecodeStage current_decode_stage = std::move(*it);
  decodeStage.erase(it); // Remove from decode stage

  // CRITICAL VALIDATION: Check for invalid instruction pointers
  if (current_decode_stage.pc == 0 || current_decode_stage.pc == 0xDEADBEEF ||
      current_decode_stage.pc == 0xCCCCCCCC ||
      current_decode_stage.pc == 0xFEEEFEEE) {
    spdlog::error(
        "Execute stage received invalid PC: 0x{:x}, aborting execution",
        current_decode_stage.pc);
    throw PipelineException("Invalid PC received in execute stage");
  }

  ExecuteStage exec;
  exec.instr = current_decode_stage.instr;
  exec.pc = current_decode_stage.pc;
  exec.valid = true;
  exec.predicted_taken = current_decode_stage.predicted_taken;
  exec.predicted_target = current_decode_stage.predicted_target;
  exec.execute_cycle = stats.cycles;
  exec.fetch_cycle = current_decode_stage.fetch_cycle;
  exec.prediction_confidence = current_decode_stage.prediction_confidence;

  // Allocate execution unit
  exec.unit_type = GetRequiredExecutionUnit(exec.instr);
  exec.execution_latency = GetInstructionLatency(exec.instr);

  ExecutionUnit *unit = AllocateExecutionUnit(exec.unit_type);
  if (!unit) {
    // This should ideally not happen if DetectStructuralHazard passed,
    // but as a safeguard.
    spdlog::error("Failed to allocate execution unit for PC 0x{:x} (type {}) "
                  "after hazard check.",
                  exec.pc, static_cast<int>(exec.unit_type));
    // Re-insert into decode stage or stall. For now, throw.
    throw PipelineException("Failed to allocate execution unit");
  }

  unit->available_cycle = stats.cycles + exec.execution_latency;
  unit->instruction_pc = exec.pc;

  spdlog::trace("Allocated execution unit type {} for PC 0x{:x}, latency={}",
                static_cast<int>(exec.unit_type), exec.pc,
                exec.execution_latency);

  // CRITICAL DEADLOCK FIX: Acquire mutex once and hold it for critical section
  // The InterpretInstruction function might interact with CPU state, which is
  // shared. The mutex should protect the pipeline's internal state and its
  // interaction with CPU. If InterpretInstruction itself needs to release the
  // mutex (e.g., for JIT compilation), that's a more advanced design. For now,
  // assume CPU operations are fast or internally synchronized.
  if (!mutex.try_lock()) {
    // If we can't acquire the lock, we must stall this instruction and try
    // again next cycle. Re-insert the instruction back to the front of the
    // decode stage.
    decodeStage.insert(decodeStage.begin(), std::move(current_decode_stage));
    spdlog::warn(
        "Pipeline::Execute() could not acquire mutex, stalling PC 0x{:x}",
        exec.pc);
    stats.lock_contentions++;
    stats.stalls++;
    return;
  }
  std::lock_guard<std::timed_mutex> lock(mutex, std::adopt_lock);

  try {
    InterpretInstruction(exec); // This modifies CPU state
    // After interpretation, the instruction is ready to move to the next stage.
    executeStage.push_back(std::move(exec));
    spdlog::trace("Execute stage processed at 0x{:x}", exec.pc);

  } catch (const std::exception &e) {
    spdlog::error("Execute stage failed at 0x{:x}: {}", exec.pc, e.what());
    stats.stalls++;
    // Release the allocated unit if execution failed
    ReleaseExecutionUnit(unit);
    // Flush the pipeline on critical error
    Flush();
    throw PipelineException("Execute stage failure");
  }
  // Mutex is automatically released when 'lock' goes out of scope
}

bool Pipeline::TryAcquireMutexWithBackoff(
    std::timed_mutex &mutex_ref,
    std::chrono::microseconds baseTimeout) {
  // Initial attempt
  if (mutex_ref.try_lock()) {
    contentionCounter.store(0); // Reset on success
    return true;
  }

  // Exponential backoff
  int current_backoff_count = contentionCounter.fetch_add(1);
  std::chrono::microseconds timeout = baseTimeout;
  if (current_backoff_count > 0) {
    timeout =
        baseTimeout * (1 << std::min(current_backoff_count, 10)); // Cap backoff
  }

  spdlog::trace("Mutex contention detected, backing off for {} us (attempt {})",
                timeout.count(), current_backoff_count);
  stats.lock_contentions++;

  if (mutex_ref.try_lock_for(timeout)) {
    contentionCounter.store(0); // Reset on success
    return true;
  }

  return false;
}

void Pipeline::FetchMultiple() {
  constexpr size_t MAX_FETCH = 4; // Fetch up to 4 instructions per cycle
  if (fetchStage.size() >= MAX_FETCH) {
    spdlog::trace("Fetch stage full ({} instructions), skipping fetch",
                  fetchStage.size());
    stats.stalls++;
    return;
  }

  if (!TryAcquireMutexWithBackoff(mutex, std::chrono::microseconds(100))) {
    spdlog::trace("Pipeline::FetchMultiple() mutex timeout - backing off");
    return;
  }
  // Mutex is now held by 'lock'

  uint64_t pc = 0;
  try {
    pc = cpu.GetRegister(Register::RIP);

    // CRITICAL SAFETY: Enhanced RIP validation with per-core offsets
    if (pc == 0 || pc == 0xDEADBEEF || pc == 0xCCCCCCCC || pc == 0xFEEEFEEE) {
      spdlog::error("Invalid RIP for fetch: 0x{:x}, aborting fetch", pc);
      Flush();
      throw PipelineException("Invalid RIP for fetch");
    }

    // IMPROVEMENT: Add per-core RIP offset to prevent all cores executing same
    // address. This is a heuristic and might not be suitable for all programs.
    // It assumes cores should start at different memory regions.
    if (pc == 0x400000) { // Common entry point
      uint32_t cpuId = cpu.GetCPUId();
      if (cpuId > 0) {
        uint64_t offsetPc = pc + (cpuId * 0x1000); // 4KB offset per core
        spdlog::debug("CPU[{}]: Adjusting default RIP from 0x{:x} to 0x{:x} to "
                      "prevent contention",
                      cpuId, pc, offsetPc);
        // Temporarily release lock to set register, then reacquire.
        // This is a potential race window, but CPU register access might be
        // atomic.
        mutex.unlock();
        cpu.SetRegister(Register::RIP, offsetPc);
        if (!TryAcquireMutexWithBackoff(mutex, std::chrono::microseconds(100))) {
          spdlog::warn(
              "Could not reacquire lock after RIP adjustment for PC 0x{:x}",
              offsetPc);
          return; // Stall and try again next cycle
        }
        pc = offsetPc; // Update local PC variable
      }
    }

    if (pc < 0x1000 || pc >= 0x800000000000ULL) { // Example valid range
      spdlog::error("RIP out of valid range for fetch: 0x{:x}, aborting fetch",
                    pc);
      Flush();
      throw PipelineException("RIP out of valid range for fetch");
    }
  } catch (const std::exception &e) {
    spdlog::error("Failed to get RIP for fetch: {}", e.what());
    stats.stalls++;
    Flush();
    throw PipelineException("Failed to get RIP for fetch");
  }

  // Buffer for fetching multiple instructions
  std::vector<uint8_t> buffer(
      16 * MAX_FETCH); // Max 16 bytes per instruction, 4 instructions
  try {
    // Read memory. This operation might be slow and could benefit from
    // releasing the lock. However, if `cpu.GetMemory().ReadVirt` is not
    // thread-safe, it needs the lock. Assuming it's thread-safe or protected
    // internally by `cpu`'s own mutex.
    mutex.unlock(); // Release pipeline mutex during potentially long memory read
    bool read_success = cpu.GetMemory().ReadVirt(
        pc, buffer.data(), buffer.size(), cpu.GetProcessId());
    if (!read_success) {
      spdlog::error("Fetch failed at 0x{:x}: memory not accessible", pc);
      // Reacquire lock before flushing or throwing
      if (!TryAcquireMutexWithBackoff(mutex, std::chrono::microseconds(100))) {
        spdlog::warn(
            "Could not reacquire lock after memory read failure for PC 0x{:x}",
            pc);
        return;
      }
      stats.stalls++;
      Flush();
      throw PipelineException("Fetch memory access failure");
    }
    // Reacquire lock after memory read
    if (!TryAcquireMutexWithBackoff(mutex, std::chrono::microseconds(100))) {
      spdlog::warn(
          "Could not reacquire lock after fetch memory read for PC 0x{:x}", pc);
      return;
    }
    // Mutex is now held by 'lock' again.

    size_t current_offset = 0;
    uint64_t current_pc_for_decode = pc; // Start decoding from the current PC

    for (size_t i = 0; i < MAX_FETCH && current_offset < buffer.size(); ++i) {
      if (fetchStage.size() >= MAX_FETCH) {
        break; // Fetch stage is full
      }

      DecodedInstruction tempInstr;
      DecoderErrorInfo errorInfo =
          decoder.Decode(current_pc_for_decode, buffer.data() + current_offset,
                         buffer.size() - current_offset, tempInstr);

      if (errorInfo.error != DecoderError::Success) {
        spdlog::error("Fetch decode failed at 0x{:x}: error={}",
                      current_pc_for_decode, static_cast<int>(errorInfo.error));
        // Trigger an interrupt for invalid instruction
        mutex.unlock(); // Release pipeline lock before triggering CPU interrupt
        try {
          cpu.TriggerInterrupt(EXC_UD, 0,
                               false); // Undefined Instruction Exception
        } catch (const std::exception &e) {
          spdlog::error("Failed to trigger interrupt after decode failure: {}",
                        e.what());
        }
        // No need to reacquire lock here, as we are stopping fetch for this
        // cycle.
        return;
      }

      FetchStage stage;
      stage.pc = current_pc_for_decode;
      stage.instr = tempInstr;
      stage.valid = true;
      stage.fetch_cycle = stats.cycles;

      // Branch prediction logic
      if (IsBranchInstruction(tempInstr)) {
        uint64_t nextRip = stage.pc + tempInstr.length;

        stage.predicted_taken = branchPredictor.PredictTaken(stage.pc);
        stage.predicted_target =
            branchPredictor.PredictTarget(stage.pc, tempInstr, nextRip);

        // Set confidence based on predictor type
        if (branchPredictor.IsIndirectBranch(tempInstr)) {
          stage.prediction_confidence =
              1; // Lower confidence for indirect branches
        } else {
          stage.prediction_confidence =
              2; // Higher confidence for direct branches
        }

        if (stage.predicted_taken && stage.predicted_target != 0) {
          // If predicted taken and target is valid, update PC for next fetch
          current_pc_for_decode = stage.predicted_target;
          spdlog::trace("Branch prediction: taken at 0x{:x}, target=0x{:x}, "
                        "confidence={}",
                        stage.pc, stage.predicted_target,
                        stage.prediction_confidence);
        } else {
          // If predicted not taken or target is 0 (unknown), fall through
          current_pc_for_decode = nextRip;
        }
      } else {
        // Not a branch, simply advance PC
        current_pc_for_decode += tempInstr.length;
      }

      fetchStage.push_back(stage);
      current_offset += tempInstr.length;

      // If a branch was predicted taken, stop fetching more instructions
      // from the current linear path, as the next fetch will be from the
      // target.
      if (stage.predicted_taken && stage.predicted_target != 0) {
        spdlog::trace("Stopping fetch at predicted taken branch (PC 0x{:x})",
                      stage.pc);
        break;
      }
    }
    // Update CPU's RIP to the next instruction to be fetched (predicted or
    // sequential) This is crucial for the next cycle's fetch.
    cpu.SetRegister(Register::RIP, current_pc_for_decode);

  } catch (const std::exception &e) {
    spdlog::error("Fetch failed at 0x{:x}: {}", pc, e.what());
    // Reacquire lock before flushing or throwing
    if (!TryAcquireMutexWithBackoff(mutex, std::chrono::microseconds(100))) {
      spdlog::warn(
          "Could not reacquire lock after fetch exception for PC 0x{:x}", pc);
      return;
    }
    stats.stalls++;
    Flush();
    throw PipelineException("Fetch operation failure");
  }
  // Mutex is automatically released when 'lock' goes out of scope
}

void Pipeline::Decode() {
  if (fetchStage.empty() || decodeStage.size() >= 8) { // Decode buffer size
    spdlog::trace("Decode stage full ({} instructions) or fetch stage empty, "
                  "skipping decode",
                  decodeStage.size());
    stats.stalls++;
    return;
  }

  // Move instruction from fetch to decode stage
  FetchStage current_fetch_stage = std::move(fetchStage.front());
  fetchStage.erase(fetchStage.begin());

  if (!current_fetch_stage.valid) {
    spdlog::warn("Invalid fetch stage entry, skipping decode");
    return;
  }

  try {
    // Validate instruction before moving to decode stage
    if (!current_fetch_stage.instr.validate()) {
      spdlog::error(
          "Pipeline: Invalid instruction in fetch stage at PC 0x{:x}, dropping",
          current_fetch_stage.pc);
      stats.stalls++;
      return;
    }

    DecodeStage decode;
    decode.instr = current_fetch_stage.instr;
    decode.pc = current_fetch_stage.pc;
    decode.valid = true;
    decode.predicted_taken = current_fetch_stage.predicted_taken;
    decode.predicted_target = current_fetch_stage.predicted_target;
    decode.decode_cycle = stats.cycles;
    decode.fetch_cycle = current_fetch_stage.fetch_cycle;
    decode.prediction_confidence = current_fetch_stage.prediction_confidence;

    // Validate instruction after copying to decode stage
    if (!decode.instr.validate()) {
      spdlog::error("Pipeline: Instruction corruption during fetch->decode "
                    "transition at PC 0x{:x}",
                    decode.pc);
      stats.stalls++;
      return;
    }

    // Update register dependencies for this instruction
    UpdateRegisterDependencies(decode.instr, stats.cycles);

    spdlog::debug(
        "Decoded at 0x{:x}: type={}, operands={}", current_fetch_stage.pc,
        static_cast<int>(decode.instr.instType), decode.instr.operandCount);
    decodeStage.push_back(std::move(decode));

    // If the branch was predicted NOT taken, the RIP was already updated in
    // FetchMultiple to the sequential next instruction. If it was predicted
    // taken, RIP was updated to the target. So, no need to update RIP here
    // based on `!stage.predicted_taken`. The RIP is managed by the Fetch
    // stage's branch prediction logic.
  } catch (const std::exception &e) {
    spdlog::error("Decode failed at 0x{:x}: {}", current_fetch_stage.pc,
                  e.what());
    stats.stalls++;
    Flush();
    throw PipelineException("Decode failure");
  }
}

void Pipeline::Memory() {
  if (executeStage.empty() || memoryStage.size() >= 4) { // Memory buffer size
    spdlog::trace("Memory stage full ({} instructions) or execute stage empty, "
                  "skipping memory",
                  memoryStage.size());
    stats.stalls++;
    return;
  }

  // Find an instruction in execute stage that has completed its execution
  auto it = executeStage.begin();
  while (it != executeStage.end()) {
    if (!it->valid) {
      it = executeStage.erase(it); // Remove invalid entries
      continue;
    }

    // An instruction can move to memory stage if its execution unit is free
    // (meaning its execution latency has passed).
    // We need to find the specific unit it was allocated to.
    // This requires tracking which unit an instruction used.
    // For simplicity, we'll assume if `available_cycle` has passed, it's done.
    // A more robust way would be to store the `ExecutionUnit*` in
    // `ExecuteStage`.

    // Find the unit that this instruction used
    ExecutionUnit *used_unit = nullptr;
    auto unit_type_it = unit_map.find(it->unit_type);
    if (unit_type_it != unit_map.end()) {
      for (size_t unit_idx : unit_type_it->second) {
        if (execution_units[unit_idx].instruction_pc == it->pc &&
            execution_units[unit_idx]
                .busy) { // Check if this unit is busy with *this* instruction
          used_unit = &execution_units[unit_idx];
          break;
        }
      }
    }

    if (!used_unit || used_unit->available_cycle > stats.cycles) {
      // Instruction is still executing or unit not found (error)
      spdlog::trace("PC 0x{:x} still executing (unit available cycle: {} vs "
                    "current cycle: {})",
                    it->pc, used_unit ? used_unit->available_cycle : 0,
                    stats.cycles);
      ++it; // Check next instruction in execute stage
      continue;
    }

    // Instruction has completed execution, release its unit
    ReleaseExecutionUnit(used_unit);
    break; // Found an instruction ready for memory stage
  }

  if (it == executeStage.end()) {
    spdlog::trace("No instruction ready for memory stage in execute stage.");
    stats.stalls++;
    return;
  }

  // Move the instruction from execute to memory stage
  ExecuteStage current_exec_stage = std::move(*it);
  executeStage.erase(it);

  // Validate instruction before moving to memory stage
  if (!current_exec_stage.instr.validate()) {
    spdlog::error(
        "Pipeline: Invalid instruction in execute stage at PC 0x{:x}, dropping",
        current_exec_stage.pc);
    stats.stalls++;
    return;
  }

  // Simulate memory operation latency (simplified)
  // In real implementation, would interact with memory subsystem
  spdlog::trace("Memory stage processing PC 0x{:x}", current_exec_stage.pc);

  MemoryStage mem_stage;
  mem_stage.instr = current_exec_stage.instr;
  mem_stage.pc = current_exec_stage.pc;
  mem_stage.valid = true;
  mem_stage.memory_cycle = stats.cycles;
  mem_stage.fetch_cycle = current_exec_stage.fetch_cycle;

  // Validate instruction after copying to memory stage
  if (!mem_stage.instr.validate()) {
    spdlog::error("Pipeline: Instruction corruption during execute->memory "
                  "transition at PC 0x{:x}",
                  mem_stage.pc);
    stats.stalls++;
    return;
  }

  memoryStage.push_back(std::move(mem_stage));
}

void Pipeline::WriteBack() {
  if (memoryStage.empty() ||
      writeBackStage.size() >= 4) { // WriteBack buffer size
    spdlog::trace("WriteBack stage full ({} instructions) or memory stage "
                  "empty, skipping writeback",
                  writeBackStage.size());
    stats.stalls++;
    return;
  }

  // In a simple in-order pipeline, the first instruction in memory stage is
  // ready for writeback.
  auto it = memoryStage.begin();
  while (it != memoryStage.end()) {
    if (!it->valid) {
      it = memoryStage.erase(it); // Remove invalid entries
      continue;
    }
    break; // Found a valid instruction
  }

  if (it == memoryStage.end()) {
    spdlog::trace("No instruction ready for writeback stage in memory stage.");
    stats.stalls++;
    return;
  }

  // Move the instruction from memory to writeback stage
  MemoryStage current_mem_stage = std::move(*it);
  memoryStage.erase(it);

  // Validate instruction before moving to writeback stage
  if (!current_mem_stage.instr.validate()) {
    spdlog::error(
        "Pipeline: Invalid instruction in memory stage at PC 0x{:x}, dropping",
        current_mem_stage.pc);
    stats.stalls++;
    return;
  }

  spdlog::trace("WriteBack stage processing PC 0x{:x}", current_mem_stage.pc);

  WriteBackStage wb_stage;
  wb_stage.instr = current_mem_stage.instr;
  wb_stage.pc = current_mem_stage.pc;
  wb_stage.valid = true;
  wb_stage.writeback_cycle = stats.cycles;
  wb_stage.fetch_cycle = current_mem_stage.fetch_cycle;

  // Validate instruction after copying to writeback stage
  if (!wb_stage.instr.validate()) {
    spdlog::error("Pipeline: Instruction corruption during memory->writeback "
                  "transition at PC 0x{:x}",
                  wb_stage.pc);
    stats.stalls++;
    return;
  }

  // Resolve register dependencies for registers written by this instruction
  std::vector<uint32_t> writes = GetRegisterWrites(wb_stage.instr);
  for (uint32_t reg_id : writes) {
    ResolveRegisterDependency(reg_id, stats.cycles);
  }

  writeBackStage.push_back(std::move(wb_stage));

  // Finalize instruction execution, update stats
  stats.instructionsExecuted++;
  // stats.cycles is incremented once per Step() call, not per instruction here.
}

void Pipeline::Flush() {
  spdlog::info("Flushing entire pipeline");
  fetchStage.clear();
  decodeStage.clear();
  executeStage.clear();
  memoryStage.clear();
  writeBackStage.clear();

  // Reset execution units
  for (auto &unit : execution_units) {
    unit.busy = false;
    unit.available_cycle = 0;
    unit.instruction_pc = 0;
  }

  // Reset register dependencies
  register_dependency_map.clear();
  instruction_dependency_map.clear();

  // Reset register scoreboard
  for (auto &[reg_id, available] : register_scoreboard) {
    available = true;
  }

  // Reset register renaming
  FlushRegisterMappings();
  // Reset contention counter
  contentionCounter.store(0);
}

void Pipeline::FlushFromStage(int stage_num) {
  spdlog::info("Flushing pipeline from stage {}", stage_num);
  switch (stage_num) {
  case 1: // Fetch
    fetchStage.clear();
    [[fallthrough]];
  case 2: // Decode
    decodeStage.clear();
    [[fallthrough]];
  case 3: // Execute
    executeStage.clear();
    // Also clear execution units associated with flushed instructions
    for (auto &unit : execution_units) {
      // This is a heuristic. A more precise flush would track which instruction
      // is in which unit and only clear those.
      if (unit.busy &&
          unit.available_cycle >
              stats.cycles) { // If unit is busy with a future instruction
        unit.busy = false;
        unit.available_cycle = 0;
        unit.instruction_pc = 0;
      }
    }
    [[fallthrough]];
  case 4: // Memory
    memoryStage.clear();
    [[fallthrough]];
  case 5: // WriteBack
    writeBackStage.clear();
    break;
  default:
    spdlog::warn("Invalid stage number for FlushFromStage: {}", stage_num);
    break;
  }
  // Reset contention counter
  contentionCounter.store(0);
}

void Pipeline::Step() {
  // Order of operations: Commit -> WriteBack -> Memory -> Execute -> Decode ->
  // Fetch This simulates the progression of instructions through the pipeline
  // stages.
  try {
    // Commit phase for out-of-order execution
    if (IsOutOfOrderEnabled()) {
      // CommitInstructions(); // Simplified - removed duplicate
    }

    // Schedule instructions from decode to reservation stations
    if (IsOutOfOrderEnabled()) {
      // ScheduleInstructions(); // Simplified - removed duplicate
    }

    // Dispatch ready instructions from reservation stations
    if (IsOutOfOrderEnabled()) {
      // DispatchReadyInstructions(); // Simplified - removed duplicate
    }

    WriteBack();
    Memory();
    Execute();
    Decode();
    FetchMultiple();
    stats.cycles++; // Increment global cycle counter once per step

    // Periodically clean old dependencies (every 100 cycles)
    if (stats.cycles % 100 == 0) {
      ClearOldDependencies(stats.cycles);
    }

    spdlog::trace("Pipeline step completed, cycle: {}", stats.cycles);
    UpdateProfiling(); // Update profiling data at the end of each step
  } catch (const PipelineException &e) {
    spdlog::error("Pipeline step failed with PipelineException: {}", e.what());
    Flush(); // Critical error, flush pipeline
    throw;   // Re-throw to caller
  } catch (const std::exception &e) {
    spdlog::error("Pipeline step failed with generic exception: {}", e.what());
    Flush(); // Critical error, flush pipeline
    throw PipelineException("Pipeline step failure: " + std::string(e.what()));
  }
}

void Pipeline::ResetStats() {
  stats = PipelineStats(); // Reset to default values
  branchPredictor.ResetStats();
  profilingData.clear(); // Clear profiling data
  spdlog::info("Pipeline statistics reset");
}

void Pipeline::InterpretInstruction(ExecuteStage &exec) {
  try {
    // Validate instruction before execution
    if (!exec.instr.validate()) {
      spdlog::error("Invalid instruction at PC 0x{:x}: length={}, "
                    "operandCount={}, instType={}",
                    exec.pc, exec.instr.length, exec.instr.operandCount,
                    static_cast<int>(exec.instr.instType));
      throw PipelineException("Invalid instruction");
    }

    // Additional safety check for operand count
    if (exec.instr.operandCount > 4) {
      spdlog::error("Instruction at PC 0x{:x} has invalid operandCount: {}",
                    exec.pc, exec.instr.operandCount);
      throw PipelineException("Invalid operand count");
    }

    // Use dispatch table for efficient instruction execution
    auto it = opcodeDispatchTable.find(static_cast<uint16_t>(exec.instr.instType));
    if (it != opcodeDispatchTable.end()) {
      // Execute the instruction using the dispatch table
      it->second(exec);
    } else {
      // Fallback for unhandled instructions - log and handle gracefully
      spdlog::warn(
          "Unhandled instruction type {} at PC 0x{:x}, using fallback handler",
          static_cast<int>(exec.instr.instType), exec.pc);

      // Basic fallback handling for common instruction patterns
      HandleFallbackInstruction(exec);
    }

    // stats.instructionsExecuted is incremented in WriteBack, not here.
    spdlog::trace("Interpreted instruction at PC 0x{:x}", exec.pc);
  } catch (const PipelineException &e) {
    // Re-throw pipeline exceptions
    throw;
  } catch (const std::exception &e) {
    spdlog::error("Instruction interpretation failed at 0x{:x}: {}", exec.pc,
                  e.what());
    throw PipelineException("Instruction interpretation failure");
  }
}

// Fallback handler for unhandled instructions
void Pipeline::HandleFallbackInstruction(ExecuteStage &exec) {
  using InstructionType = x86_64::InstructionType;

  // Handle common instruction patterns that aren't in the dispatch table yet
  switch (exec.instr.instType) {
  case InstructionType::Mov:
    // Handle MOV instruction variants
    if (exec.instr.operandCount >= 2) {
      if (exec.instr.operands[0].type ==
              DecodedInstruction::Operand::Type::REGISTER &&
          exec.instr.operands[1].type ==
              DecodedInstruction::Operand::Type::REGISTER) {
        cpu.SetRegister(exec.instr.operands[0].reg,
                        cpu.GetRegister(exec.instr.operands[1].reg));
      } else if (exec.instr.operands[0].type ==
                     DecodedInstruction::Operand::Type::REGISTER &&
                 exec.instr.operands[1].type ==
                     DecodedInstruction::Operand::Type::IMMEDIATE) {
        cpu.SetRegister(exec.instr.operands[0].reg,
                        exec.instr.operands[1].immediate);
      }
      // Add more MOV variants as needed
    }
    break;

  case InstructionType::Add:
    // Handle ADD instruction variants
    if (exec.instr.operandCount >= 2 &&
        exec.instr.operands[0].type ==
            DecodedInstruction::Operand::Type::REGISTER) {
      uint64_t op1 = cpu.GetRegister(exec.instr.operands[0].reg);
      uint64_t op2 = (exec.instr.operands[1].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[1].reg)
                         : exec.instr.operands[1].immediate;
      cpu.SetRegister(exec.instr.operands[0].reg, op1 + op2);
    }
    break;

  case InstructionType::Sub:
    // Handle SUB instruction variants
    if (exec.instr.operandCount >= 2 &&
        exec.instr.operands[0].type ==
            DecodedInstruction::Operand::Type::REGISTER) {
      uint64_t op1 = cpu.GetRegister(exec.instr.operands[0].reg);
      uint64_t op2 = (exec.instr.operands[1].type ==
                      DecodedInstruction::Operand::Type::REGISTER)
                         ? cpu.GetRegister(exec.instr.operands[1].reg)
                         : exec.instr.operands[1].immediate;
      cpu.SetRegister(exec.instr.operands[0].reg, op1 - op2);
    }
    break;

  default:
    // For unhandled instructions, just log and continue
    spdlog::warn("Fallback handler: Unhandled instruction type {} at PC 0x{:x}",
                 static_cast<int>(exec.instr.instType), exec.pc);
    break;
  }
}

bool Pipeline::IsBranchInstruction(const DecodedInstruction &instr) const {
  using InstructionType = x86_64::InstructionType;
  switch (instr.instType) {
  case InstructionType::Jump:
  case InstructionType::Jcc:
  case InstructionType::Call:
  case InstructionType::Ret:
    return true;
  default:
    return false;
  }
}

bool Pipeline::IsSIMDInstruction(const DecodedInstruction &instr) const {
  using InstructionType = x86_64::InstructionType;
  switch (instr.instType) {
  case InstructionType::Addps:
  case InstructionType::Subps:
  case InstructionType::Mulps:
  case InstructionType::Divps:
  case InstructionType::Sqrtps:
  case InstructionType::Maxps:
  case InstructionType::Minps:
  case InstructionType::Andps:
  case InstructionType::Orps:
  case InstructionType::Xorps:
  case InstructionType::Movaps:
  case InstructionType::Movups:
  case InstructionType::Paddb:   // Example SIMD integer
  case InstructionType::Pcmpeqb: // Example SIMD comparison
    return true;
  default:
    return false;
  }
}

const DecodedInstruction::Operand *
Pipeline::SafeGetOperand(const DecodedInstruction &instr, uint8_t index) const {
  // CRITICAL: Multiple bounds checks to prevent vector out of range errors

  // First check: Validate index against array size (most critical)
  if (index >= 4) {
    spdlog::error(
        "SafeGetOperand: Array bounds violation - index {} >= 4",
        index);
    return nullptr;
  }

  // Second check: Validate operand count consistency
  if (instr.operandCount > 4) {
    spdlog::error("SafeGetOperand: Invalid operand count {} > 4",
                  instr.operandCount);
    return nullptr;
  }

  // Third check: Validate index against operand count
  if (index >= instr.operandCount) {
    spdlog::error("SafeGetOperand: Invalid operand index {} for instruction "
                  "with {} operands",
                  index, instr.operandCount);
    return nullptr;
  }

  // Fourth check: Additional validation - validate instruction first
  if (!instr.validate()) {
    spdlog::error("SafeGetOperand: Invalid instruction");
    return nullptr;
  }

  // SAFE ACCESS: All bounds checks passed, now safe to access array
  const auto &operand = instr.operands[index];

  // Final validation: Check operand validity
  if (operand.size == 0) {
    spdlog::error("SafeGetOperand: Operand {} has invalid size 0",
                  index);
    return nullptr;
  }

  return &instr.operands[index];
}

std::function<void(ExecuteStage&)> Pipeline::CreateSafeHandler(std::function<void(ExecuteStage&)> handler) const {
  return [this, handler](ExecuteStage &exec) {
    // Validate instruction first
    if (!exec.instr.validate()) {
      spdlog::error("Invalid instruction at PC 0x{:x}", exec.pc);
      return;
    }

    // Call the actual handler
    try {
      handler(exec);
    } catch (const std::exception &e) {
      spdlog::error("Handler exception at PC 0x{:x}: {}", exec.pc, e.what());
    }
  };
}

// Removed PredictBranchTarget as its logic is integrated into BranchPredictor
// and FetchMultiple.

bool Pipeline::Conflicts(const DecodedInstruction &instr1, uint64_t pc1,
                         const DecodedInstruction &instr2, uint64_t pc2) const {
  // This function checks for register conflicts between two instructions.
  // It's used for RAW, WAW, WAR hazard detection.

  // For simplicity, assume instr1 is the "earlier" instruction in the pipeline
  // (e.g., in Execute/Memory stage) and instr2 is the "later" instruction
  // (e.g., in Decode stage, trying to move to Execute).

  // Check for RAW (Read After Write): instr2 reads what instr1 writes
  // instr1 writes to its first operand (destination)
  if (instr1.operandCount > 0 &&
      (instr1.operands[0].type == DecodedInstruction::Operand::Type::REGISTER ||
       instr1.operands[0].type == DecodedInstruction::Operand::Type::XMM)) {
    uint32_t instr1_dest_reg = static_cast<uint32_t>(instr1.operands[0].reg);
    // instr2 reads from any of its operands (sources)
    for (int i = 0; i < instr2.operandCount; ++i) {
      // A simple heuristic for read operand: not the first operand, or if it's
      // a read-modify-write
      bool instr2_is_read_operand =
          (i > 0) || (instr2.isReadModifyWrite && i == 0);
      if ((instr2.operands[i].type ==
               DecodedInstruction::Operand::Type::REGISTER ||
           instr2.operands[i].type == DecodedInstruction::Operand::Type::XMM) &&
          instr2_is_read_operand &&
          static_cast<uint32_t>(instr2.operands[i].reg) == instr1_dest_reg) {
        spdlog::trace("RAW Conflict: instr2 (0x{:x}) reads reg {} written by "
                      "instr1 (0x{:x})",
                      pc2, instr1_dest_reg, pc1);
        return true;
      }
    }
  }

  // Check for WAW (Write After Write): instr2 writes what instr1 writes
  // instr1 writes to its first operand (destination)
  // instr2 writes to its first operand (destination)
  if (instr1.operandCount > 0 && instr2.operandCount > 0 &&
      (instr1.operands[0].type == DecodedInstruction::Operand::Type::REGISTER ||
       instr1.operands[0].type == DecodedInstruction::Operand::Type::XMM) &&
      (instr2.operands[0].type == DecodedInstruction::Operand::Type::REGISTER ||
       instr2.operands[0].type == DecodedInstruction::Operand::Type::XMM)) {
    uint32_t instr1_dest_reg = static_cast<uint32_t>(instr1.operands[0].reg);
    uint32_t instr2_dest_reg = static_cast<uint32_t>(instr2.operands[0].reg);
    if (instr1_dest_reg == instr2_dest_reg) {
      spdlog::trace("WAW Conflict: both instr1 (0x{:x}) and instr2 (0x{:x}) "
                    "write to reg {}",
                    pc1, pc2, instr1_dest_reg);
      return true;
    }
  }

  // Check for WAR (Write After Read): instr2 writes what instr1 reads
  // This is less common in simple in-order pipelines, but for completeness:
  // instr2 writes to its first operand (destination)
  if (instr2.operandCount > 0 &&
      (instr2.operands[0].type == DecodedInstruction::Operand::Type::REGISTER ||
       instr2.operands[0].type == DecodedInstruction::Operand::Type::XMM)) {
    uint32_t instr2_dest_reg = static_cast<uint32_t>(instr2.operands[0].reg);
    // instr1 reads from any of its operands (sources)
    for (int i = 0; i < instr1.operandCount; ++i) {
      bool instr1_is_read_operand =
          (i > 0) || (instr1.isReadModifyWrite && i == 0);
      if ((instr1.operands[i].type ==
               DecodedInstruction::Operand::Type::REGISTER ||
           instr1.operands[i].type == DecodedInstruction::Operand::Type::XMM) &&
          instr1_is_read_operand &&
          static_cast<uint32_t>(instr1.operands[i].reg) == instr2_dest_reg) {
        spdlog::trace("WAR Conflict: instr2 (0x{:x}) writes reg {} read by "
                      "instr1 (0x{:x})",
                      pc2, instr2_dest_reg, pc1);
        return true;
      }
    }
  }

  return false;
}

bool Pipeline::ValidatePipelineState() const {
  // Basic validation: check if stages are not excessively large or invalid
  if (fetchStage.size() > 8 || decodeStage.size() > 8 ||
      executeStage.size() > 4 || memoryStage.size() > 4 ||
      writeBackStage.size() > 4) {
    spdlog::error(
        "Pipeline state validation failed: stage sizes exceed limits.");
    return false;
  }
  // More complex checks could involve:
  // - Ensuring PCs are monotonically increasing within a linear block
  // - Checking for valid instruction types in each stage
  // - Verifying that execution units are correctly allocated/released
  return true;
}

void Pipeline::UpdateProfiling() {
  // Record current pipeline stats for profiling
  ProfilingEntry entry;
  entry.cycle = stats.cycles;
  entry.instructions = stats.instructionsExecuted;
  entry.stalls = stats.stalls;
  entry.timestamp = std::chrono::steady_clock::now();
  profilingData.push_back(entry);

  // Optionally, trim old profiling data if it gets too large
  if (profilingData.size() > 10000) { // Keep last 10,000 entries
    profilingData.erase(profilingData.begin(), profilingData.begin() + 5000);
  }
}

std::unordered_map<std::string, uint64_t> Pipeline::GetDiagnostics() const {
  std::unordered_map<std::string, uint64_t> diagnostics;
  diagnostics["cycles"] = stats.cycles;
  diagnostics["instructionsExecuted"] = stats.instructionsExecuted;
  diagnostics["stalls"] = stats.stalls;
  diagnostics["data_hazard_stalls"] = stats.data_hazard_stalls;
  diagnostics["structural_hazard_stalls"] = stats.structural_hazard_stalls;
  diagnostics["branch_hits"] = stats.branch_hits;
  diagnostics["branch_mispredictions"] = stats.branch_mispredictions;
  diagnostics["lock_contentions"] = stats.lock_contentions;
  diagnostics["fetchStageOccupancy"] = fetchStage.size();
  diagnostics["decodeStageOccupancy"] = decodeStage.size();
  diagnostics["executeStageOccupancy"] = executeStage.size();
  diagnostics["memoryStageOccupancy"] = memoryStage.size();
  diagnostics["writeBackStageOccupancy"] = writeBackStage.size();
  diagnostics["activeExecutionUnits"] = 0;
  for (const auto &unit : execution_units) {
    if (unit.busy) {
      diagnostics["activeExecutionUnits"]++;
    }
  }
  return diagnostics;
}

void Pipeline::SwitchToFiber(void* fiberHandle) {
  spdlog::warn("Pipeline::SwitchToFiber is a placeholder and does not "
               "implement actual fiber switching.");
  // In a real emulator, this would involve:
  // 1. Saving current CPU context (registers, stack pointer, etc.)
  // 2. Loading context of the target fiber.
  // 3. Updating CPU's active fiber ID.
  // 4. Potentially flushing pipeline if the new fiber's PC is very different.
  // This requires interaction with a FiberManager (ps4/fiber_manager.h).
  // For now, just log and do nothing.
}

void Pipeline::InitializeRegisterDependencyTracking() {
  // Initialize register scoreboard
  for (uint32_t i = 0; i < 16; i++) {
    // General purpose registers (RAX, RBX, etc.)
    register_scoreboard[i] = true; // Initially available
  }

  for (uint32_t i = 16; i < 32; i++) {
    // XMM registers
    register_scoreboard[i] = true; // Initially available
  }

  // Initialize physical register file for register renaming
  physical_register_file.resize(64); // 64 physical registers
  for (uint32_t i = 0; i < 64; i++) {
    physical_register_file[i].physical_id = i;
    physical_register_file[i].available = true;
    free_physical_registers.push(i);
  }

  // Initialize architectural to physical register mapping
  for (uint32_t i = 0; i < 32; i++) {
    architectural_to_physical[i] = i; // Initially 1:1 mapping
  }

  spdlog::debug("Pipeline: Register dependency tracking initialized");
}

void Pipeline::UpdateRegisterDependencies(const DecodedInstruction &instr,
                                          uint64_t cycle) {
  // Get the PC of the instruction
  uint64_t pc = 0;
  for (const auto &stage : decodeStage) {
    if (stage.valid && &stage.instr == &instr) {
      pc = stage.pc;
      break;
    }
  }

  // Update instruction dependencies
  UpdateInstructionDependencies(instr, pc, cycle);

  // Update register dependencies
  std::vector<uint32_t> writes = GetRegisterWrites(instr);
  std::vector<uint32_t> reads = GetRegisterReads(instr);

  // Update write dependencies
  for (uint32_t reg_id : writes) {
    RegisterDependency &dep = register_dependency_map[reg_id];
    dep.reg_id = reg_id;
    dep.pc = pc;
    dep.cycle = cycle;
    dep.ready = false;

    // Mark register as busy in scoreboard
    MarkRegisterBusy(reg_id, cycle);
  }

  // Update read dependencies
  for (uint32_t reg_id : reads) {
    RegisterDependency &dep = register_dependency_map[reg_id];
    dep.reg_id = reg_id;
    dep.pc = pc;
    dep.cycle = cycle;
    dep.ready = true; // Reads are typically ready immediately
  }

  spdlog::trace(
      "Pipeline: Updated register dependencies for instruction at 0x{:x}", pc);
}

void Pipeline::UpdateInstructionDependencies(const DecodedInstruction &instr,
                                             uint64_t pc, uint64_t cycle) {
  InstructionDependency &dep = instruction_dependency_map[pc];
  dep.pc = pc;
  dep.cycle = cycle;

  // Get register reads and writes
  dep.reads = GetRegisterReads(instr);
  dep.writes = GetRegisterWrites(instr);

  // Determine if instruction is ready to execute
  dep.ready = true;
  for (uint32_t reg_id : dep.reads) {
    if (!CheckRegisterAvailability(reg_id)) {
      dep.ready = false;
      break;
    }
  }

  // Set execution latency
  dep.latency = GetInstructionLatency(instr);

  spdlog::trace(
      "Pipeline: Instruction at 0x{:x} has {} reads and {} writes", pc,
      dep.reads.size(), dep.writes.size());
}

std::vector<uint32_t>
Pipeline::GetRegisterReads(const DecodedInstruction &instr) const {
  std::vector<uint32_t> reads;

  // Check all operands for register reads
  for (int i = 0; i < instr.operandCount; ++i) {
    bool is_read_operand = (i > 0) || (instr.isReadModifyWrite && i == 0);
    if ((instr.operands[i].type ==
             DecodedInstruction::Operand::Type::REGISTER ||
         instr.operands[i].type == DecodedInstruction::Operand::Type::XMM) &&
        is_read_operand) {
      uint32_t reg_id = static_cast<uint32_t>(instr.operands[i].reg);
      reads.push_back(reg_id);
    }
  }

  // Check for implicit register dependencies
  switch (instr.instType) {
  case InstructionType::Push:
  case InstructionType::Pop:
  case InstructionType::Call:
  case InstructionType::Ret:
    // These instructions implicitly use RSP
    reads.push_back(static_cast<uint32_t>(Register::RSP));
    break;
  case InstructionType::Mul:
  case InstructionType::Div:
  case InstructionType::Idiv:
    // These instructions implicitly use RAX and sometimes RDX
    reads.push_back(static_cast<uint32_t>(Register::RAX));
    reads.push_back(static_cast<uint32_t>(Register::RDX));
    break;
  default:
    break;
  }

  return reads;
}

std::vector<uint32_t>
Pipeline::GetRegisterWrites(const DecodedInstruction &instr) const {
  std::vector<uint32_t> writes;

  // Check destination operand
  if (instr.operandCount > 0 &&
      (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER ||
       instr.operands[0].type == DecodedInstruction::Operand::Type::XMM)) {
    uint32_t reg_id = static_cast<uint32_t>(instr.operands[0].reg);
    writes.push_back(reg_id);
  }

  // Check for implicit register writes
  switch (instr.instType) {
  case InstructionType::Push:
  case InstructionType::Pop:
  case InstructionType::Call:
  case InstructionType::Ret:
    // These instructions implicitly modify RSP
    writes.push_back(static_cast<uint32_t>(Register::RSP));
    break;
  case InstructionType::Mul:
  case InstructionType::Div:
  case InstructionType::Idiv:
    // These instructions implicitly modify RAX and RDX
    writes.push_back(static_cast<uint32_t>(Register::RAX));
    writes.push_back(static_cast<uint32_t>(Register::RDX));
    break;
  default:
    break;
  }

  return writes;
}

bool Pipeline::CheckRegisterAvailability(uint32_t reg_id) const {
  auto it = register_scoreboard.find(reg_id);
  if (it != register_scoreboard.end()) {
    return it->second; // true if available, false if busy
  }
  return true; // Default to available if not tracked
}

void Pipeline::MarkRegisterBusy(uint32_t reg_id, uint64_t cycle) {
  register_scoreboard[reg_id] = false;

  // Update register dependency
  if (register_dependency_map.count(reg_id) > 0) {
    RegisterDependency &dep = register_dependency_map[reg_id];
    dep.ready = false;
    dep.cycle = cycle;
  }

  spdlog::trace("Pipeline: Register {} marked busy at cycle {}",
                reg_id, cycle);
}

void Pipeline::MarkRegisterReady(uint32_t reg_id) {
  register_scoreboard[reg_id] = true;

  // Update register dependency
  if (register_dependency_map.count(reg_id) > 0) {
    RegisterDependency &dep = register_dependency_map[reg_id];
    dep.ready = true;

    spdlog::trace("Pipeline: Register {} marked ready", reg_id);
  }
}

bool Pipeline::CanExecuteInstruction(const DecodedInstruction &instr) const {
  // Check if all required registers are available
  for (int i = 0; i < instr.operandCount; ++i) {
    if (instr.operands[i].type == DecodedInstruction::Operand::Type::REGISTER) {
      uint32_t reg_id = static_cast<uint32_t>(instr.operands[i].reg);
      if (!CheckRegisterAvailability(reg_id)) {
        return false;
      }
    }
  }
  return true;
}

void Pipeline::ResolveRegisterDependency(uint32_t reg_id, uint64_t cycle) {
  // Mark register as ready
  MarkRegisterReady(reg_id, cycle);

  // Update all instructions that depend on this register
  for (auto &[pc, dep] : instruction_dependency_map) {
    // Check if this instruction reads from the resolved register
    for (uint32_t read_reg : dep.reads) {
      if (read_reg == reg_id) {
        dep.ready = true;
        spdlog::trace("Pipeline: Resolved dependency for instruction at 0x{:x} "
                      "on register {}",
                      pc, reg_id);
        break;
      }
    }
  }
}

void Pipeline::ClearOldDependencies(uint64_t current_cycle) {
  // Clear old instruction dependencies
  std::vector<uint64_t> to_remove;
  for (const auto &[pc, dep] : instruction_dependency_map) {
    // Remove instructions that have completed (written back)
    if (dep.cycle + dep.latency + 10 < current_cycle) {
      to_remove.push_back(pc);
    }
  }

  for (uint64_t pc : to_remove) {
    instruction_dependency_map.erase(pc);
  }

  // Clear old register dependencies
  std::vector<uint32_t> regs_to_remove;
  for (const auto &[reg_id, dep] : register_dependency_map) {
    if (dep.cycle + 20 < current_cycle) {
      regs_to_remove.push_back(reg_id);
    }
  }

  for (uint32_t reg_id : regs_to_remove) {
    register_dependency_map.erase(reg_id);
  }

  spdlog::trace("Pipeline: Cleared old dependencies at cycle {}",
                current_cycle);
}

uint32_t Pipeline::AllocatePhysicalRegister() {
  if (free_physical_registers.empty()) {
    return 0;
  }
  uint32_t reg = free_physical_registers.front();
  free_physical_registers.pop();
  return reg;
}

void Pipeline::ReleasePhysicalRegister(uint32_t physical_id) {
  free_physical_registers.push(physical_id);
}

uint32_t Pipeline::GetPhysicalRegister(uint32_t architectural_id) {
  auto it = architectural_to_physical.find(architectural_id);
  if (it != architectural_to_physical.end()) {
    return it->second;
  }
  return architectural_id;
}

void Pipeline::MapArchitecturalRegister(uint32_t architectural_id,
                                        uint32_t physical_id) {
  architectural_to_physical[architectural_id] = physical_id;
}

void Pipeline::CommitRegisterMapping(uint32_t architectural_id) {
  // In a real implementation, this would commit the speculative mapping
  // For now, just mark the physical register as committed
  auto it = architectural_to_physical.find(architectural_id);
  if (it != architectural_to_physical.end()) {
    uint32_t physical_id = it->second;
    physical_register_file[physical_id].last_write_cycle = stats.cycles;

    spdlog::trace(
        "Pipeline: Committed register mapping for architectural register {}",
        architectural_id);
  }
}

void Pipeline::FlushRegisterMappings() {
  // Reset all mappings to 1:1
  for (uint32_t i = 0; i < 32; i++) {
    architectural_to_physical[i] = i;
  }

  // Reset physical register file
  for (uint32_t i = 0; i < physical_register_file.size(); i++) {
    physical_register_file[i].available =
        (i >= 32); // First 32 are reserved for 1:1 mapping
  }

  // Reset free register queue
  while (!free_physical_registers.empty()) {
    free_physical_registers.pop();
  }

  for (uint32_t i = 32; i < physical_register_file.size(); i++) {
    free_physical_registers.push(i);
  }

  spdlog::debug("Pipeline: Flushed all register mappings");
}

// Pipeline utility functions - definitions
namespace PipelineUtils {

double CalculateIPC(const PipelineStats &stats) {
  if (stats.cycles == 0)
    return 0.0;
  return static_cast<double>(stats.instructionsExecuted) / stats.cycles;
}

double CalculateCacheHitRatio(const PipelineStats &stats) {
  uint64_t total_l1_accesses = stats.cache_l1_hits + stats.cache_l1_misses;
  if (total_l1_accesses == 0)
    return 0.0;
  return static_cast<double>(stats.cache_l1_hits) / total_l1_accesses;
}

double CalculateBranchPredictionAccuracy(const PipelineStats &stats) {
  uint64_t total_branches = stats.branch_hits + stats.branch_mispredictions;
  if (total_branches == 0)
    return 0.0;
  return static_cast<double>(stats.branch_hits) / total_branches;
}

double CalculateJITEfficiency(const PipelineStats &stats) {
  uint64_t total_jit_attempts = stats.jit_executions + stats.jit_fallbacks;
  if (total_jit_attempts == 0)
    return 0.0;
  return static_cast<double>(stats.jit_executions) / total_jit_attempts;
}

bool ValidateConfiguration() {
  // Simplified validation
  return true;
}

void AnalyzePerformance(const std::vector<ProfilingEntry> &data) {
  // Simplified performance analysis
  if (data.empty()) return;
  
  spdlog::info("Performance analysis completed for {} entries", data.size());
}

} // namespace PipelineUtils

// Simplified out-of-order execution methods
void Pipeline::EnableOutOfOrderExecution(bool enable) {
  // Simplified implementation
  spdlog::info("Pipeline: Out-of-order execution {}", enable ? "enabled" : "disabled");
}



void Pipeline::HandleBranchMispredictionROB(uint64_t correct_pc) {
  // Simplified implementation
  spdlog::info("Pipeline: Handled branch misprediction, set PC to 0x{:x}", correct_pc);
}

// Simplified exception handling
void Pipeline::HandlePreciseException(uint64_t pc, uint32_t exception_code) {
  spdlog::error("Pipeline: Handling exception at PC 0x{:x}, code 0x{:x}", pc, exception_code);
  stats.exceptions++;
}

void Pipeline::RecoverFromException() {
  spdlog::info("Pipeline: Recovered from exception, pipeline restarted");
}

void Pipeline::FlushSpeculativeInstructions(uint64_t recovery_pc) {
  spdlog::info("Pipeline: Flushed speculative instructions for recovery at PC 0x{:x}", recovery_pc);
}





























// Pipeline Register Renaming Methods
uint32_t Pipeline::RenameRegister(uint32_t arch_reg) {
  if (!IsOutOfOrderEnabled()) {
    return arch_reg; // No renaming in in-order mode
  }

  uint32_t phys_reg = AllocatePhysicalRegister();
  MapArchitecturalRegister(arch_reg, phys_reg);

  spdlog::trace(
      "Pipeline: Renamed architectural register {} to physical register {}",
      arch_reg, phys_reg);
  return phys_reg;
}

void Pipeline::CommitRegisterRename(uint32_t arch_reg) {
  if (!IsOutOfOrderEnabled()) {
    return;
  }

  CommitRegisterMapping(arch_reg);
  spdlog::trace(
      "Pipeline: Committed register rename for architectural register {}",
      arch_reg);
}

uint32_t Pipeline::CreateRenameCheckpoint() {
  if (!IsOutOfOrderEnabled()) {
    return 0;
  }

  // Simple checkpoint implementation
  static uint32_t next_checkpoint_id = 1;
  uint32_t checkpoint_id = next_checkpoint_id++;
  
  spdlog::trace("Pipeline: Created rename checkpoint {}", checkpoint_id);
  return checkpoint_id;
}

void Pipeline::RestoreRenameCheckpoint(uint32_t checkpoint_id) {
  if (!IsOutOfOrderEnabled()) {
    return;
  }

  // Simple restore implementation
  FlushRegisterMappings();
  spdlog::info("Pipeline: Restored rename checkpoint {}", checkpoint_id);
}

// Simplified register renaming implementation
void Pipeline::MarkRegisterReady(uint32_t reg_id, uint64_t cycle) {
  register_scoreboard[reg_id] = true;

  // Update register dependency
  if (register_dependency_map.count(reg_id) > 0) {
    RegisterDependency &dep = register_dependency_map[reg_id];
    dep.ready = true;
    dep.cycle = cycle;

    spdlog::trace("Pipeline: Register {} marked ready at cycle {}", reg_id, cycle);
  }
}



// Pipeline methods implementation
const PipelineStats& Pipeline::GetStats() const {
  return stats;
}

size_t Pipeline::GetFetchStageOccupancy() const {
  return fetchStage.size();
}

size_t Pipeline::GetDecodeStageOccupancy() const {
  return decodeStage.size();
}

size_t Pipeline::GetExecuteStageOccupancy() const {
  return executeStage.size();
}

size_t Pipeline::GetMemoryStageOccupancy() const {
  return memoryStage.size();
}

size_t Pipeline::GetWriteBackStageOccupancy() const {
  return writeBackStage.size();
}

// Simplified conflict detection
bool Pipeline::Conflicts(const DecodedInstruction& instr1, const DecodedInstruction& instr2) const {
    // Check if two instructions conflict (register dependencies)
    for (uint8_t i = 0; i < instr1.operandCount; ++i) {
        for (uint8_t j = 0; j < instr2.operandCount; ++j) {
            const auto& op1 = instr1.operands[i];
            const auto& op2 = instr2.operands[j];
            
            if ((op1.type == DecodedInstruction::Operand::Type::REGISTER ||
                 op1.type == DecodedInstruction::Operand::Type::XMM) &&
                (op2.type == DecodedInstruction::Operand::Type::REGISTER ||
                 op2.type == DecodedInstruction::Operand::Type::XMM) &&
                op1.reg == op2.reg) {
                return true;
            }
        }
    }
    return false;
}



namespace {
  constexpr uint32_t NUM_ARCH_REGISTERS = 32;
  constexpr uint32_t NUM_PHYS_REGISTERS = 64;
  constexpr uint32_t ROB_SIZE = 128;
}

// Constructor implementations
HazardInfo::HazardInfo() : type(HazardType::None), blocking_pc(0), blocking_stage(0), cycles_remaining(0) {}

PipelineException::PipelineException(const std::string& message) : std::runtime_error(message) {}

// Missing method implementations
bool Pipeline::IsOutOfOrderEnabled() const {
  return optimizationHints.enable_out_of_order;
}

void Pipeline::InitializeReservationStations() {
  // Initialize reservation stations for out-of-order execution
  spdlog::debug("Pipeline: Initialized reservation stations");
}

const DecodedInstruction::Operand* Pipeline::SafeGetOperand(const DecodedInstruction& instr, int operand_index) const {
  if (operand_index < 0 || operand_index >= instr.operandCount || operand_index >= 4) {
    return nullptr;
  }
  return &instr.operands[operand_index];
}

} // namespace x86_64