// Copyright 2025 <Copyright Owner>

// Additional bounds checking improvements for CPU diagnostics

#include "cpu_diagnostics.h"
#include "../debug/vector_debug.h"
#include "../common/lock_ordering.h"
#include "decoded_instruction.h" // Add for DecodedInstruction

#include <algorithm>
#include <chrono>
#include <mutex>
#include <string>
#include <unordered_map>

#include <spdlog/spdlog.h>

#include "emulator/apic.h"
#include "ps4/fiber_manager.h"
#include "ps4/ps4_emulator.h"

namespace x86_64 {

void CPUDiagnostics::RecordInstruction(const DecodedInstruction& instruction) {
    MEMORY_LOCK(m_mutex, "CPUDiagnosticsMutex");
    
    // Validate instruction data before recording
    if (instruction.opcode >= MAX_OPCODES) {
        spdlog::error("CPUDiagnostics::RecordInstruction: Invalid opcode {}", instruction.opcode);
        return;
    }
    
    // Ensure instruction history doesn't exceed bounds
    if (m_instruction_history.size() >= MAX_HISTORY_SIZE) {
        m_instruction_history.erase(m_instruction_history.begin());
    }
    
    // Safe insertion with bounds checking
    try {
        m_instruction_history.push_back(instruction);
        
        // Update opcode statistics with bounds checking
        if (instruction.opcode < m_opcode_stats.size()) {
            auto& stats = CRITICAL_VECTOR_ACCESS(m_opcode_stats, instruction.opcode, 
                                               "CPUDiagnostics::RecordInstruction opcode stats");
            stats.count++;
            stats.total_cycles += instruction.estimated_cycles;
        }
        
    } catch (const std::exception& e) {
        spdlog::error("CPUDiagnostics::RecordInstruction: Failed to record instruction: {}", e.what());
    }
}

/**
 * @brief Constructs a CPUDiagnostics singleton instance.
 */
CPUDiagnostics::CPUDiagnostics() {
  try {
    // Initialize opcode statistics vector
    m_opcode_stats.resize(MAX_OPCODES);

    ResetMetrics();
    spdlog::info("CPUDiagnostics initialized");
  } catch (const std::exception &e) {
    spdlog::error("CPUDiagnostics initialization failed: {}", e.what());
    // Initialize with empty metrics to prevent crashes
    m_metrics.clear();
    m_opcode_stats.resize(MAX_OPCODES);
  }
}

/**
 * @brief Retrieves the singleton instance of CPUDiagnostics.
 * @return Reference to the singleton instance.
 */
CPUDiagnostics &CPUDiagnostics::GetInstance() {
  static CPUDiagnostics instance;
  return instance;
}

/**
 * @brief Updates CPU-related diagnostic metrics.
 * @details Collects metrics from all CPUs, pipelines, APIC, fibers, and caches.
 */
void CPUDiagnostics::UpdateMetrics(DiagnosticLevel level) {
  MEMORY_LOCK(m_mutex, "CPUDiagnosticsMutex");
  try {
    // Note: This method should be called by the emulator with data pushed to it
    // rather than pulling data from the emulator to break circular dependency
    spdlog::trace("CPU diagnostics: UpdateMetrics called but emulator data not "
                  "available");
    return;
  } catch (const std::exception &e) {
    spdlog::warn("CPUDiagnostics::UpdateMetrics failed: {}", e.what());
  }
}

/**
 * @brief Updates CPU-related diagnostic metrics with provided emulator data.
 * @details Collects metrics from provided CPU data to break circular
 * dependency.
 */
void CPUDiagnostics::UpdateMetricsWithData(
    const std::vector<x86_64::X86_64CPU *> &cpus,
    const ps4::FiberManager &fiberManager, DiagnosticLevel level) {
  MEMORY_LOCK(m_mutex, "CPUDiagnosticsMutex");
  try {
    size_t cpu_count = cpus.size();
    if (cpu_count == 0) {
      spdlog::trace("CPU diagnostics: no CPUs available yet, skipping update");
      return;
    }

    uint64_t total_cycles = 0, total_instructions = 0, total_utilization = 0;
    uint64_t total_stalls = 0, total_data_hazards = 0, total_memory_stalls = 0;
    uint64_t total_branch_hits = 0, total_branch_mispredictions = 0;
    uint64_t total_apic_ops = 0, total_apic_latency = 0,
             total_simd_instructions = 0;
    uint64_t cache_hits = 0, cache_misses = 0;

    for (size_t i = 0; i < cpu_count; ++i) {
      auto &cpu = *cpus[i];
      auto s = cpu.GetPipeline().GetStats();
      total_cycles += s.cycles;
      total_instructions += s.instructionsExecuted;
      total_utilization += static_cast<uint64_t>(cpu.GetUtilization() * 100);
      total_stalls += s.stalls;
      total_data_hazards += s.data_hazard_stalls;
      total_memory_stalls += s.memory_stalls;
      total_branch_hits += s.branch_hits;
      total_branch_mispredictions += s.branch_mispredictions;
      auto ap = cpu.GetAPIC().GetStats();
      total_apic_ops += ap.operationCount;
      total_apic_latency += ap.totalLatencyUs;

      // SIMD instruction counts from pipeline stats
      auto pipeline_stats = cpu.GetPipeline().GetStats();
      total_simd_instructions += pipeline_stats.simd_instructions;

      // Cache stats (assuming L1 cache access)
      auto cache_stats = cpu.GetMemory().GetStats();
      cache_hits += cache_stats.hits.load();
      cache_misses += cache_stats.misses.load();
    }

    m_metrics["cpu_cycles"] = total_cycles / cpu_count;
    m_metrics["cpu_instructions"] = total_instructions / cpu_count;
    m_metrics["cpu_utilization"] = total_utilization / cpu_count;
    m_metrics["pipeline_stalls"] = total_stalls / cpu_count;
    m_metrics["data_hazard_stalls"] = total_data_hazards / cpu_count;
    m_metrics["memory_stalls"] = total_memory_stalls / cpu_count;
    m_metrics["branch_hits"] = total_branch_hits / cpu_count;
    m_metrics["branch_mispredictions"] =
        total_branch_mispredictions / cpu_count;
    m_metrics["branch_misprediction_rate"] =
        (total_branch_hits + total_branch_mispredictions) > 0
            ? (total_branch_mispredictions * 100) /
                  (total_branch_hits + total_branch_mispredictions)
            : 0;
    m_metrics["apic_operations"] = total_apic_ops / cpu_count;
    m_metrics["apic_latency_us"] = total_apic_latency / cpu_count;
    m_metrics["apic_interrupts"] = total_apic_ops / cpu_count;
    m_metrics["fiber_count"] = fiberManager.GetFiberCount();
    m_metrics["simd_instructions"] = total_simd_instructions / cpu_count;
    m_metrics["cache_hit_ratio"] =
        (cache_hits + cache_misses) > 0
            ? (cache_hits * 100) / (cache_hits + cache_misses)
            : 0;

    spdlog::trace(
        "CPU diagnostics updated: cycles={}, instr={}, stalls={}, apic_ops={}",
        m_metrics.at("cpu_cycles"), m_metrics.at("cpu_instructions"),
        m_metrics.at("pipeline_stalls"), m_metrics.at("apic_operations"));
  } catch (const std::exception &e) {
    spdlog::warn("CPUDiagnostics::UpdateMetricsWithData failed: {}", e.what());
  }
}

/**
 * @brief Resets all diagnostic metrics to default values.
 */
void CPUDiagnostics::ResetMetrics() {
  MEMORY_LOCK(m_mutex, "CPUDiagnosticsMutex");
  m_metrics.clear();
  m_performance_metrics.clear();
  spdlog::info("CPU diagnostics reset");
}

/**
 * @brief Get performance summary
 */
std::string CPUDiagnostics::GetPerformanceSummary() const {
  MEMORY_SHARED_LOCK(m_mutex, "CPUDiagnosticsMutex");
  std::string summary = "CPU Performance Summary:\n";
  for (const auto &[key, value] : m_metrics) {
    summary += "  " + key + ": " + std::to_string(value) + "\n";
  }
  return summary;
}

/**
 * @brief Export metrics to JSON format
 */
std::string CPUDiagnostics::ExportToJSON() const {
  MEMORY_SHARED_LOCK(m_mutex, "CPUDiagnosticsMutex");
  std::string json = "{\n";
  bool first = true;
  for (const auto &[key, value] : m_metrics) {
    if (!first)
      json += ",\n";
    json += "  \"" + key + "\": " + std::to_string(value);
    first = false;
  }
  json += "\n}";
  return json;
}

/**
 * @brief Set update interval for automatic metrics collection
 */
void CPUDiagnostics::SetUpdateInterval(uint32_t intervalMs) {
  MEMORY_LOCK(m_mutex, "CPUDiagnosticsMutex");
  m_update_interval_ms = intervalMs;
}

/**
 * @brief Collect basic metrics
 */
void CPUDiagnostics::CollectBasicMetrics() {
  MEMORY_LOCK(m_mutex, "CPUDiagnosticsMutex");
  try {
    // Note: This method should be called by the emulator with data pushed to it
    // rather than pulling data from the emulator to break circular dependency
    spdlog::trace("CPU diagnostics: CollectBasicMetrics called but emulator "
                  "data not available");
    return;
  } catch (const std::exception &e) {
    spdlog::warn("CPUDiagnostics::CollectBasicMetrics failed: {}", e.what());
  }
}

/**
 * @brief Collect detailed metrics
 */
void CPUDiagnostics::CollectDetailedMetrics() {
  MEMORY_LOCK(m_mutex, "CPUDiagnosticsMutex");
  try {
    // Note: This method should be called by the emulator with data pushed to it
    // rather than pulling data from the emulator to break circular dependency
    spdlog::trace("CPU diagnostics: CollectDetailedMetrics called but emulator "
                  "data not available");
    return;

  } catch (const std::exception &e) {
    spdlog::warn("CPUDiagnostics::CollectDetailedMetrics failed: {}", e.what());
  }
}

/**
 * @brief Collect verbose metrics
 */
void CPUDiagnostics::CollectVerboseMetrics() {
  MEMORY_LOCK(m_mutex, "CPUDiagnosticsMutex");
  try {
    // First collect all detailed metrics
    CollectDetailedMetrics();

    // Get CPU instances from PS4Emulator
    auto &emulator = ps4::PS4Emulator::GetInstance();
    std::vector<x86_64::X86_64CPU *> cpus;
    for (uint32_t i = 0; i < emulator.GetCPUCount(); i++) {
      cpus.push_back(&emulator.GetCPU(i));
    }

    if (cpus.empty()) {
      spdlog::warn("No CPUs available for verbose metrics collection");
      return;
    }

    // Collect additional verbose metrics
    CollectInstructionTraces(cpus);
    CollectPipelineDetailedStats(cpus);
    CollectRegisterDependencyStats(cpus);
    CollectCacheDetailedStats(cpus);
    CollectBranchPredictionStats(cpus);
    CollectMemorySubsystemStats(cpus);
    CollectPerformanceCounters(cpus);

    spdlog::debug("CPU verbose metrics collected successfully");
  } catch (const std::exception &e) {
    spdlog::warn("CPUDiagnostics::CollectVerboseMetrics failed: {}", e.what());
  }
}

/**
 * @brief Collect instruction traces from all CPUs
 */
void CPUDiagnostics::CollectInstructionTraces(
    const std::vector<x86_64::X86_64CPU *> &cpus) {
  try {
    // Clear old traces if we've reached the limit
    if (m_instruction_traces.size() >= m_max_trace_entries) {
      m_instruction_traces.clear();
    }

    for (auto cpu : cpus) {
      // Get the most recently executed instructions from each CPU
      auto &pipeline = cpu->GetPipeline();

      // Get the current instruction in each pipeline stage
      size_t writeBackOccupancy = pipeline.GetWriteBackStageOccupancy();
      if (writeBackOccupancy > 0) {
        InstructionTrace trace;
        trace.pc = pipeline.GetStats().instructionsExecuted;
        trace.instruction_type = static_cast<uint32_t>(
            InstructionType::Unknown); // Would need access to actual
                                       // instruction
        trace.cycle = pipeline.GetStats().cycles;
        trace.stage = 5;             // Writeback stage
        trace.operands = "N/A";      // Would need access to actual operands
        trace.execution_time_ns = 0; // Would need timing info

        m_instruction_traces.push_back(trace);
      }
    }

    // Add trace count to metrics
    m_metrics["instruction_trace_count"] = m_instruction_traces.size();

    spdlog::trace("Collected {} instruction traces",
                  m_instruction_traces.size());
  } catch (const std::exception &e) {
    spdlog::warn("CollectInstructionTraces failed: {}", e.what());
  }
}

/**
 * @brief Collect detailed pipeline statistics
 */
void CPUDiagnostics::CollectPipelineDetailedStats(
    const std::vector<x86_64::X86_64CPU *> &cpus) {
  try {
    // Reset pipeline stage stats
    m_pipeline_stage_stats = {};

    for (auto cpu : cpus) {
      auto &pipeline = cpu->GetPipeline();
      auto stats = pipeline.GetStats();

      // Accumulate stall counts
      m_pipeline_stage_stats.fetch_stalls +=
          stats.stalls; // Would need per-stage stall counts
      m_pipeline_stage_stats.decode_stalls += stats.stalls;
      m_pipeline_stage_stats.execute_stalls += stats.data_hazard_stalls;
      m_pipeline_stage_stats.memory_stalls += stats.memory_stalls;
      m_pipeline_stage_stats.writeback_stalls += stats.stalls;

      // Calculate utilization (would need more detailed stats from pipeline)
      m_pipeline_stage_stats.fetch_utilization =
          pipeline.GetFetchStageOccupancy() / 8.0;
      m_pipeline_stage_stats.decode_utilization =
          pipeline.GetDecodeStageOccupancy() / 8.0;
      m_pipeline_stage_stats.execute_utilization =
          pipeline.GetExecuteStageOccupancy() / 4.0;
      m_pipeline_stage_stats.memory_utilization =
          pipeline.GetMemoryStageOccupancy() / 4.0;
      m_pipeline_stage_stats.writeback_utilization =
          pipeline.GetWriteBackStageOccupancy() / 4.0;
    }

    // Add pipeline stats to metrics
    m_metrics["pipeline_fetch_stalls"] = m_pipeline_stage_stats.fetch_stalls;
    m_metrics["pipeline_decode_stalls"] = m_pipeline_stage_stats.decode_stalls;
    m_metrics["pipeline_execute_stalls"] =
        m_pipeline_stage_stats.execute_stalls;
    m_metrics["pipeline_memory_stalls"] = m_pipeline_stage_stats.memory_stalls;
    m_metrics["pipeline_writeback_stalls"] =
        m_pipeline_stage_stats.writeback_stalls;

    m_performance_metrics["pipeline_fetch_utilization"] =
        m_pipeline_stage_stats.fetch_utilization;
    m_performance_metrics["pipeline_decode_utilization"] =
        m_pipeline_stage_stats.decode_utilization;
    m_performance_metrics["pipeline_execute_utilization"] =
        m_pipeline_stage_stats.execute_utilization;
    m_performance_metrics["pipeline_memory_utilization"] =
        m_pipeline_stage_stats.memory_utilization;
    m_performance_metrics["pipeline_writeback_utilization"] =
        m_pipeline_stage_stats.writeback_utilization;

    spdlog::trace("Collected detailed pipeline statistics");
  } catch (const std::exception &e) {
    spdlog::warn("CollectPipelineDetailedStats failed: {}", e.what());
  }
}

/**
 * @brief Collect register dependency statistics
 */
void CPUDiagnostics::CollectRegisterDependencyStats(
    const std::vector<x86_64::X86_64CPU *> &cpus) {
  try {
    uint64_t total_register_stalls = 0;
    uint64_t total_dependency_chains = 0;
    uint64_t total_register_renames = 0;

    for (auto cpu : cpus) {
      auto &pipeline = cpu->GetPipeline();
      auto stats = pipeline.GetStats();

      // Accumulate register-related stalls
      total_register_stalls += stats.data_hazard_stalls;

      // Would need access to pipeline internals for more detailed stats
      // For now, estimate based on available data
      total_dependency_chains += stats.data_hazard_stalls / 2; // Rough estimate
      total_register_renames +=
          stats.instructionsExecuted / 10; // Rough estimate
    }

    m_metrics["register_dependency_stalls"] = total_register_stalls;
    m_metrics["register_dependency_chains"] = total_dependency_chains;
    m_metrics["register_renames"] = total_register_renames;

    spdlog::trace("Collected register dependency statistics");
  } catch (const std::exception &e) {
    spdlog::warn("CollectRegisterDependencyStats failed: {}", e.what());
  }
}

/**
 * @brief Collect detailed cache statistics
 */
void CPUDiagnostics::CollectCacheDetailedStats(
    const std::vector<x86_64::X86_64CPU *> &cpus) {
  try {
    uint64_t total_l1_hits = 0, total_l1_misses = 0;
    uint64_t total_l2_hits = 0, total_l2_misses = 0;
    uint64_t total_l3_hits = 0, total_l3_misses = 0;
    uint64_t total_prefetch_hits = 0, total_prefetch_misses = 0;

    for (auto cpu : cpus) {
      auto &pipeline = cpu->GetPipeline();
      auto stats = pipeline.GetStats();

      total_l1_hits += stats.cache_l1_hits;
      total_l1_misses += stats.cache_l1_misses;
      total_l2_hits += stats.cache_l2_hits;
      total_l2_misses += stats.cache_l2_misses;
      total_prefetch_hits += stats.prefetch_hits;
      total_prefetch_misses += stats.prefetch_misses;

      // L3 cache would be shared, so only count once
      if (cpu == cpus[0]) {
        // Would need access to shared L3 cache stats
        total_l3_hits = 0;   // Placeholder
        total_l3_misses = 0; // Placeholder
      }
    }

    m_metrics["cache_l1_hits"] = total_l1_hits;
    m_metrics["cache_l1_misses"] = total_l1_misses;
    m_metrics["cache_l2_hits"] = total_l2_hits;
    m_metrics["cache_l2_misses"] = total_l2_misses;
    m_metrics["cache_l3_hits"] = total_l3_hits;
    m_metrics["cache_l3_misses"] = total_l3_misses;
    m_metrics["prefetch_hits"] = total_prefetch_hits;
    m_metrics["prefetch_misses"] = total_prefetch_misses;

    // Calculate hit ratios
    if (total_l1_hits + total_l1_misses > 0) {
      m_performance_metrics["cache_l1_hit_ratio"] =
          static_cast<double>(total_l1_hits) /
          (total_l1_hits + total_l1_misses);
    }
    if (total_l2_hits + total_l2_misses > 0) {
      m_performance_metrics["cache_l2_hit_ratio"] =
          static_cast<double>(total_l2_hits) /
          (total_l2_hits + total_l2_misses);
    }
    if (total_prefetch_hits + total_prefetch_misses > 0) {
      m_performance_metrics["prefetch_accuracy"] =
          static_cast<double>(total_prefetch_hits) /
          (total_prefetch_hits + total_prefetch_misses);
    }

    spdlog::trace("Collected detailed cache statistics");
  } catch (const std::exception &e) {
    spdlog::warn("CollectCacheDetailedStats failed: {}", e.what());
  }
}

/**
 * @brief Collect branch prediction statistics
 */
void CPUDiagnostics::CollectBranchPredictionStats(
    const std::vector<x86_64::X86_64CPU *> &cpus) {
  try {
    uint64_t total_branch_predictions = 0;
    uint64_t total_branch_hits = 0;
    uint64_t total_branch_misses = 0;
    uint64_t total_indirect_branches = 0;
    uint64_t total_return_predictions = 0;

    for (auto cpu : cpus) {
      auto &pipeline = cpu->GetPipeline();
      auto stats = pipeline.GetStats();

      total_branch_hits += stats.branch_hits;
      total_branch_misses += stats.branch_mispredictions;
      total_branch_predictions = total_branch_hits + total_branch_misses;

      // Would need access to branch predictor internals for more detailed stats
      total_indirect_branches += stats.branch_hits / 10; // Rough estimate
      total_return_predictions += stats.branch_hits / 5; // Rough estimate
    }

    m_metrics["branch_predictions_total"] = total_branch_predictions;
    m_metrics["branch_predictions_correct"] = total_branch_hits;
    m_metrics["branch_predictions_incorrect"] = total_branch_misses;
    m_metrics["indirect_branch_predictions"] = total_indirect_branches;
    m_metrics["return_address_predictions"] = total_return_predictions;

    // Calculate accuracy
    if (total_branch_predictions > 0) {
      m_performance_metrics["branch_prediction_accuracy"] =
          static_cast<double>(total_branch_hits) / total_branch_predictions;
    }

    spdlog::trace("Collected branch prediction statistics");
  } catch (const std::exception &e) {
    spdlog::warn("CollectBranchPredictionStats failed: {}", e.what());
  }
}

/**
 * @brief Collect memory subsystem statistics
 */
void CPUDiagnostics::CollectMemorySubsystemStats(
    const std::vector<x86_64::X86_64CPU *> &cpus) {
  try {
    uint64_t total_memory_accesses = 0;
    uint64_t total_tlb_hits = 0;
    uint64_t total_tlb_misses = 0;
    uint64_t total_page_faults = 0;
    uint64_t total_memory_bandwidth = 0;

    for (auto cpu : cpus) {
      auto &pipeline = cpu->GetPipeline();
      auto stats = pipeline.GetStats();

      total_tlb_hits += stats.tlb_hits;
      total_tlb_misses += stats.tlb_misses;
      total_page_faults += stats.memory_protection_faults;
      total_memory_accesses = total_tlb_hits + total_tlb_misses;

      // Estimate memory bandwidth (would need actual measurement)
      total_memory_bandwidth +=
          stats.instructionsExecuted * 64; // Rough estimate in bytes
    }

    m_metrics["memory_accesses_total"] = total_memory_accesses;
    m_metrics["tlb_hits"] = total_tlb_hits;
    m_metrics["tlb_misses"] = total_tlb_misses;
    m_metrics["page_faults"] = total_page_faults;
    m_metrics["memory_bandwidth_bytes"] = total_memory_bandwidth;

    // Calculate TLB hit ratio
    if (total_memory_accesses > 0) {
      m_performance_metrics["tlb_hit_ratio"] =
          static_cast<double>(total_tlb_hits) / total_memory_accesses;
    }

    spdlog::trace("Collected memory subsystem statistics");
  } catch (const std::exception &e) {
    spdlog::warn("CollectMemorySubsystemStats failed: {}", e.what());
  }
}

/**
 * @brief Collect performance counters
 */
void CPUDiagnostics::CollectPerformanceCounters(
    const std::vector<x86_64::X86_64CPU *> &cpus) {
  try {
    uint64_t total_atomic_ops = 0;
    uint64_t total_lock_contentions = 0;
    uint64_t total_thread_switches = 0;
    uint64_t total_jit_executions = 0;
    uint64_t total_jit_cache_hits = 0;
    uint64_t total_jit_cache_misses = 0;

    for (auto cpu : cpus) {
      auto &pipeline = cpu->GetPipeline();
      auto stats = pipeline.GetStats();

      total_atomic_ops += stats.atomic_operations;
      total_lock_contentions += stats.lock_contentions;
      total_thread_switches += stats.thread_switches;
      total_jit_executions += stats.jit_executions;
      total_jit_cache_hits += stats.jit_cache_hits;
      total_jit_cache_misses += stats.jit_cache_misses;
    }

    m_metrics["atomic_operations"] = total_atomic_ops;
    m_metrics["lock_contentions"] = total_lock_contentions;
    m_metrics["thread_switches"] = total_thread_switches;
    m_metrics["jit_executions"] = total_jit_executions;
    m_metrics["jit_cache_hits"] = total_jit_cache_hits;
    m_metrics["jit_cache_misses"] = total_jit_cache_misses;

    // Calculate JIT efficiency
    if (total_jit_cache_hits + total_jit_cache_misses > 0) {
      m_performance_metrics["jit_cache_hit_ratio"] =
          static_cast<double>(total_jit_cache_hits) /
          (total_jit_cache_hits + total_jit_cache_misses);
    }

    spdlog::trace("Collected performance counters");
  } catch (const std::exception &e) {
    spdlog::warn("CollectPerformanceCounters failed: {}", e.what());
  }
}

std::vector<DecodedInstruction> CPUDiagnostics::GetRecentInstructions(size_t count) const {
    MEMORY_SHARED_LOCK(m_mutex, "CPUDiagnosticsMutex");
    
    VALIDATE_VECTOR(m_instruction_history, "CPUDiagnostics::GetRecentInstructions");
    
    if (count == 0 || m_instruction_history.empty()) {
        return {};
    }
    
    size_t start_index = 0;
    if (count < m_instruction_history.size()) {
        start_index = m_instruction_history.size() - count;
    }
    
    std::vector<DecodedInstruction> result;
    result.reserve(count);
    
    // Safe iteration with bounds checking
    for (size_t i = start_index; i < m_instruction_history.size(); ++i) {
        const auto& instruction = SAFE_VECTOR_ACCESS(m_instruction_history, i, 
                                                   "CPUDiagnostics::GetRecentInstructions");
        result.push_back(instruction);
    }
    
    return result;
}

/**
 * @brief Retrieves the current diagnostic metrics.
 * @return Map of metric names to values.
 */
std::unordered_map<std::string, uint64_t>
CPUDiagnostics::GetMetrics(DiagnosticLevel level) const {
  MEMORY_SHARED_LOCK(m_mutex, "CPUDiagnosticsMutex");
  return m_metrics;
}

/**
 * @brief Logs detailed diagnostic information.
 */
void CPUDiagnostics::LogDiagnostics(DiagnosticLevel level) {
  MEMORY_SHARED_LOCK(m_mutex, "CPUDiagnosticsMutex");
  spdlog::info("CPU Diagnostics Report:");
  for (const auto &[key, value] : m_metrics) {
    spdlog::info("  {}: {}", key, value);
  }
}
} // namespace x86_64