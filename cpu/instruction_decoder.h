#ifndef CPU_INSTRUCTION_DECODER_H
#define CPU_INSTRUCTION_DECODER_H

#include "decoded_instruction.h"
#include <array>
#include <cstdint>
#include <string>
#include <unordered_map>
#include <vector>

// Include Zydis headers
#include <Zydis/Zydis.h>

namespace x86_64 {

// Error codes for the decoder.
enum class DecoderError {
  Success,
  BufferOverflow,
  IncompleteInstruction,
  InvalidInstruction,
  UnknownOpcode
};

// Structure to hold detailed error information.
struct DecoderErrorInfo {
  DecoderError error;
  std::string message;
};

// Main class for decoding x86-64 instructions.
class InstructionDecoder {
public:
  // Constructor: Initializes the Zydis decoder.
  InstructionDecoder();

  // Decodes a single instruction from the provided buffer.
  // @param addr The memory address of the instruction.
  // @param buffer Pointer to the byte stream.
  // @param bufferSize The size of the buffer.
  // @param instr The DecodedInstruction struct to be filled.
  // @return DecoderErrorInfo containing success or failure info.
  DecoderErrorInfo Decode(uint64_t addr, const uint8_t *buffer,
                          size_t bufferSize, DecodedInstruction &instr);

  // Retrieves the statistics of decoded instructions.
  const std::unordered_map<InstructionType, uint64_t> &GetStats() const;

  // Resets the decoding statistics.
  void ResetStats();

private:
  // Zydis backend objects
  ZydisDecoder decoder;
  ZydisFormatter formatter;

  // Decoding statistics
  std::unordered_map<InstructionType, uint64_t> stats;

  // Helper methods for Zydis integration
  InstructionType MapZydisToInstructionType(ZydisMnemonic mnemonic) const;
  ConditionCode MapZydisToConditionCode(ZydisBranchType branchType) const;
  Register MapZydisToRegister(ZydisRegister zydisReg) const;
  void ConvertZydisOperand(const ZydisDecodedOperand &zydisOp,
                           DecodedInstruction::Operand &ourOp) const;
};

} // namespace x86_64

#endif // CPU_INSTRUCTION_DECODER_H
