#pragma once

#include <cstdint>
#include <vector>
#include <memory>
#include <stdexcept>
#include <string>
#include <unordered_map>
#include <queue>
#include <atomic>
#include <chrono>
#include <functional>
#include <mutex>
#include "decoded_instruction.h"
#include "instruction_decoder.h"
#include "register.h"

// Forward declarations
namespace jit {
    enum class Status;
    class JitCache;
    class JitTranslator;
}

namespace ps4 {
    class PS4MMU;
}

namespace x86_64 {

class X86_64CPU;
class X86_64JITCompiler;

enum class ExecutionUnitType {
    ALU,
    FPU,
    SIMD,
    LOAD_STORE,
    BRANCH,
    DIVIDE,
    INTEGER = ALU,  // Alias for backward compatibility
    FLOATING_POINT = FPU,
    VECTOR = SIMD
};

// Execution unit structure
struct ExecutionUnit {
    ExecutionUnitType type;
    bool busy;
    uint64_t available_cycle;
    uint64_t instruction_pc;

    ExecutionUnit(ExecutionUnitType t) : type(t), busy(false), available_cycle(0), instruction_pc(0) {}
};

// Basic pipeline stage structure
struct PipelineStage {
    DecodedInstruction instruction;
    uint64_t pc;
    int cycle;
};

// Detailed pipeline stage structures
struct FetchStage {
    DecodedInstruction instr;
    uint64_t pc;
    bool valid;
    bool predicted_taken;
    uint64_t predicted_target;
    uint64_t fetch_cycle;
    float prediction_confidence;
};

struct DecodeStage {
    DecodedInstruction instr;
    uint64_t pc;
    bool valid;
    bool predicted_taken;
    uint64_t predicted_target;
    uint64_t decode_cycle;
    uint64_t fetch_cycle;
    float prediction_confidence;
};

struct ExecuteStage {
    DecodedInstruction instr;
    uint64_t pc;
    bool valid;
    bool predicted_taken;
    uint64_t predicted_target;
    uint64_t execute_cycle;
    uint64_t fetch_cycle;
    float prediction_confidence;
    ExecutionUnitType unit_type;
    int execution_latency;
};

struct MemoryStage {
    DecodedInstruction instr;
    uint64_t pc;
    bool valid;
    uint64_t memory_cycle;
    uint64_t fetch_cycle;
};

struct WriteBackStage {
    DecodedInstruction instr;
    uint64_t pc;
    bool valid;
    uint64_t writeback_cycle;
    uint64_t fetch_cycle;
};

// Pipeline exception classes
class PipelineException : public std::runtime_error {
public:
    explicit PipelineException(const std::string& message);
};

class PipelineExecuteException : public PipelineException {
public:
    PipelineExecuteException(const std::string& message, uint64_t pc)
        : PipelineException(message), pc_(pc) {}
    uint64_t GetPC() const { return pc_; }
private:
    uint64_t pc_;
};

class PipelineFetchException : public PipelineException {
public:
    PipelineFetchException(const std::string& message, uint64_t pc)
        : PipelineException(message), pc_(pc) {}
    uint64_t GetPC() const { return pc_; }
private:
    uint64_t pc_;
};

// Branch predictor constants
constexpr size_t LOCAL_HISTORY_TABLE_SIZE = 1024;
constexpr size_t LOCAL_PATTERN_TABLE_SIZE = 1024;
constexpr size_t PATTERN_TABLE_SIZE = 4096;
constexpr size_t CHOICE_TABLE_SIZE = 4096;
constexpr size_t BTB_SIZE = 512;
constexpr size_t RAS_SIZE = 32;
constexpr size_t LOCAL_HISTORY_BITS = 10;
constexpr size_t GLOBAL_HISTORY_BITS = 12;

// BTB entry structure
struct BTBEntry {
    uint64_t tag;
    uint64_t target;
    bool valid;
    uint8_t confidence;

    BTBEntry() : tag(0), target(0), valid(false), confidence(0) {}
};

// Branch predictor detailed statistics
struct BranchPredictorStats {
    uint64_t local_correct;
    uint64_t local_incorrect;
    uint64_t global_correct;
    uint64_t global_incorrect;
    uint64_t btb_hits;
    uint64_t btb_misses;
    uint64_t ras_hits;
    uint64_t ras_misses;
    uint64_t choice_local_selected;
    uint64_t choice_global_selected;
    uint64_t target_correct;
    uint64_t target_incorrect;

    BranchPredictorStats() : local_correct(0), local_incorrect(0), global_correct(0),
                           global_incorrect(0), btb_hits(0), btb_misses(0),
                           ras_hits(0), ras_misses(0), choice_local_selected(0),
                           choice_global_selected(0), target_correct(0), target_incorrect(0) {}
};

// Branch predictor class
class BranchPredictor {
private:
    std::vector<uint16_t> m_local_history_table;
    std::vector<uint8_t> m_local_pattern_table;
    std::vector<uint8_t> m_global_pattern_table;
    std::vector<uint8_t> m_choice_table;
    uint16_t m_global_history;
    std::vector<BTBEntry> m_btb;
    std::vector<uint64_t> m_ras;
    size_t m_ras_top;
    uint64_t m_hits;
    uint64_t m_misses;
    BranchPredictorStats m_detailed_stats;

public:
    BranchPredictor();

    bool PredictTaken(uint64_t pc);
    void Update(uint64_t pc, bool taken);
    uint64_t PredictTarget(uint64_t pc, const DecodedInstruction& instr, uint64_t fallthrough);
    void UpdateTarget(uint64_t pc, uint64_t target);
    void PushReturnAddress(uint64_t return_addr);
    uint64_t PopReturnAddress();
    void ResetStats();
    double GetAccuracy() const;
    void RecordPrediction(bool predicted, bool actual);

    // Helper methods
    size_t GetChoiceIndex(uint64_t pc) const;
    size_t GetLocalPatternIndex(uint64_t pc) const;
    size_t GetGlobalPatternIndex() const;
    size_t GetLocalHistoryIndex(uint64_t pc) const;
    size_t GetBTBIndex(uint64_t pc) const;
    void UpdateCounter(uint8_t& counter, bool taken, uint8_t max_val = 3);
    bool IsIndirectBranch(const DecodedInstruction& instr) const;
};

// Forward declarations for Pipeline dependencies
class InstructionDecoder;
class X86_64JITCompiler;
class ReorderBuffer;

// Hazard detection structures
enum class HazardType {
    None,
    DataHazard,
    StructuralHazard,
    ControlHazard,
    RAW,
    WAW,
    WAR,
    Structural
};

struct HazardInfo {
    HazardType type;
    std::string description;
    uint64_t blocking_pc;
    uint64_t cycles_remaining;
    int blocking_stage;

    HazardInfo();
};

// Profiling structures
struct ProfilingEntry {
    uint64_t cycle;
    uint64_t instructions;
    uint64_t stalls;
    std::chrono::steady_clock::time_point timestamp;
};

// Register dependency tracking
struct PhysicalRegister {
    uint32_t physical_id;
    bool available;
    uint64_t last_write_cycle;

    PhysicalRegister() : physical_id(0), available(true), last_write_cycle(0) {}
};

struct RegisterDependency {
    uint32_t reg_id;
    uint64_t pc;
    uint64_t cycle;
    bool ready;
    
    RegisterDependency() : reg_id(0), pc(0), cycle(0), ready(false) {}
};

struct InstructionDependency {
    uint64_t pc;
    uint64_t cycle;
    std::vector<uint32_t> reads;
    std::vector<uint32_t> writes;
    bool ready;
    int latency;
    
    InstructionDependency() : pc(0), cycle(0), ready(false), latency(1) {}
};

// Constants for pipeline
namespace PipelineConstants {
    constexpr uint32_t NUM_ARCH_REGISTERS = 32;
    constexpr uint32_t NUM_PHYS_REGISTERS = 64;
    constexpr uint32_t ROB_SIZE = 128;
}

// ROB Entry structure
struct ROBEntry {
    uint64_t pc;
    DecodedInstruction instr;
    bool valid;
    bool completed;
    bool exception;
    uint32_t exception_code_value;
    uint64_t issue_cycle;
    uint64_t complete_cycle;
    uint32_t dest_register;
    uint64_t dest_value;
    bool dest_valid;
    uint32_t rob_index;
    bool speculative;
    bool branch_mispredicted;
    uint64_t predicted_target;
};

// Reorder Buffer class
class ReorderBuffer {
public:
    ReorderBuffer() = default;
    void CommitEntry() {}
    bool CanCommit() const { return false; }
    ROBEntry* GetCommitEntry() { return nullptr; }
    void FlushAll() {}
    bool IsFull() const { return false; }
    size_t GetSize() const { return 0; }
    uint32_t AllocateEntry(uint64_t pc, const DecodedInstruction& instr) { return 0; }
};

// Optimization hints structure
struct OptimizationHints {
    bool enable_out_of_order = false;
    uint32_t fetch_width = 4;
    uint32_t decode_width = 4;
    uint32_t execute_width = 4;
};

// Performance metrics structure
struct PerformanceMetrics {
    uint64_t total_cycles = 0;
    uint64_t total_instructions = 0;
    double avg_ipc = 0.0;
    double peak_ipc = 0.0;
    double cache_miss_rate = 0.0;
    double branch_miss_rate = 0.0;
    double pipeline_utilization = 0.0;
};

// Pipeline statistics structure
struct PipelineStats {
    uint64_t cycles;
    uint64_t instructionsExecuted;
    uint64_t stalls;
    uint64_t data_hazard_stalls;
    uint64_t structural_hazard_stalls;
    uint64_t memory_stalls;
    uint64_t branch_hits;
    uint64_t branch_mispredictions;
    uint64_t simd_instructions;

    // Cache statistics
    uint64_t cache_l1_hits;
    uint64_t cache_l1_misses;
    uint64_t cache_l2_hits;
    uint64_t cache_l2_misses;
    uint64_t prefetch_hits;
    uint64_t prefetch_misses;

    // TLB and memory protection statistics
    uint64_t tlb_hits;
    uint64_t tlb_misses;
    uint64_t memory_protection_faults;

    // Performance counters
    uint64_t atomic_operations;
    uint64_t lock_contentions;
    uint64_t thread_switches;

    // JIT statistics
    uint64_t jit_executions;
    uint64_t jit_cache_hits;
    uint64_t jit_cache_misses;
    uint64_t jit_fallbacks;
    
    // Exception statistics
    uint64_t exceptions;
    
    PipelineStats() : cycles(0), instructionsExecuted(0), stalls(0), data_hazard_stalls(0),
                     structural_hazard_stalls(0), memory_stalls(0), branch_hits(0),
                     branch_mispredictions(0), simd_instructions(0), cache_l1_hits(0),
                     cache_l1_misses(0), cache_l2_hits(0), cache_l2_misses(0),
                     prefetch_hits(0), prefetch_misses(0), tlb_hits(0), tlb_misses(0),
                     memory_protection_faults(0), atomic_operations(0), lock_contentions(0),
                     thread_switches(0), jit_executions(0), jit_cache_hits(0),
                     jit_cache_misses(0), jit_fallbacks(0), exceptions(0) {}
};

class Pipeline {
private:
    X86_64CPU& cpu;
    ps4::PS4MMU& memory_;
    jit::JitCache& jitCache_;
    jit::JitTranslator& translator_;
    int cpuId_;
    uint64_t pc_;

    // Pipeline stages
    std::vector<FetchStage> fetchStage;
    std::vector<DecodeStage> decodeStage;
    std::vector<ExecuteStage> executeStage;
    std::vector<MemoryStage> memoryStage;
    std::vector<WriteBackStage> writeBackStage;

    // Pipeline statistics
    PipelineStats stats;

    // Branch predictor
    BranchPredictor branchPredictor;

    // Execution units and mapping
    std::vector<ExecutionUnit> execution_units;
    std::unordered_map<ExecutionUnitType, std::vector<size_t>> unit_map;
    size_t execution_units_count;

    // Instruction decoder and JIT
    InstructionDecoder decoder;
    std::unique_ptr<X86_64JITCompiler> jit;

    // Out-of-order execution support
    ReorderBuffer reorderBuffer;
    OptimizationHints optimizationHints;

    // Profiling data
    std::vector<ProfilingEntry> profilingData;

    // Register dependency tracking
    std::unordered_map<uint32_t, bool> register_scoreboard;
    std::unordered_map<uint32_t, uint64_t> register_dependencies;
    std::unordered_map<uint64_t, std::vector<uint32_t>> instruction_dependencies;
    std::unordered_map<uint32_t, RegisterDependency> register_dependency_map;
    std::unordered_map<uint64_t, InstructionDependency> instruction_dependency_map;
    std::vector<PhysicalRegister> physical_register_file;
    std::unordered_map<uint32_t, uint32_t> architectural_to_physical;
    std::queue<uint32_t> free_physical_registers;

    // Contention tracking and synchronization
    std::atomic<uint32_t> contentionCounter;
    mutable std::timed_mutex mutex;
    
    // Performance counters and dispatch table
    uint64_t m_hits;
    uint64_t m_misses;
    std::unordered_map<uint16_t, std::function<void(ExecuteStage&)>> opcodeDispatchTable;

public:
    // Constructor that takes CPU reference and JIT components
    Pipeline(X86_64CPU& cpu_ref, ps4::PS4MMU& memory, jit::JitCache& jitCache, jit::JitTranslator& translator);

    // Pipeline control methods
    void Step();
    void Flush();
    void ResetStats();
    bool ValidatePipelineState() const;

    // Stage execution methods
    void Fetch();
    void Decode();
    void Execute();
    void Memory();
    void WriteBack();

    // Instruction execution
    void InterpretInstruction(ExecuteStage& exec);

    // Hazard detection
    bool HasWAWHazard(const DecodedInstruction& instr, uint64_t pc) const;
    bool HasWARHazard(const DecodedInstruction& instr, uint64_t pc) const;
    HazardInfo DetectStructuralHazard(const DecodedInstruction& instr) const;
    bool HasDataHazard(const DecodedInstruction& instr, uint64_t pc) const;
    bool HasStructuralHazard() const;
    HazardInfo DetectDataHazard(const DecodedInstruction& instr, uint64_t pc) const;
    bool HasRAWHazard(const DecodedInstruction& instr, uint64_t pc) const;
    bool Conflicts(const DecodedInstruction& instr1, const DecodedInstruction& instr2) const;
    bool Conflicts(const DecodedInstruction& instr1, uint64_t pc1, const DecodedInstruction& instr2, uint64_t pc2) const;

    // Execution unit management
    ExecutionUnitType GetRequiredExecutionUnit(const DecodedInstruction& instr) const;
    int GetInstructionLatency(const DecodedInstruction& instr) const;
    bool IsExecutionUnitAvailable(ExecutionUnitType type) const;
    ExecutionUnit* AllocateExecutionUnit(ExecutionUnitType type);
    void ReleaseExecutionUnit(ExecutionUnit* unit);

    // JIT and caching
    bool translateAndCache(uint64_t pc);

    // Out-of-order execution support
    bool IsOutOfOrderEnabled() const;
    void CommitInstructions();
    void ScheduleInstructions();
    void DispatchReadyInstructions();

    // Statistics and diagnostics
    const PipelineStats& GetStats() const;

    // Pipeline stage occupancy methods
    size_t GetFetchStageOccupancy() const;
    size_t GetDecodeStageOccupancy() const;
    size_t GetExecuteStageOccupancy() const;
    size_t GetMemoryStageOccupancy() const;
    size_t GetWriteBackStageOccupancy() const;

    // Additional pipeline methods
    void InitializeExecutionUnits();
    void InitializeOpcodeDispatchTable();
    void InitializeRegisterDependencyTracking();
    void InitializeReservationStations();
    void UpdateProfiling();
    std::unordered_map<std::string, uint64_t> GetDiagnostics() const;
    void HandleBranchMisprediction(uint64_t actual_target, uint64_t predicted_target, uint64_t branch_pc);
    void RecoverFromMisprediction(uint64_t correct_target);
    void FlushFromStage(int stage_num);
    void FetchMultiple();
    const DecodedInstruction::Operand* SafeGetOperand(const DecodedInstruction& instr, int operand_index) const;
    const DecodedInstruction::Operand* SafeGetOperand(const DecodedInstruction& instr, uint8_t operand_index) const;
    
    // Additional helper methods
    bool TryAcquireMutexWithBackoff(std::timed_mutex& mutex_ref, std::chrono::microseconds baseTimeout);
    void HandleFallbackInstruction(ExecuteStage& exec);
    bool IsBranchInstruction(const DecodedInstruction& instr) const;
    bool IsSIMDInstruction(const DecodedInstruction& instr) const;
    std::function<void(ExecuteStage&)> CreateSafeHandler(std::function<void(ExecuteStage&)> handler) const;
    void SwitchToFiber(void* fiberHandle);
    
    // Register dependency helper methods
    void UpdateInstructionDependencies(const DecodedInstruction& instr, uint64_t pc, uint64_t cycle);
    std::vector<uint32_t> GetRegisterDependencies(const DecodedInstruction& instr) const;
    bool CheckRegisterAvailability(uint32_t reg_id) const;
    void MarkRegisterBusy(uint32_t reg_id, uint64_t cycle);
    void MarkRegisterReady(uint32_t reg_id);
    void MarkRegisterReady(uint32_t reg_id, uint64_t cycle);
    bool CanExecuteInstruction(const DecodedInstruction& instr) const;

    // Register dependency methods
    void UpdateRegisterDependencies(const DecodedInstruction& instr, uint64_t cycle);
    void ResolveRegisterDependency(uint32_t reg_id, uint64_t cycle);
    void ClearOldDependencies(uint64_t current_cycle);
    std::vector<uint32_t> GetRegisterReads(const DecodedInstruction& instr) const;
    std::vector<uint32_t> GetRegisterWrites(const DecodedInstruction& instr) const;
    void FlushRegisterMappings();
    
    // Register renaming methods
    uint32_t AllocatePhysicalRegister();
    void ReleasePhysicalRegister(uint32_t physical_id);
    uint32_t GetPhysicalRegister(uint32_t architectural_id);
    void MapArchitecturalRegister(uint32_t architectural_id, uint32_t physical_id);
    void CommitRegisterMapping(uint32_t architectural_id);
    
    // Out-of-order execution methods
    void EnableOutOfOrderExecution(bool enable);
    void HandleBranchMispredictionROB(uint64_t correct_pc);
    void HandlePreciseException(uint64_t pc, uint32_t exception_code);
    void RecoverFromException();
    void FlushSpeculativeInstructions(uint64_t recovery_pc);
    size_t GetROBOccupancy() const;
    uint32_t IssueToReservationStation(uint32_t rob_index, const DecodedInstruction& instr, uint64_t pc);
    void* GetReservationStation(ExecutionUnitType type);
    bool CanIssueInstruction(const DecodedInstruction& instr) const;
    void UpdateOperandDependencies(uint32_t rs_index, const DecodedInstruction& instr);
    void WakeupDependentInstructions(uint32_t rob_index, uint64_t result_value);
    ExecutionUnitType GetExecutionUnitType(const DecodedInstruction& instr) const;
    
    // Register renaming methods
    uint32_t RenameRegister(uint32_t arch_reg);
    void CommitRegisterRename(uint32_t arch_reg);
    uint32_t CreateRenameCheckpoint();
    void RestoreRenameCheckpoint(uint32_t checkpoint_id);
};

// Utility namespace for pipeline functions
namespace PipelineUtils {
    double CalculateIPC(const PipelineStats& stats);
    double CalculateCacheHitRatio(const PipelineStats& stats);
    double CalculateBranchPredictionAccuracy(const PipelineStats& stats);
    double CalculateJITEfficiency(const PipelineStats& stats);
    bool ValidateConfiguration();
    void AnalyzePerformance(const std::vector<ProfilingEntry>& data);
}

} // namespace x86_64