// Copyright 2025 <Copyright Owner>

#pragma once

#define NOMINMAX // Prevent conflicts with std::min and std::max
#undef RELATIVE  // Prevent conflicts with RELATIVE macro in Windows headers

#include "../syscall/syscall_handler.h"
#include <atomic>
#include <condition_variable>
#include <map>
#include <memory>
#include <mutex>
#include <queue>
#include <shared_mutex>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>
#include <ostream>
#include <istream>

namespace ps4 {

class PS4Emulator;
class FiberManager;
class TrophyManager;
class ZlibWrapper;

/**
 * @brief Exception for OrbisOS-related errors.
 */
struct OrbisOSException : std::runtime_error {
  explicit OrbisOSException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief Enum for compression levels.
 */
enum class CompressionLevel : int {
  DEFAULT = -1,
  NO_COMPRESSION = 0,
  BEST_SPEED = 1,
  BEST_COMPRESSION = 9
};

#ifndef PROT_NONE
#define PROT_NONE 0x0
#define PROT_READ 0x1
#define PROT_WRITE 0x2
#define PROT_EXEC 0x4
#endif

/**
 * @brief Structure representing a mutex.
 */
struct Mutex {
  std::string name;                   ///< Mutex name
  std::mutex lock;                    ///< Underlying mutex
  uint64_t ownerThread = 0;           ///< Owning thread ID
  uint32_t recursionCount = 0;        ///< Recursion count
  std::queue<uint64_t> waitingThreads; ///< Waiting thread IDs
  uint64_t lockCount = 0;             ///< Total locks
  uint64_t contentionCount = 0;       ///< Contention events
};

/**
 * @brief Structure representing a semaphore.
 */
struct Semaphore {
  std::string name;                   ///< Semaphore name
  uint32_t count = 0;                 ///< Current count
  std::mutex mutex;                   ///< Underlying mutex
  std::condition_variable cv;         ///< Condition variable
  std::queue<uint64_t> waitingThreads; ///< Waiting thread IDs
  uint64_t signalCount = 0;           ///< Total signals
  uint64_t waitCount = 0;             ///< Total waits
};

/**
 * @brief Structure representing a process.
 */
struct Process {
  uint64_t id;                        ///< Process ID
  std::string path;                   ///< Executable path
  uint64_t baseAddress;               ///< Base virtual address
  uint64_t size;                      ///< Process size
  std::unique_ptr<std::vector<SyscallHandler::FDInfo>> fdTable; ///< File descriptor table
  std::unique_ptr<std::map<uint64_t, uint64_t>> memoryMap;     ///< Memory mappings
  uint64_t memoryAllocations = 0;     ///< Total memory allocations
  uint64_t memoryFrees = 0;           ///< Total memory frees

  Process(uint64_t pid, const std::string &path, uint64_t baseAddr, uint64_t sz)
      : id(pid), path(path), baseAddress(baseAddr), size(sz) {
    fdTable = std::make_unique<std::vector<SyscallHandler::FDInfo>>();
    memoryMap = std::make_unique<std::map<uint64_t, uint64_t>>();
  }
};

/**
 * @brief Structure representing a thread.
 */
struct Thread {
  uint64_t id;                        ///< Thread ID
  uint64_t processId;                 ///< Parent process ID
  uint64_t entry;                     ///< Entry point
  void *arg;                          ///< Thread argument
  int priority;                       ///< Priority (0-255)
  int assignedCoreId;                 ///< Assigned CPU core
  std::thread hostThread;             ///< Host thread
  std::atomic<bool> running{false};   ///< Running state
  std::atomic<bool> waiting{false};   ///< Waiting state
  std::mutex mutex;                   ///< Thread mutex
  std::condition_variable cv;         ///< Condition variable
  uint64_t cyclesExecuted = 0;        ///< Total cycles executed
};

/**
 * @brief Structure for OrbisOS statistics.
 */
struct OrbisOSStats {
  uint64_t processCount = 0;          ///< Total processes created
  uint64_t threadCount = 0;           ///< Total threads created
  uint64_t mutexLockCount = 0;        ///< Total mutex locks
  uint64_t semaphoreSignalCount = 0;  ///< Total semaphore signals
  uint64_t memoryAllocations = 0;     ///< Total memory allocations
  uint64_t totalLatencyUs = 0;        ///< Total operation latency (microseconds)
  uint64_t cacheHits = 0;             ///< Cache hits for operations
  uint64_t cacheMisses = 0;           ///< Cache misses for operations
};

/**
 * @brief Manages PS4 OS functionality, including processes, threads, and synchronization.
 * @details Provides thread-safe process and thread management, memory operations, and system calls.
 */
class OrbisOS {
public:
  /**
   * @brief Constructs OrbisOS with an emulator reference.
   * @param emulator Reference to the PS4 emulator.
   */
  OrbisOS(PS4Emulator &emulator);

  /**
   * @brief Destructor, cleaning up resources.
   */
  ~OrbisOS();

  /**
   * @brief Initializes OrbisOS.
   * @return True on success, false otherwise.
   */
  bool Initialize();

  /**
   * @brief Shuts down OrbisOS, releasing resources.
   */
  void Shutdown();

  /**
   * @brief Creates a new process.
   * @param path Executable path.
   * @return Process ID, or 0 on failure.
   */
  uint64_t SceCreateProcess(const std::string &path);

  /**
   * @brief Exits the current process.
   * @param code Exit code.
   */
  void ExitProcess(int code);

  /**
   * @brief Creates a new thread.
   * @param processId Parent process ID.
   * @param entry Entry point.
   * @param arg Thread argument.
   * @param priority Thread priority.
   * @return Thread ID, or 0 on failure.
   */
  uint64_t CreateThread(uint64_t processId, uint64_t entry, void *arg, int priority);

  /**
   * @brief Exits the current thread.
   * @param tid Thread ID.
   */
  void ExitThread(uint64_t tid);

  /**
   * @brief Schedules threads for execution.
   */
  void ScheduleThreads();

  /**
   * @brief Gets the current thread ID.
   * @return Thread ID, or 0 if none.
   */
  uint64_t GetCurrentThreadId();

  /**
   * @brief Creates a mutex.
   * @param name Mutex name.
   * @return Mutex ID, or 0 on failure.
   */
  uint64_t SceCreateMutex(const char *name);

  /**
   * @brief Locks a mutex with timeout.
   * @param mutexId Mutex ID.
   * @param timeoutUs Timeout in microseconds.
   * @return True on success, false on timeout or failure.
   */
  bool LockMutex(uint64_t mutexId, uint64_t timeoutUs);

  /**
   * @brief Attempts to lock a mutex without waiting.
   * @param mutexId Mutex ID.
   * @return True on success, false if locked.
   */
  bool TryLockMutex(uint64_t mutexId);

  /**
   * @brief Unlocks a mutex.
   * @param mutexId Mutex ID.
   */
  void UnlockMutex(uint64_t mutexId);

  /**
   * @brief Creates a semaphore.
   * @param name Semaphore name.
   * @param initialCount Initial count.
   * @return Semaphore ID, or 0 on failure.
   */
  uint64_t SceCreateSemaphore(const char *name, uint32_t initialCount);

  /**
   * @brief Waits on a semaphore with timeout.
   * @param semId Semaphore ID.
   * @param timeoutUs Timeout in microseconds.
   * @return True on success, false on timeout or failure.
   */
  bool WaitSemaphore(uint64_t semId, uint64_t timeoutUs);

  /**
   * @brief Signals a semaphore.
   * @param semId Semaphore ID.
   */
  void SignalSemaphore(uint64_t semId);

  /**
   * @brief Allocates virtual memory.
   * @param size Size to allocate.
   * @param alignment Alignment requirement.
   * @param shared True if shared memory.
   * @return Virtual address, or 0 on failure.
   */
  uint64_t AllocateVirtualMemory(uint64_t size, uint64_t alignment, bool shared);

  /**
   * @brief Frees virtual memory.
   * @param address Virtual address.
   */
  void FreeVirtualMemory(uint64_t address);

  /**
   * @brief Sets memory protection.
   * @param address Virtual address.
   * @param size Size of region.
   * @param protection Protection flags.
   * @return True on success, false otherwise.
   */
  bool ProtectMemory(uint64_t address, uint64_t size, int protection);

  /**
   * @brief Gets the current process ID.
   * @return Process ID.
   */
  uint64_t SceKernelGetProcessId();

  /**
   * @brief Delays execution for a specified time.
   * @param microseconds Delay in microseconds.
   * @return 0 on success, -1 on failure.
   */
  int SceKernelUsleep(uint32_t microseconds);

  /**
   * @brief Gets the CPU frequency.
   * @return Frequency in Hz.
   */
  int SceKernelGetCpuFrequency();

  /**
   * @brief Creates a fiber.
   * @param name Fiber name.
   * @param entry Entry point.
   * @param arg Fiber argument.
   * @param stackSize Stack size.
   * @return Fiber ID, or 0 on failure.
   */
  uint64_t SceKernelCreateFiber(const char *name, uint64_t entry, void *arg, uint64_t stackSize);

  /**
   * @brief Deletes a fiber.
   * @param fiberId Fiber ID.
   * @return 0 on success, -1 on failure.
   */
  int SceKernelDeleteFiber(uint64_t fiberId);

  /**
   * @brief Switches to a fiber.
   * @param fiberId Fiber ID.
   * @param argCount Number of arguments.
   * @param args Argument array.
   * @return 0 on success, -1 on failure.
   */
  int SceKernelSwitchToFiber(uint64_t fiberId, uint64_t argCount, void *args);

  /**
   * @brief Initializes the trophy system.
   * @return 0 on success, -1 on failure.
   */
  int SceNpTrophyInit();

  /**
   * @brief Creates a trophy context.
   * @param context Output context ID.
   * @param titleId Title ID.
   * @param titleSecret Title secret.
   * @param userData User data.
   * @return 0 on success, -1 on failure.
   */
  int SceNpTrophyCreateContext(uint32_t *context, const char *titleId, const char *titleSecret, void *userData);

  /**
   * @brief Unlocks a trophy.
   * @param context Context ID.
   * @param handle Handle ID.
   * @param trophyId Trophy ID.
   * @param platinumId Output platinum ID.
   * @return 0 on success, -1 on failure.
   */
  int SceNpTrophyUnlockTrophy(uint32_t context, uint32_t handle, uint32_t trophyId, uint32_t *platinumId);

  /**
   * @brief Decompresses data using zlib.
   * @param dst Destination buffer.
   * @param dstSize Destination size.
   * @param src Source buffer.
   * @param srcSize Source size.
   * @return 0 on success, -1 on failure.
   */
  int SceZlibDecompress(void *dst, size_t *dstSize, const void *src, size_t srcSize);

  /**
   * @brief Compresses data using zlib.
   * @param dst Destination buffer.
   * @param dstSize Destination size.
   * @param src Source buffer.
   * @param srcSize Source size.
   * @param level Compression level.
   * @return 0 on success, -1 on failure.
   */
  int SceZlibCompress(void *dst, size_t *dstSize, const void *src, size_t srcSize, int level);

  /**
   * @brief Retrieves OrbisOS statistics.
   * @return Current statistics.
   */
  OrbisOSStats GetStats() const;

  /**
   * @brief Saves the OrbisOS state to a stream.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the OrbisOS state from a stream.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

private:
  /**
   * @brief Thread entry point.
   * @param tid Thread ID.
   * @param entry Entry point.
   * @param arg Thread argument.
   */
  void ThreadEntry(uint64_t tid, uint64_t entry, void *arg);

  PS4Emulator &m_emulator; ///< Reference to emulator
  mutable std::shared_mutex m_osMutex; ///< Mutex for thread safety
  std::mutex m_tidMapMutex; ///< Mutex for thread ID mapping
  std::atomic<uint64_t> m_nextProcessId{1}; ///< Next process ID
  std::atomic<uint64_t> m_nextThreadId{1}; ///< Next thread ID
  std::map<uint64_t, Process> m_processes; ///< Active processes
  std::map<uint64_t, Thread> m_threads; ///< Active threads
  std::unordered_map<std::thread::id, uint64_t> m_hostToEmulatedTidMap; ///< Host to emulated thread ID map
  std::atomic<uint64_t> m_nextMutexId{1}; ///< Next mutex ID
  std::atomic<uint64_t> m_nextSemId{1}; ///< Next semaphore ID
  std::map<uint64_t, Mutex> m_mutexes; ///< Active mutexes
  std::map<uint64_t, Semaphore> m_semaphores; ///< Active semaphores
  OrbisOSStats m_stats; ///< OrbisOS statistics
};

} // namespace ps4