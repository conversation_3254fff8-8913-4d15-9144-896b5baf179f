#pragma once

#include "../memory/ps4_mmu.h"
#include "../video_core/gnm_shader_translator.h"
#include "../video_core/gnm_state.h"
#include "../video_core/tile_manager.h"
#include <SDL.h>
#include <functional>
#include <memory>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <vector>
#include <vulkan/vulkan.h>

#ifdef _WIN32
#define VK_USE_PLATFORM_WIN32_KHR
#endif
#ifdef __linux__
#define VK_USE_PLATFORM_XCB_KHR
#endif

namespace ps4 {

/**
 * @brief Exception for GPU-related errors.
 */
struct GPUException : std::runtime_error {
  explicit GPUException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief Structure for Vulkan context.
 */
struct VulkanContext {
  VkInstance instance = VK_NULL_HANDLE;             ///< Vulkan instance
  VkPhysicalDevice physicalDevice = VK_NULL_HANDLE; ///< Physical device
  VkDevice device = VK_NULL_HANDLE;                 ///< Logical device
  VkQueue graphicsQueue = VK_NULL_HANDLE;           ///< Graphics queue
  VkQueue computeQueue = VK_NULL_HANDLE;            ///< Compute queue
  VkQueue presentQueue = VK_NULL_HANDLE;            ///< Present queue
  VkCommandPool commandPool = VK_NULL_HANDLE;       ///< Command pool
  VkSwapchainKHR swapchain = VK_NULL_HANDLE;        ///< Swapchain
  std::vector<VkImage> swapchainImages;             ///< Swapchain images
  std::vector<VkImageView> swapchainImageViews;     ///< Swapchain image views
  VkExtent2D swapchainExtent = {0, 0};              ///< Swapchain extent
  VkFormat swapchainImageFormat = VK_FORMAT_UNDEFINED; ///< Swapchain format
  VkSurfaceKHR surface = VK_NULL_HANDLE;               ///< Surface
  VkDescriptorPool descriptorPool = VK_NULL_HANDLE;    ///< Descriptor pool
  VkSemaphore imageAvailableSemaphore = VK_NULL_HANDLE; ///< Image available semaphore
  VkSemaphore renderFinishedSemaphore = VK_NULL_HANDLE; ///< Render finished semaphore
  VkFence inFlightFence = VK_NULL_HANDLE;              ///< In-flight fence
  uint32_t graphicsQueueFamily = UINT32_MAX;           ///< Graphics queue family
  uint32_t presentQueueFamily = UINT32_MAX;            ///< Present queue family
  uint32_t computeQueueFamily = UINT32_MAX;            ///< Compute queue family
  std::shared_mutex contextMutex;                      ///< Mutex for thread safety
  std::vector<VkFence> in_flight_fences;               ///< In-flight fences
  uint32_t currentFrame = 0;                           ///< Current frame index
  uint64_t cacheHits = 0;                              ///< Cache hits for Vulkan operations
  uint64_t cacheMisses = 0;                            ///< Cache misses for Vulkan operations
  std::vector<VkSemaphore> renderFinishedSemaphores;   ///< Render finished semaphores
  uint64_t frameCount = 0;                             ///< Frame count
  uint64_t renderLatencyUs = 0;                        ///< Render latency in microseconds
  std::vector<VkFramebuffer> framebuffers;             ///< Framebuffers
  VkRenderPass renderPass = VK_NULL_HANDLE;            ///< Render pass
};

/**
 * @brief Structure for shader module.
 */
struct ShaderModule {
  VkShaderModule module = VK_NULL_HANDLE; ///< Shader module
  GCNShaderType type = {};                ///< Shader type
  std::vector<uint32_t> spirvCode;        ///< SPIR-V code
};

/**
 * @brief Structure for render target.
 */
struct RenderTarget {
  uint64_t surfaceId = 0;                 ///< Surface ID
  VkImage image = VK_NULL_HANDLE;          ///< Image
  VkDeviceMemory memory = VK_NULL_HANDLE;       ///< Memory
  VkImageView view = VK_NULL_HANDLE;       ///< Image view
  VkFormat format = VK_FORMAT_UNDEFINED;  ///< Format
  uint32_t width = 0;                     ///< Width
  uint32_t height = 0;                    ///< Height
  bool isDepthStencil = false;            ///< Depth-stencil flag
};

/**
 * @brief Structure for memory mapping.
 */
struct MemoryMapping {
  uint64_t gpuAddress = 0;                ///< GPU address
  uint64_t cpuAddress = 0;                ///< CPU address
  size_t size = 0;                        ///< Size
  VkBuffer buffer = VK_NULL_HANDLE;        ///< Buffer
  VkDeviceMemory memory = VK_NULL_HANDLE;       ///< Memory
  void *mappedData = nullptr;             ///< Mapped data
};

/**
 * @brief Structure for tessellation state.
 */
struct TessellationState {
  float tessFactor = 1.0f;                   ///< Tessellation factor
  VkPrimitiveTopology topology = VK_PRIMITIVE_TOPOLOGY_PATCH_LIST; ///< Topology
  uint32_t patchControlPoints = 3;           ///< Number of control points
};

/**
 * @brief Structure for graphics pipeline key.
 */
struct GraphicsPipelineKey {
  uint64_t vsShaderId = 0;                  ///< Vertex shader ID
  uint64_t psShaderId = 0;                  ///< Pixel shader ID
  uint64_t gsShaderId = 0;                  ///< Geometry shader ID
  uint64_t hsShaderId = 0;                  ///< Hull shader ID
  uint64_t dsShaderId = 0;                  ///< Domain shader ID
  uint32_t rasterizerStateHash = 0;         ///< Rasterizer state hash
  uint32_t blendStateHash = 0;              ///< Blend state hash
  uint32_t depthStencilStateHash = 0;       ///< Depth-stencil state hash
  uint32_t vertexInputHash = 0;             ///< Vertex input hash
  VkRenderPass renderPass = VK_NULL_HANDLE; ///< Render pass
  uint32_t subpassIndex = 0;                ///< Subpass index
  VkPrimitiveTopology topology = VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST; ///< Topology
  TessellationState tessState;              ///< Tessellation state

  bool operator==(const GraphicsPipelineKey &other) const {
    return vsShaderId == other.vsShaderId && psShaderId == other.psShaderId &&
           gsShaderId == other.gsShaderId && hsShaderId == other.hsShaderId &&
           dsShaderId == other.dsShaderId &&
           rasterizerStateHash == other.rasterizerStateHash &&
           blendStateHash == other.blendStateHash &&
           depthStencilStateHash == other.depthStencilStateHash &&
           vertexInputHash == other.vertexInputHash &&
           renderPass == other.renderPass &&
           subpassIndex == other.subpassIndex && topology == other.topology &&
           tessState.tessFactor == other.tessState.tessFactor &&
           tessState.topology == other.tessState.topology &&
           tessState.patchControlPoints == other.tessState.patchControlPoints;
  }
};

/**
 * @brief Hash function for GraphicsPipelineKey.
 */
struct GraphicsPipelineKeyHash {
  std::size_t operator()(const GraphicsPipelineKey &key) const {
    size_t seed = 0;
    auto hash_combine = [&](size_t &seed, auto const &v) {
      using T = std::decay_t<decltype(v)>;
      if constexpr (std::is_enum_v<T>) {
        seed ^= std::hash<std::underlying_type_t<T>>{}(
                    static_cast<std::underlying_type_t<T>>(v)) +
                0x9e3779b9 + (seed << 6) + (seed >> 2);
      } else if constexpr (std::is_same_v<T, VkRenderPass> ||
                           std::is_pointer_v<T>) {
        seed ^= std::hash<uint64_t>{}(reinterpret_cast<uint64_t>(v)) +
                0x9e3779b9 + (seed << 6) + (seed >> 2);
      } else {
        seed ^= std::hash<T>{}(v) + 0x9e3779b9 + (seed << 6) + (seed >> 2);
      }
    };
    hash_combine(seed, key.vsShaderId);
    hash_combine(seed, key.psShaderId);
    hash_combine(seed, key.gsShaderId);
    hash_combine(seed, key.hsShaderId);
    hash_combine(seed, key.dsShaderId);
    hash_combine(seed, key.rasterizerStateHash);
    hash_combine(seed, key.blendStateHash);
    hash_combine(seed, key.depthStencilStateHash);
    hash_combine(seed, key.vertexInputHash);
    hash_combine(seed, key.renderPass);
    hash_combine(seed, key.subpassIndex);
    hash_combine(seed, key.topology);
    hash_combine(seed, key.tessState.tessFactor);
    hash_combine(seed, key.tessState.topology);
    hash_combine(seed, key.tessState.patchControlPoints);
    return seed;
  }
};

/**
 * @brief Structure for compute pipeline key.
 */
struct ComputePipelineKey {
  uint64_t csShaderId = 0;                ///< Compute shader ID
  uint32_t resourceStateHash = 0;         ///< Resource state hash

  bool operator==(const ComputePipelineKey &other) const {
    return csShaderId == other.csShaderId &&
           resourceStateHash == other.resourceStateHash;
  }
};

/**
 * @brief Hash function for ComputePipelineKey.
 */
struct ComputePipelineKeyHash {
  std::size_t operator()(const ComputePipelineKey &key) const {
    size_t seed = 0;
    auto hash_combine = [&](size_t &seed, auto const &v) {
      seed ^= std::hash<std::decay_t<decltype(v)>>{}(v) + 0x9e3779b9 +
              (seed << 6) + (seed >> 2);
    };
    hash_combine(seed, key.csShaderId);
    hash_combine(seed, key.resourceStateHash);
    return seed;
  }
};

/**
 * @brief Structure for render pass key.
 */
struct RenderPassKey {
  std::vector<VkFormat> colorFormats;         ///< Color formats
  VkFormat depthFormat = VK_FORMAT_UNDEFINED; ///< Depth format
  VkAttachmentLoadOp colorLoadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE; ///< Color load op
  VkAttachmentStoreOp colorStoreOp = VK_ATTACHMENT_STORE_OP_DONT_CARE; ///< Color store op
  VkAttachmentLoadOp depthLoadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE; ///< Depth load op
  VkAttachmentStoreOp depthStoreOp = VK_ATTACHMENT_STORE_OP_DONT_CARE; ///< Depth store op
  VkImageLayout initialColorLayout = VK_IMAGE_LAYOUT_UNDEFINED; ///< Initial color layout
  VkImageLayout finalColorLayout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL; ///< Final color layout
  VkImageLayout initialDepthLayout = VK_IMAGE_LAYOUT_UNDEFINED; ///< Initial depth layout
  VkImageLayout finalDepthLayout = VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT_OPTIMAL; ///< Final depth layout

  bool operator==(const RenderPassKey &other) const {
    return colorFormats == other.colorFormats &&
           depthFormat == other.depthFormat &&
           colorLoadOp == other.colorLoadOp &&
           colorStoreOp == other.colorStoreOp &&
           depthLoadOp == other.depthLoadOp &&
           depthStoreOp == other.depthStoreOp &&
           initialColorLayout == other.initialColorLayout &&
           finalColorLayout == other.finalColorLayout &&
           initialDepthLayout == other.initialDepthLayout &&
           finalDepthLayout == other.finalDepthLayout;
  }
};

/**
 * @brief Hash function for RenderPassKey.
 */
struct RenderPassKeyHash {
  std::size_t operator()(const RenderPassKey &key) const {
    size_t seed = 0;
    auto hash_combine = [&](size_t &seed, auto const &v) {
      using T = std::decay_t<decltype(v)>;
      if constexpr (std::is_enum_v<T>) {
        seed ^= std::hash<std::underlying_type_t<T>>{}(
                    static_cast<std::underlying_type_t<T>>(v)) +
                0x9e3779b9 + (seed << 6) + (seed >> 2);
      } else if constexpr (std::is_same_v<T, VkRenderPass> ||
                           std::is_pointer_v<T>) {
        seed ^= std::hash<uint64_t>{}(reinterpret_cast<uint64_t>(v)) +
                0x9e3779b9 + (seed << 6) + (seed >> 2);
      } else {
        seed ^= std::hash<T>{}(v) + 0x9e3779b9 + (seed << 6) + (seed >> 2);
      }
    };
    for (const auto &fmt : key.colorFormats) {
      hash_combine(seed, static_cast<std::underlying_type_t<VkFormat>>(fmt));
    }
    hash_combine(seed, static_cast<std::underlying_type_t<VkFormat>>(key.depthFormat));
    hash_combine(seed, static_cast<std::underlying_type_t<VkAttachmentLoadOp>>(key.colorLoadOp));
    hash_combine(seed, static_cast<std::underlying_type_t<VkAttachmentStoreOp>>(key.colorStoreOp));
    hash_combine(seed, static_cast<std::underlying_type_t<VkAttachmentLoadOp>>(key.depthLoadOp));
    hash_combine(seed, static_cast<std::underlying_type_t<VkAttachmentStoreOp>>(key.depthStoreOp));
    hash_combine(seed, key.initialColorLayout);
    hash_combine(seed, key.finalColorLayout);
    hash_combine(seed, key.initialDepthLayout);
    hash_combine(seed, key.finalDepthLayout);
    return seed;
  }
};

/**
 * @brief Structure for profiling results.
 */
struct ProfileResult {
  std::string label;           ///< Profile label
  uint64_t startTimestamp = 0; ///< Start timestamp
  uint64_t endTimestamp = 0;   ///< End timestamp
};

/**
 * @brief Structure for GPU statistics with atomic members to prevent race conditions.
 */
struct GPUStats {
  std::atomic<uint64_t> drawCalls{0};          ///< Total draw calls
  std::atomic<uint64_t> shaderCompilations{0}; ///< Total shader compilations
  std::atomic<uint64_t> vramUsage{0};          ///< VRAM usage in bytes
  std::atomic<uint64_t> pipelineCreations{0};  ///< Total pipeline creations
  std::atomic<uint64_t> totalLatencyUs{0};     ///< Total operation latency
  std::atomic<uint64_t> cacheHits{0};          ///< Cache hits for GPU operations
  std::atomic<uint64_t> cacheMisses{0};        ///< Cache misses for GPU operations
  std::atomic<uint64_t> shaderCount{0};        ///< Number of active shaders
  std::atomic<uint64_t> drawCount{0};          ///< Total draw call count
  std::atomic<uint64_t> errorCount{0};         ///< Number of GPU errors
  std::atomic<uint64_t> computeDispatches{0};  ///< Total compute dispatches
  std::atomic<uint64_t> tessellationDraws{0};  ///< Total tessellation draws

  GPUStats() = default;
  GPUStats(const GPUStats &other)
      : drawCalls(other.drawCalls.load()),
        shaderCompilations(other.shaderCompilations.load()),
        vramUsage(other.vramUsage.load()),
        pipelineCreations(other.pipelineCreations.load()),
        totalLatencyUs(other.totalLatencyUs.load()),
        cacheHits(other.cacheHits.load()),
        cacheMisses(other.cacheMisses.load()),
        shaderCount(other.shaderCount.load()),
        drawCount(other.drawCount.load()),
        errorCount(other.errorCount.load()),
        computeDispatches(other.computeDispatches.load()),
        tessellationDraws(other.tessellationDraws.load()) {}

  GPUStats &operator=(const GPUStats &other) {
    if (this != &other) {
      drawCalls.store(other.drawCalls.load());
      shaderCompilations.store(other.shaderCompilations.load());
      vramUsage.store(other.vramUsage.load());
      pipelineCreations.store(other.pipelineCreations.load());
      totalLatencyUs.store(other.totalLatencyUs.load());
      cacheHits.store(other.cacheHits.load());
      cacheMisses.store(other.cacheMisses.load());
      shaderCount.store(other.shaderCount.load());
      drawCount.store(other.drawCount.load());
      errorCount.store(other.errorCount.load());
      computeDispatches.store(other.computeDispatches.load());
      tessellationDraws.store(other.tessellationDraws.load());
    }
    return *this;
  }
};

/**
 * @brief Emulates the PS4 GPU using Vulkan.
 * @details Manages rendering, shader compilation, and memory, with thread-safe access and metrics.
 */
class PS4GPU {
public:
  PS4GPU(PS4MMU &memory, std::unique_ptr<GNMShaderTranslator> translator,
         std::unique_ptr<TileManager> tileManager, VulkanContext *vulkanContext,
         SDL_Window *window);
  ~PS4GPU();

  bool Initialize();
  void Shutdown();
  void BeginFrame();
  void EndFrame();
  void Present();
  void SetShaderRegister(uint32_t stage, uint32_t offset, uint32_t value);
  void SetContextRegister(uint32_t offset, uint32_t value);
  void SetRenderTarget(uint32_t index, uint64_t surfaceId);
  void SetDepthRenderTarget(uint64_t surfaceId);
  void SetViewport(float x, float y, float width, float height, float minDepth, float maxDepth);
  void SetScissor(int32_t x, int32_t y, uint32_t width, uint32_t height);
  void BindVertexBuffer(uint32_t binding, uint64_t gpuAddress, uint32_t stride);
  void BindIndexBuffer(uint64_t gpuAddress, VkIndexType indexType);
  void BindResource(uint32_t set, uint32_t binding, uint64_t resourceId);
  void DrawIndex(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex,
                 int32_t vertexOffset, uint32_t firstInstance);
  void DrawIndexIndirect(uint64_t bufferAddress, uint32_t drawCount, uint32_t stride);
  void DrawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex);
  void Dispatch(uint32_t groupCountX, uint32_t groupCountY, uint32_t groupCountZ);
  void DispatchIndirect(uint64_t bufferAddress, uint64_t offset);
  void WaitRegisterMemory(uint64_t address, uint32_t reference, uint32_t mask,
                         uint32_t function, bool isMemory);
  void AcquireMemory(uint64_t address, uint32_t size);
  void ReleaseMemory(uint64_t address, uint32_t size);
  void Nop();
  uint64_t CompileShader(const void *gcnCode, size_t size, GCNShaderType type);
  void UnloadShader(uint64_t shaderId);
  uint64_t MapMemory(uint64_t cpuAddress, size_t size, VkBufferUsageFlags usage);
  void UnmapMemory(uint64_t gpuAddress);
  uint64_t CreateTexture(uint64_t surfaceId);
  void UpdateTexture(uint64_t textureId, const void *data, size_t size);
  void DeleteTexture(uint64_t textureId);
  uint64_t CreateSampler();
  void DeleteSampler(uint64_t samplerId);
  void WaitForGPUIdle();
  void InsertFence(uint64_t *fenceValue);
  bool CheckFence(uint64_t fenceValue);
  void BeginGPUProfiler(const std::string &label);
  void EndGPUProfiler();
  std::vector<ProfileResult> GetProfileResults();
  VkDevice GetDevice() const;
  VkCommandBuffer GetCurrentCommandBuffer() const;
  const GNMRegisterState &GetGNMState() const;

  // New methods for GNM/Gnmx support
  void SetTessellationState(float tessFactor, uint32_t patchControlPoints);
  void BindComputeShader(uint64_t shaderId);
  uint64_t CreateTiledTexture(uint64_t surfaceId, uint32_t tileMode);
  uint64_t MapTiledMemory(uint64_t cpuAddress, size_t size, uint32_t tileMode);

  void NotifyPacketProcessed(uint32_t header, const std::vector<uint32_t> &data);
  void NotifyShaderTranslated(GCNShaderType type, uint64_t bytecodeHash,
                             const std::vector<uint32_t> &spirvCode);
  void NotifyShaderTranslated(GCNShaderType type, uint64_t bytecodeHash,
                             const std::string &glslCode);
  void NotifyRegisterChange(GNMRegisterType regType, uint32_t stage,
                            uint32_t offset, uint32_t value);
  void HandleShaderRegisterChange(uint32_t stage, uint32_t offset, uint32_t value);
  void HandleContextRegisterChange(uint32_t offset, uint32_t value);
  void HandleConfigRegisterChange(uint32_t offset, uint32_t value);
  void HandleUserRegisterChange(uint32_t offset, uint32_t value);
  void NotifyShaderExecuted(GCNShaderType shaderType, uint64_t instructionCount);
  GPUStats GetStats() const;
  void ClearShaderCache();
  void ClearRenderTargetCache();
  void SaveState(std::ostream &out) const;
  void LoadState(std::istream &in);
  GNMRegisterState &GetMutableGNMState();
  void SetTileManager(std::unique_ptr<TileManager> tileManager);
  TileManager &GetTileManager();
  GNMRegisterState &GetRegisterState();

private:
  bool CreateSwapchain();
  bool CreateSwapchainImageViews();
  bool RecreateSwapchain();
  bool CreateCommandPool();
  bool CreateDescriptorPool();
  bool CreateSyncObjects();
  bool CreateDefaultRenderPass();
  bool CreateFramebuffers();
  VkPipeline GetOrCreateGraphicsPipeline();
  GraphicsPipelineKey BuildGraphicsPipelineKey();
  VkPipeline GetOrCreateComputePipeline();
  VkRenderPass GetOrCreateRenderPass(const RenderPassKey &key);
  VkFramebuffer GetOrCreateFramebuffer(const std::vector<uint64_t> &colorTargetIds,
                                      uint64_t depthTargetId);
  VkDescriptorSetLayout GetOrCreateDescriptorSetLayout();
  VkDescriptorSet AllocateDescriptorSet(VkDescriptorSetLayout layout);
  void UpdateDescriptorSet();
  uint32_t FindMemoryType(uint32_t typeFilter, VkMemoryPropertyFlags properties);
  VkBuffer CreateBuffer(uint64_t size, VkBufferUsageFlags usage,
                        VkMemoryPropertyFlags properties, VkDeviceMemory &memory);
  void BeginCommandBuffer();
  void SubmitCommandBuffer();
  void TransitionImageLayout(VkImage image, VkFormat format,
                            VkImageLayout oldLayout, VkImageLayout newLayout);
  void CopyBufferToImage(VkBuffer buffer, VkImage image, uint32_t width,
                         uint32_t height);
  uint64_t TranslateGCNShader(const void *gcnCode, size_t size,
                              VkShaderStageFlagBits stage);

  PS4MMU &m_memory;                                    ///< Reference to MMU
  VulkanContext *m_vulkan;                             ///< Vulkan context
  SDL_Window *m_window;                                ///< SDL window
  std::unique_ptr<GNMShaderTranslator> m_shaderTranslator; ///< Shader translator
  std::unique_ptr<TileManager> m_tileManager;          ///< Tile manager
  GNMRegisterState m_gnmState;                         ///< GNM state
  GNMRegisterState *m_registerState = nullptr;         ///< Pointer to register state
  VkCommandBuffer m_commandBuffer = VK_NULL_HANDLE;    ///< Current command buffer
  uint32_t m_currentFrame = 0;                         ///< Current frame index
  uint32_t m_currentSwapchainImageIndex = 0;           ///< Current swapchain image index
  std::unordered_map<uint64_t, ShaderModule> m_shaderModules; ///< Shader modules
  std::unordered_map<uint64_t, RenderTarget> m_renderTargets; ///< Render targets
  std::unordered_map<uint64_t, VkImageView> m_textureViews;   ///< Texture views
  std::unordered_map<uint64_t, VkSampler> m_samplers;         ///< Samplers
  std::unordered_map<uint64_t, MemoryMapping> m_memoryMappings; ///< Memory mappings
  std::unordered_map<GraphicsPipelineKey, VkPipeline, GraphicsPipelineKeyHash>
      m_graphicsPipelineCache; ///< Graphics pipeline cache
  std::unordered_map<ComputePipelineKey, VkPipeline, ComputePipelineKeyHash>
      m_computePipelineCache; ///< Compute pipeline cache
  std::unordered_map<RenderPassKey, VkRenderPass, RenderPassKeyHash>
      m_renderPassCache; ///< Render pass cache
  std::unordered_map<uint64_t, VkFramebuffer> m_framebufferCache; ///< Framebuffer cache
  VkRenderPass m_currentRenderPass = VK_NULL_HANDLE;   ///< Current render pass
  VkFramebuffer m_currentFramebuffer = VK_NULL_HANDLE; ///< Current framebuffer
  VkPipelineLayout m_currentPipelineLayout = VK_NULL_HANDLE; ///< Current pipeline layout
  VkPipeline m_currentGraphicsPipeline = VK_NULL_HANDLE; ///< Current graphics pipeline
  VkPipeline m_currentComputePipeline = VK_NULL_HANDLE; ///< Current compute pipeline
  std::vector<VkDescriptorSet> m_currentDescriptorSets; ///< Current descriptor sets
  VkBuffer m_currentIndexBuffer = VK_NULL_HANDLE;      ///< Current index buffer
  VkIndexType m_currentIndexType = VK_INDEX_TYPE_UINT16; ///< Current index type
  std::vector<VkBuffer> m_currentVertexBuffers;         ///< Current vertex buffers
  std::vector<VkDeviceSize> m_currentVertexBufferOffsets; ///< Current vertex buffer offsets
  std::unordered_map<uint64_t, VkFence> m_fences;       ///< Fences
  uint64_t m_nextFenceValue = 1;                        ///< Next fence value
  std::unordered_map<std::string, VkQueryPool> m_profileQueryPools; ///< Profiling query pools
  std::vector<ProfileResult> m_profileResults;           ///< Profiling results
  float m_timestampPeriod = 1.0f;                       ///< Timestamp period
  bool m_pipelineStateDirty = true;                     ///< Pipeline state needs update
  bool m_viewportStateDirty = true;                     ///< Viewport state needs update
  bool m_renderTargetStateDirty = true;                 ///< Render target state needs update
  mutable std::shared_mutex m_gpuMutex;                 ///< Mutex for thread safety
  mutable GPUStats m_stats;                             ///< GPU statistics
};

} // namespace ps4