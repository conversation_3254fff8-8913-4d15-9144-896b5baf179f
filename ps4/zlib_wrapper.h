// Copyright 2025 <Copyright Owner>

#pragma once

#include <cstdint>
#include <shared_mutex>
#include <stdexcept>
#include <string>
#include <vector>

namespace ps4 {

/**
 * @brief Exception for zlib-related errors.
 */
struct ZlibException : std::runtime_error {
  explicit ZlibException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief Compression levels for zlib, mapping to zlib constants.
 */
enum class CompressionLevel : int {
  NO_COMPRESSION = 0,      ///< No compression (zlib level 0)
  BEST_SPEED = 1,          ///< Fastest compression (zlib level 1)
  BEST_COMPRESSION = 9,    ///< Maximum compression (zlib level 9)
  DEFAULT_COMPRESSION = -1 ///< Default compression (zlib level -1)
};

/**
 * @brief Compression strategies for zlib, mapping to zlib strategies.
 */
enum class CompressionStrategy : int {
  DEFAULT = 0,      ///< Default strategy (Z_DEFAULT_STRATEGY)
  FILTERED = 1,     ///< Filtered data (Z_FILTERED)
  HUFFMAN_ONLY = 2, ///< Huffman-only compression (Z_HUFFMAN_ONLY)
  RLE = 3,          ///< Run-length encoding (Z_RLE)
  FIXED = 4         ///< Fixed Huffman codes (Z_FIXED)
};

/**
 * @brief Wraps zlib compression/decompression functionality for PS4 emulation.
 * @details Provides thread-safe in-memory and file-based
 * compression/decompression, with metrics for performance and cache efficiency.
 * Serialization includes versioning for state persistence.
 */
class ZlibWrapper {
public:
  /**
   * @brief Constructs the zlib wrapper.
   * @details Initializes internal state and metrics. Thread-safe.
   */
  ZlibWrapper();

  /**
   * @brief Destructor, cleaning up resources.
   * @details Thread-safe and noexcept.
   */
  ~ZlibWrapper() noexcept;

  /**
   * @brief Compresses data in memory.
   * @param input Input data to compress.
   * @param output Output compressed data.
   * @param level Compression level (default: DEFAULT_COMPRESSION).
   * @param strategy Compression strategy (default: DEFAULT).
   * @return True on success, false on failure (check GetLastError()).
   * @throws ZlibException on invalid input or zlib errors.
   * @details Thread-safe. Updates metrics and error state.
   */
  bool Compress(const std::vector<uint8_t> &input, std::vector<uint8_t> &output,
                CompressionLevel level = CompressionLevel::DEFAULT_COMPRESSION,
                CompressionStrategy strategy = CompressionStrategy::DEFAULT);

  /**
   * @brief Decompresses data in memory.
   * @param input Input compressed data.
   * @param output Output decompressed data.
   * @param expectedSize Expected output size (0 for unknown).
   * @return True on success, false on failure (check GetLastError()).
   * @throws ZlibException on invalid input or zlib errors.
   * @details Thread-safe. Updates metrics and error state.
   */
  bool Decompress(const std::vector<uint8_t> &input,
                  std::vector<uint8_t> &output, size_t expectedSize = 0);

  /**
   * @brief Compresses a file.
   * @param inputPath Input file path.
   * @param outputPath Output file path.
   * @param level Compression level (default: DEFAULT_COMPRESSION).
   * @param strategy Compression strategy (default: DEFAULT).
   * @return True on success, false on failure (check GetLastError()).
   * @throws ZlibException on file I/O or zlib errors.
   * @details Thread-safe. Creates directories as needed. Updates metrics.
   */
  bool
  CompressFile(const std::string &inputPath, const std::string &outputPath,
               CompressionLevel level = CompressionLevel::DEFAULT_COMPRESSION,
               CompressionStrategy strategy = CompressionStrategy::DEFAULT);

  /**
   * @brief Decompresses a file.
   * @param inputPath Input file path.
   * @param outputPath Output file path.
   * @return True on success, false on failure (check GetLastError()).
   * @throws ZlibException on file I/O or zlib errors.
   * @details Thread-safe. Creates directories as needed. Updates metrics.
   */
  bool DecompressFile(const std::string &inputPath,
                      const std::string &outputPath);

  /**
   * @brief Gets the last error message.
   * @return Last error message (empty if no error).
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  std::string GetLastError() const;

  /**
   * @brief Gets the compression ratio (compressed/uncompressed size).
   * @return Compression ratio (0.0f if no data processed).
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  float GetCompressionRatio() const;

  /**
   * @brief Gets the compressed data size.
   * @return Compressed size in bytes.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  size_t GetCompressedSize() const;

  /**
   * @brief Gets the uncompressed data size.
   * @return Uncompressed size in bytes.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  size_t GetUncompressedSize() const;

  /**
   * @brief Statistics for zlib operations.
   */
  struct Stats {
    uint64_t operationCount = 0; ///< Total compression/decompression operations
    uint64_t totalLatencyUs = 0; ///< Total latency in microseconds
    uint64_t cacheHits = 0;      ///< Cache hits for read operations
    uint64_t cacheMisses = 0;    ///< Cache misses for read operations
    uint64_t errorCount = 0;     ///< Total errors encountered
  };

  /**
   * @brief Retrieves zlib wrapper statistics.
   * @return Current statistics.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  Stats GetStats() const;

  /**
   * @brief Saves the zlib wrapper state to a stream.
   * @param out Output stream.
   * @details Thread-safe (read-only). Serializes with versioning (version 1).
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the zlib wrapper state from a stream.
   * @param in Input stream.
   * @details Thread-safe. Expects version 1 serialization format.
   */
  void LoadState(std::istream &in);

private:
  /**
   * @brief Sets the last error message.
   * @param error Error message.
   * @details Thread-safe. Updates error count in stats.
   */
  void SetError(const std::string &error);

  /**
   * @brief Updates compression statistics.
   * @param inputSize Input data size.
   * @param outputSize Output data size.
   * @details Thread-safe. Updates compression ratio and sizes.
   */
  void UpdateCompressionStats(size_t inputSize, size_t outputSize);

  /**
   * @brief Simple decompression when expected size is known.
   * @param input Input compressed data.
   * @param output Output decompressed data.
   * @param expectedSize Expected output size.
   * @return True on success, false otherwise.
   * @details Assumes mutex is already held by caller.
   */
  bool DecompressSimple(const std::vector<uint8_t> &input,
                        std::vector<uint8_t> &output, size_t expectedSize);

  /**
   * @brief Chunked decompression for unknown output sizes.
   * @param input Input compressed data.
   * @param output Output decompressed data.
   * @return True on success, false otherwise.
   * @details Assumes mutex is already held by caller.
   */
  bool DecompressChunked(const std::vector<uint8_t> &input,
                         std::vector<uint8_t> &output);

  std::string m_lastError;               ///< Last error message
  size_t m_compressedSize;               ///< Compressed data size
  size_t m_uncompressedSize;             ///< Uncompressed data size
  float m_compressionRatio;              ///< Compression ratio
  mutable Stats m_stats;                 ///< Operation statistics
  mutable std::shared_mutex m_zlibMutex; ///< Mutex for thread safety
};

// PS4 API functions
extern "C" {
/**
 * @brief Decompresses data using zlib (PS4 API).
 * @param dst Destination buffer.
 * @param dstSize Destination size (updated with actual size).
 * @param src Source buffer.
 * @param srcSize Source size.
 * @return 0 on success, -1 on failure.
 * @details Thread-safe. Calls ZlibWrapper::Decompress internally.
 */
int sceZlibDecompress(void *dst, size_t *dstSize, const void *src,
                      size_t srcSize);

/**
 * @brief Compresses data using zlib (PS4 API).
 * @param dst Destination buffer.
 * @param dstSize Destination size (updated with actual size).
 * @param src Source buffer.
 * @param srcSize Source size.
 * @param level Compression level (0-9, -1 for default).
 * @return 0 on success, -1 on failure.
 * @details Thread-safe. Calls ZlibWrapper::Compress internally.
 */
int sceZlibCompress(void *dst, size_t *dstSize, const void *src, size_t srcSize,
                    int level);
}

} // namespace ps4