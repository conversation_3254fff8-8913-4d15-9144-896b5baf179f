#pragma once

#include <cstdint>
#include <stdexcept>
#include <string>
#include <vector>

namespace ps4 {

/**
 * @brief Exception for audio-related errors.
 */
struct AudioException : std::runtime_error {
  explicit AudioException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief Enhanced audio format support for PS4.
 */
enum class PS4AudioFormat {
  PCM_S16 = 0, ///< 16-bit signed PCM
  PCM_S24 = 1, ///< 24-bit signed PCM
  PCM_S32 = 2, ///< 32-bit signed PCM
  PCM_F32 = 3, ///< 32-bit float PCM
  ADPCM = 4,   ///< ADPCM compressed
  AT9 = 5,     ///< ATRAC9 compressed
  MP3 = 6,     ///< MP3 compressed
  AAC = 7,     ///< AAC compressed
  OPUS = 8     ///< Opus compressed
};

/**
 * @brief Audio3D processing modes.
 */
enum class Audio3DMode {
  DISABLED = 0, ///< No 3D processing
  HRTF = 1,     ///< Head-Related Transfer Function
  SURROUND = 2, ///< Surround sound processing
  BINAURAL = 3  ///< Binaural processing
};

/**
 * @brief DSP effect types.
 */
enum class DSPEffectType {
  REVERB = 0,     ///< Reverb effect
  DELAY = 1,      ///< Delay effect
  CHORUS = 2,     ///< Chorus effect
  COMPRESSOR = 3, ///< Dynamic range compressor
  EQUALIZER = 4,  ///< Multi-band equalizer
  LIMITER = 5     ///< Audio limiter
};

/**
 * @brief Enhanced audio buffer structure.
 */
struct EnhancedAudioBuffer {
  std::vector<float> data;
  PS4AudioFormat format;
  uint32_t sampleRate;
  uint32_t channels;
  uint64_t timestamp;
  bool is3D;
  float position[3]; ///< 3D position (x, y, z)
  float velocity[3]; ///< 3D velocity
  uint32_t priority;
  bool looping;
};

/**
 * @brief DSP effect parameters.
 */
struct DSPEffectParams {
  DSPEffectType type;
  float intensity;
  float frequency;
  float feedback;
  bool enabled;
};

} // namespace ps4