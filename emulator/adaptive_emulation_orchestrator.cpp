// Copyright 2025 xAI
#define NOMINMAX
#include "adaptive_emulation_orchestrator.h"
#include "../debug/vector_debug.h"
#include <algorithm>
#include <chrono>
#include <cstdint>
#include <mutex>
#include <string>

// Simple logging replacement
namespace spdlog {
template <typename... Args> static void info(const char *fmt, Args &&...args) {}
template <typename... Args> static void warn(const char *fmt, Args &&...args) {}
template <typename... Args>
static void error(const char *fmt, Args &&...args) {}
template <typename... Args>
static void debug(const char *fmt, Args &&...args) {}
template <typename... Args>
static void trace(const char *fmt, Args &&...args) {}
template <typename... Args>
static void critical(const char *fmt, Args &&...args) {}
} // namespace spdlog

// Simple clamp function for C++14 compatibility
template <typename T>
constexpr const T &clamp(const T &v, const T &lo, const T &hi) {
  return (v < lo) ? lo : (hi < v) ? hi : v;
}

#include "../memory/memory_diagnostics.h"
#include "../common/lock_ordering.h"

namespace x86_64 {
/**
 * @brief Constructs an AdaptiveEmulationOrchestrator instance.
 * @details Initializes performance metrics and starts monitoring the emulator.
 * @param emulator Reference to the PS4Emulator instance.
 */
AdaptiveEmulationOrchestrator::AdaptiveEmulationOrchestrator(
    ps4::PS4Emulator &emulator)
    : m_emulatorCore(emulator) {
  CollectPerformanceMetrics();
  spdlog::info("AdaptiveEmulationOrchestrator initialized");
}

/**
 * @brief Enhanced optimization cycle with robust metric validation and adaptive
 * tuning.
 */
void AdaptiveEmulationOrchestrator::Cycle() {
  COMPONENT_LOCK(m_mutex, "OrchestratorMutex");
  try {
    // Collect and validate performance metrics
    CollectPerformanceMetrics();
    PopulateMemoryDiagnostics();
    CalculateDerivedMetrics();

    if (!ValidateMetrics()) {
      spdlog::warn("Metric validation failed, skipping tuning cycle");
      // CRITICAL: Safe map access for stats
      if (m_stats.find("validation_failures") != m_stats.end()) {
        m_stats["validation_failures"]++;
      } else {
        m_stats["validation_failures"] = 1;
      }
      return;
    }

    // Update metric history for stability analysis
    UpdateTuningHistory();

    // Check if we're in cooldown period
    uint64_t currentTime =
        std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch())
            .count();

    if (currentTime - m_lastTuningTime < m_tuningCooldownMs) {
      spdlog::debug("Tuning cooldown active, skipping adjustments");
      // CRITICAL: Safe map access for stats
      if (m_stats.find("cooldown_skips") != m_stats.end()) {
        m_stats["cooldown_skips"]++;
      } else {
        m_stats["cooldown_skips"] = 1;
      }
      return;
    }

    // Apply adaptive tuning based on metric stability
    if (m_adaptiveTuningEnabled) {
      ApplyAdaptiveTuning();
    } else {
      // Fallback to original tuning logic
      TuneJITCompiler();
      TuneMemoryHierarchy();
      TuneFiberManager();
      AdjustThresholds();
    }

    m_lastTuningTime = currentTime;
    // CRITICAL: Safe map access for stats
    if (m_stats.find("cycles") != m_stats.end()) {
      m_stats["cycles"]++;
    } else {
      m_stats["cycles"] = 1;
    }

    spdlog::trace(
        "Enhanced optimization cycle: cpu={:.2f}, memory={:.2f}, jit={:.2f}, "
        "fibers={:.2f}, interrupts={:.2f}, cache_hit={:.2f}, simd={:.2f}, "
        "memory_compression={:.2f}, stability_score={:.2f}",
        // CRITICAL: Safe map access for performance metrics
        (m_performanceMetrics.find("cpu_usage") != m_performanceMetrics.end() ? m_performanceMetrics["cpu_usage"] : 0.0f),
        (m_performanceMetrics.find("memory_bandwidth") != m_performanceMetrics.end() ? m_performanceMetrics["memory_bandwidth"] : 0.0f),
        (m_performanceMetrics.find("jit_cache_usage") != m_performanceMetrics.end() ? m_performanceMetrics["jit_cache_usage"] : 0.0f),
        (m_performanceMetrics.find("fiber_load") != m_performanceMetrics.end() ? m_performanceMetrics["fiber_load"] : 0.0f),
        (m_performanceMetrics.find("interrupt_rate") != m_performanceMetrics.end() ? m_performanceMetrics["interrupt_rate"] : 0.0f),
        (m_performanceMetrics.find("cache_hit_ratio") != m_performanceMetrics.end() ? m_performanceMetrics["cache_hit_ratio"] : 0.0f),
        (m_performanceMetrics.find("simd_instruction_ratio") != m_performanceMetrics.end() ? m_performanceMetrics["simd_instruction_ratio"] : 0.0f),
        (m_performanceMetrics.find("memory_compression_ratio") != m_performanceMetrics.end() ? m_performanceMetrics["memory_compression_ratio"] : 0.0f),
        (m_performanceMetrics.find("stability_score") != m_performanceMetrics.end() ? m_performanceMetrics["stability_score"] : 0.0f));
  } catch (const std::exception &e) {
    spdlog::error("Enhanced optimization cycle failed: {}", e.what());
    m_stats["cycle_errors"]++;
  }
}

/**
 * @brief Collects performance metrics from emulator components.
 * @details Gathers CPU, memory, JIT, fiber, APIC, cache, and SIMD metrics.
 */
void AdaptiveEmulationOrchestrator::CollectPerformanceMetrics() {
  auto &emulator = m_emulatorCore;
  size_t cpu_count = emulator.GetCPUCount();
  if (cpu_count == 0) {
    spdlog::warn("No CPUs available for metric collection");
    return;
  }

  float total_cpu_usage = 0.0f;
  float total_memory_bandwidth = 0.0f;
  float total_jit_cache_usage = 0.0f;
  float total_interrupt_rate = 0.0f;
  float total_apic_latency = 0.0f;
  float total_pipeline_stalls = 0.0f;
  float total_data_hazard_stalls = 0.0f;
  float total_memory_stalls = 0.0f;
  float total_branch_misprediction_rate = 0.0f;
  float total_cache_hit_ratio = 0.0f;
  float total_simd_instructions = 0.0f;

  for (size_t i = 0; i < cpu_count; ++i) {
    // CRITICAL: Bounds check for CPU access
    if (i >= emulator.GetCPUCount()) {
      spdlog::error("CPU index {} out of bounds", i);
      break;
    }
    auto &cpu = emulator.GetCPU(i);
    total_cpu_usage += static_cast<float>(cpu.GetUtilization());
    auto &memory = emulator.GetMMU();
    total_memory_bandwidth += static_cast<float>(memory.GetBandwidthUsage());
    auto &jit = cpu.GetJITCompiler();
    total_jit_cache_usage += static_cast<float>(jit.GetCacheUsage());
    auto &apic = cpu.GetAPIC();

    // Pipeline stats
    auto ps = cpu.GetPipeline().GetStats();
    float stalls = static_cast<float>(ps.stalls);
    float cycles = static_cast<float>(ps.cycles);
    // CRITICAL: Division by zero check
    float ratio = cycles > 0 ? stalls / cycles : 0.0f;
    total_pipeline_stalls += clamp(ratio, 0.0f, 1.0f);
    // CRITICAL: Division by zero checks
    total_data_hazard_stalls +=
        stalls > 0 ? static_cast<float>(ps.data_hazard_stalls) / stalls : 0.0f;
    total_memory_stalls +=
        stalls > 0 ? static_cast<float>(ps.memory_stalls) / stalls : 0.0f;
    float branch_total =
        static_cast<float>(ps.branch_hits + ps.branch_mispredictions);
    total_branch_misprediction_rate +=
        branch_total > 0
            ? static_cast<float>(ps.branch_mispredictions) / branch_total
            : 0.0f;

    // APIC stats
    auto ap = apic.GetStats();
    float interrupts = static_cast<float>(ap.operationCount);
    float accesses = static_cast<float>(ap.operationCount);
    float denom = accesses > 0 ? accesses : 1.0f;
    total_interrupt_rate += clamp(interrupts / denom, 0.0f, 1.0f);
    total_apic_latency +=
        ap.operationCount > 0
            ? static_cast<float>(ap.totalLatencyUs) / ap.operationCount
            : 0.0f;

    // Cache stats
    auto cache_stats = cpu.GetMemory().GetStats();
    float cache_total =
        static_cast<float>(cache_stats.hits + cache_stats.misses);
    total_cache_hit_ratio +=
        cache_total > 0 ? static_cast<float>(cache_stats.hits) / cache_total
                        : 0.0f;

    // SIMD instructions from pipeline stats
    total_simd_instructions +=
        ps.instructionsExecuted > 0
            ? static_cast<float>(ps.simd_instructions) /
                  static_cast<float>(ps.instructionsExecuted)
            : 0.0f;
  }

  // CRITICAL: Division by zero check for cpu_count
  float cpu_count_f = cpu_count > 0 ? static_cast<float>(cpu_count) : 1.0f;
  m_performanceMetrics["cpu_usage"] =
      clamp(total_cpu_usage / cpu_count_f, 0.0f, 1.0f);
  m_performanceMetrics["memory_bandwidth"] =
      clamp(total_memory_bandwidth / cpu_count_f, 0.0f, 1.0f);
  m_performanceMetrics["jit_cache_usage"] =
      clamp(total_jit_cache_usage / cpu_count_f, 0.0f, 1.0f);
  m_performanceMetrics["pipeline_stalls"] =
      clamp(total_pipeline_stalls / cpu_count_f, 0.0f, 1.0f);
  m_performanceMetrics["data_hazard_stalls"] =
      clamp(total_data_hazard_stalls / cpu_count_f, 0.0f, 1.0f);
  m_performanceMetrics["memory_stalls"] =
      clamp(total_memory_stalls / cpu_count_f, 0.0f, 1.0f);
  m_performanceMetrics["branch_misprediction_rate"] =
      clamp(total_branch_misprediction_rate / cpu_count_f, 0.0f, 1.0f);
  m_performanceMetrics["interrupt_rate"] =
      clamp(total_interrupt_rate / cpu_count_f, 0.0f, 1.0f);
  m_performanceMetrics["apic_latency_us"] = total_apic_latency / cpu_count_f;
  m_performanceMetrics["cache_hit_ratio"] =
      clamp(total_cache_hit_ratio / cpu_count_f, 0.0f, 1.0f);
  m_performanceMetrics["simd_instruction_ratio"] =
      clamp(total_simd_instructions / cpu_count_f, 0.0f, 1.0f);

  // Memory prefetcher stats
  auto &memory = emulator.GetMMU();
  if (memory.GetPrefetchHint() != ps4::PrefetchHint::PREFETCH_NONE) {
    auto prefetchStats = memory.GetPrefetcherStats();
    float hitRate = prefetchStats.hitRate;
    m_performanceMetrics["prefetch_hit_rate"] = hitRate;
    m_performanceMetrics["prefetch_requests"] =
        static_cast<float>(prefetchStats.prefetchRequests);
    m_performanceMetrics["prefetch_hits"] =
        static_cast<float>(prefetchStats.prefetchHits);
    m_performanceMetrics["sequential_patterns"] =
        static_cast<float>(prefetchStats.sequentialPatterns);
    m_performanceMetrics["stride_patterns"] =
        static_cast<float>(prefetchStats.stridePatterns);
    spdlog::debug("Prefetcher stats: hit_rate={:.2f}, requests={}, hits={}, "
                  "sequential={}, stride={}",
                  hitRate, prefetchStats.prefetchRequests,
                  prefetchStats.prefetchHits, prefetchStats.sequentialPatterns,
                  prefetchStats.stridePatterns);
  }

  // Fiber stats
  auto &fiberManager = emulator.GetFiberManager();
  m_performanceMetrics["fiber_load"] = clamp(
      static_cast<float>(fiberManager.GetFiberCount()) / 100.0f, 0.0f, 1.0f);
  uint64_t totalExecutionTimeUs = 0;
  for (uint64_t i = 1; i <= fiberManager.GetFiberCount(); ++i) {
    std::string name;
    ps4::FiberState state;
    uint8_t priority;
    uint64_t executionTimeUs;
    uint32_t switchCount;
    if (fiberManager.GetFiberInfo(i, name, state, priority, executionTimeUs,
                                  switchCount)) {
      totalExecutionTimeUs += executionTimeUs;
    }
  }
  m_performanceMetrics["fiber_execution_time_us"] =
      static_cast<float>(totalExecutionTimeUs) / 1000.0f;

  // Memory diagnostics
  ps4::MemoryDiagnostics &diag = ps4::MemoryDiagnostics::GetInstance();
  auto usage = diag.GetUsageStats();
  m_performanceMetrics["memory_totalAllocated"] =
      static_cast<float>(usage.totalAllocated);
  m_performanceMetrics["memory_totalFreed"] =
      static_cast<float>(usage.totalFreed);
  m_performanceMetrics["memory_currentAllocated"] =
      static_cast<float>(usage.currentAllocated);
  m_performanceMetrics["memory_peakAllocated"] =
      static_cast<float>(usage.peakAllocated);
  m_performanceMetrics["memory_allocationCount"] =
      static_cast<float>(usage.allocationCount);
  m_performanceMetrics["memory_freeCount"] =
      static_cast<float>(usage.freeCount);
}

/**
 * @brief Enhanced JIT compiler tuning with stability analysis and adaptive
 * optimization.
 */
void AdaptiveEmulationOrchestrator::TuneJITCompiler() {
  float jitUsage = m_performanceMetrics["jit_cache_usage"];
  float pipelineStalls = m_performanceMetrics["pipeline_stalls"];
  float branchMispredictionRate =
      m_performanceMetrics["branch_misprediction_rate"];
  float simdRatio = m_performanceMetrics["simd_instruction_ratio"];
  float cpuUsage = m_performanceMetrics["cpu_usage"];

  // Check metric stability before making changes
  bool jitStable = IsMetricStable("jit_cache_usage");
  bool stallsStable = IsMetricStable("pipeline_stalls");

  for (size_t i = 0; i < m_emulatorCore.GetCPUCount(); ++i) {
    auto &jit = m_emulatorCore.GetCPU(i).GetJITCompiler();
    try {
      auto jitStats = jit.GetStats();
      float cacheHitRatio =
          (jitStats.cacheHits + jitStats.cacheMisses > 0)
              ? static_cast<float>(jitStats.cacheHits) /
                    (jitStats.cacheHits + jitStats.cacheMisses)
              : 0.0f;

      // Enhanced JIT tuning logic
      bool shouldClearCache = false;
      std::string reason;

      // Primary condition: high usage with stable metrics
      if (jitUsage > m_jitThreshold && jitStable) {
        shouldClearCache = true;
        reason = "high_usage_stable";
      }
      // Secondary condition: low cache hit ratio with high stalls
      else if (cacheHitRatio < 0.6f && pipelineStalls > 0.3f && stallsStable) {
        shouldClearCache = true;
        reason = "low_hit_ratio_high_stalls";
      }
      // Tertiary condition: excessive branch mispredictions
      else if (branchMispredictionRate > 0.2f && jitUsage > 0.7f) {
        shouldClearCache = true;
        reason = "high_branch_mispredictions";
      }

      if (shouldClearCache) {
        // Record tuning action
        TuningAction action;
        action.component = "JIT";
        action.parameter = "cache_clear";
        action.oldValue = jitUsage;
        action.newValue = 0.0f;
        action.timestamp =
            std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now().time_since_epoch())
                .count();

        jit.ClearCache();
        m_stats["jit_clears"]++;
        m_performanceMetrics["jit_clear_count"] =
            static_cast<float>(m_stats["jit_clears"]);

        action.successful = true;
        m_tuningHistory.push_back(action);

        spdlog::info(
            "CPU {}: Cleared JIT cache ({}): usage={:.2f}, threshold={:.2f}, "
            "hit_ratio={:.2f}, stalls={:.2f}, branch_mispredict={:.2f}",
            i, reason, jitUsage, m_jitThreshold, cacheHitRatio, pipelineStalls,
            branchMispredictionRate);
      }

      if (pipelineStalls > 0.5f || branchMispredictionRate > 0.3f) {
        jit.EnableTieredCompilation(true);
        spdlog::debug("CPU {}: Enabled tiered JIT compilation: stalls={:.2f}, "
                      "misprediction_rate={:.2f}",
                      i, pipelineStalls, branchMispredictionRate);
      } else if (pipelineStalls < 0.2f && branchMispredictionRate < 0.1f) {
        jit.EnableTieredCompilation(false);
        spdlog::debug("CPU {}: Disabled tiered JIT compilation: stalls={:.2f}, "
                      "misprediction_rate={:.2f}",
                      i, pipelineStalls, branchMispredictionRate);
      }

      if (simdRatio > 0.3f) {
        jit.EnableSIMDOptimizations(true);
        spdlog::debug("CPU {}: Enabled SIMD optimizations: ratio={:.2f}", i,
                      simdRatio);
      } else if (simdRatio < 0.1f) {
        jit.EnableSIMDOptimizations(false);
        spdlog::debug("CPU {}: Disabled SIMD optimizations: ratio={:.2f}", i,
                      simdRatio);
      }
    } catch (const std::exception &e) {
      spdlog::error("CPU {}: JIT tuning failed: {}", i, e.what());
    }
  }
}

/**
 * @brief Tunes the memory hierarchy based on performance metrics.
 * @details Adjusts prefetching, memory protections, and compression based on
 * bandwidth and hit rates.
 */
void AdaptiveEmulationOrchestrator::TuneMemoryHierarchy() {
  auto &memory = m_emulatorCore.GetMMU();
  float bandwidth = m_performanceMetrics["memory_bandwidth"];
  float cacheHitRatio = m_performanceMetrics["cache_hit_ratio"];

  try {
    if (bandwidth > 0.85f) {
      memory.SetPrefetchHint(ps4::PrefetchHint::PREFETCH_AGGRESSIVE);
      spdlog::debug(
          "Set aggressive prefetch: bandwidth={:.2f}, cache_hit={:.2f}",
          bandwidth, cacheHitRatio);
    } else if (bandwidth > 0.65f) {
      memory.SetPrefetchHint(ps4::PrefetchHint::PREFETCH_MODERATE);
      spdlog::debug("Set moderate prefetch: bandwidth={:.2f}, cache_hit={:.2f}",
                    bandwidth, cacheHitRatio);
    } else if (bandwidth > 0.45f) {
      memory.SetPrefetchHint(ps4::PrefetchHint::PREFETCH_CONSERVATIVE);
      spdlog::debug(
          "Set conservative prefetch: bandwidth={:.2f}, cache_hit={:.2f}",
          bandwidth, cacheHitRatio);
    } else {
      memory.SetPrefetchHint(ps4::PrefetchHint::PREFETCH_NONE);
      spdlog::debug("Disabled prefetch: bandwidth={:.2f}, cache_hit={:.2f}",
                    bandwidth, cacheHitRatio);
    }

    if (memory.GetPrefetchHint() != ps4::PrefetchHint::PREFETCH_NONE &&
        m_performanceMetrics.find("prefetch_hit_rate") !=
            m_performanceMetrics.end()) {
      float hitRate = m_performanceMetrics["prefetch_hit_rate"];
      if (hitRate < 0.25f &&
          memory.GetPrefetchHint() == ps4::PrefetchHint::PREFETCH_AGGRESSIVE) {
        memory.SetPrefetchHint(ps4::PrefetchHint::PREFETCH_MODERATE);
        spdlog::info("Downgraded to moderate prefetch: hit_rate={:.2f}",
                     hitRate);
      } else if (hitRate > 0.75f &&
                 memory.GetPrefetchHint() ==
                     ps4::PrefetchHint::PREFETCH_CONSERVATIVE) {
        memory.SetPrefetchHint(ps4::PrefetchHint::PREFETCH_MODERATE);
        spdlog::info("Upgraded to moderate prefetch: hit_rate={:.2f}", hitRate);
      }
    }

    if (bandwidth > m_memoryThreshold) {
      uint64_t addr = 0x400000;
      memory.ProtectMemory(addr, 1024 * 1024, ps4::PROT_READ | ps4::PROT_WRITE,
                           0);
      for (size_t i = 0; i < m_emulatorCore.GetCPUCount(); ++i) {
        m_emulatorCore.GetCPU(i).InvalidateTLB(addr);
      }
      m_stats["memory_tunes"]++;
      spdlog::info("Adjusted memory protections at 0x{:x}: bandwidth={:.2f}",
                   addr, bandwidth);
    }

    // Use memory diagnostics for compression tuning
    if (m_performanceMetrics.find("memory_compression_ratio") !=
        m_performanceMetrics.end()) {
      float compressionRatio = m_performanceMetrics["memory_compression_ratio"];
      if (compressionRatio < 0.5f && bandwidth > 0.7f) {
        memory.EnableCompression(false);
        spdlog::info(
            "Disabled memory compression: ratio={:.2f}, bandwidth={:.2f}",
            compressionRatio, bandwidth);
      } else if (compressionRatio > 0.8f && bandwidth < 0.5f) {
        memory.EnableCompression(true);
        spdlog::info(
            "Enabled memory compression: ratio={:.2f}, bandwidth={:.2f}",
            compressionRatio, bandwidth);
      }
    }
  } catch (const std::exception &e) {
    spdlog::error("Memory hierarchy tuning failed: {}", e.what());
  }
}

/**
 * @brief Tunes the fiber manager based on performance metrics.
 * @details Adjusts scheduling policy and fiber priorities based on load and
 * execution time.
 */
void AdaptiveEmulationOrchestrator::TuneFiberManager() {
  auto &fiberManager = m_emulatorCore.GetFiberManager();
  float fiberLoad = m_performanceMetrics["fiber_load"];
  float executionTime = m_performanceMetrics["fiber_execution_time_us"];

  try {
    if (fiberLoad > m_fiberThreshold || executionTime > 1000.0f) {
      fiberManager.SetSchedulingPolicy(ps4::SchedulingPolicy::ROUND_ROBIN);
      m_stats["fiber_tunes"]++;
      spdlog::info(
          "Switched to ROUND_ROBIN scheduling: load={:.2f}, execution={:.2f}ms",
          fiberLoad, executionTime);
    } else if (fiberLoad < m_fiberThreshold * 0.8f && executionTime < 500.0f) {
      fiberManager.SetSchedulingPolicy(ps4::SchedulingPolicy::PRIORITY);
      spdlog::debug(
          "Reverted to PRIORITY scheduling: load={:.2f}, execution={:.2f}ms",
          fiberLoad, executionTime);
    }

    // Adjust fiber priorities based on execution time
    for (uint64_t i = 1; i <= fiberManager.GetFiberCount(); ++i) {
      std::string name;
      ps4::FiberState state;
      uint8_t priority;
      uint64_t executionTimeUs;
      uint32_t switchCount;
      if (fiberManager.GetFiberInfo(i, name, state, priority, executionTimeUs,
                                    switchCount)) {
        if (executionTimeUs > 1000000 && priority < 200) {
          fiberManager.SetFiberPriority(i, priority + 10);
          spdlog::debug(
              "Increased priority of fiber {} to {}: execution={:.2f}ms", i,
              priority + 10, executionTimeUs / 1000.0f);
        } else if (executionTimeUs < 100000 && priority > 50) {
          fiberManager.SetFiberPriority(i, priority - 10);
          spdlog::debug(
              "Decreased priority of fiber {} to {}: execution={:.2f}ms", i,
              priority - 10, executionTimeUs / 1000.0f);
        }
      }
    }
  } catch (const std::exception &e) {
    spdlog::error("Fiber manager tuning failed: {}", e.what());
  }
}

/**
 * @brief Adjusts optimization thresholds based on performance metrics.
 * @details Dynamically tunes JIT, memory, and fiber thresholds to balance
 * performance.
 */
void AdaptiveEmulationOrchestrator::AdjustThresholds() {
  try {
    if (m_performanceMetrics["cpu_usage"] > 0.9f &&
        m_jitThreshold > m_minJitThreshold) {
      m_jitThreshold -= 0.05f;
      spdlog::debug("Lowered JIT threshold to {:.2f}: high CPU usage",
                    m_jitThreshold);
    }
    if (m_performanceMetrics["memory_bandwidth"] > 0.9f &&
        m_memoryThreshold > m_minMemoryThreshold) {
      m_memoryThreshold -= 0.05f;
      spdlog::debug("Lowered memory threshold to {:.2f}: high bandwidth",
                    m_memoryThreshold);
    }
    if (m_performanceMetrics["fiber_load"] > 0.8f &&
        m_fiberThreshold > m_minFiberThreshold) {
      m_fiberThreshold -= 0.05f;
      spdlog::debug("Lowered fiber threshold to {:.2f}: high fiber load",
                    m_fiberThreshold);
    }

    if (m_performanceMetrics["cpu_usage"] < 0.6f && m_jitThreshold < 0.9f) {
      m_jitThreshold = std::min(m_jitThreshold + 0.02f, 0.9f);
      spdlog::debug("Raised JIT threshold to {:.2f}: low CPU usage",
                    m_jitThreshold);
    }
    if (m_performanceMetrics["memory_bandwidth"] < 0.6f &&
        m_memoryThreshold < 0.8f) {
      m_memoryThreshold = std::min(m_memoryThreshold + 0.02f, 0.8f);
      spdlog::debug("Raised memory threshold to {:.2f}: low bandwidth",
                    m_memoryThreshold);
    }
    if (m_performanceMetrics["fiber_load"] < 0.5f && m_fiberThreshold < 0.7f) {
      m_fiberThreshold = std::min(m_fiberThreshold + 0.02f, 0.7f);
      spdlog::debug("Raised fiber threshold to {:.2f}: low fiber load",
                    m_fiberThreshold);
    }

    m_jitThreshold = std::max(m_jitThreshold, m_minJitThreshold);
    m_memoryThreshold = std::max(m_memoryThreshold, m_minMemoryThreshold);
    m_fiberThreshold = std::max(m_fiberThreshold, m_minFiberThreshold);
  } catch (const std::exception &e) {
    spdlog::error("Threshold adjustment failed: {}", e.what());
  }
}

/**
 * @brief Saves the orchestrator state to a stream.
 * @details Serializes performance metrics, statistics, and thresholds.
 * @param out The output stream.
 */
void AdaptiveEmulationOrchestrator::SaveState(std::ostream &out) const {
  COMPONENT_LOCK(m_mutex, "OrchestratorMutex");
  uint32_t version = 1;
  out.write(reinterpret_cast<const char *>(&version), sizeof(version));
  uint32_t metricCount = static_cast<uint32_t>(m_performanceMetrics.size());
  out.write(reinterpret_cast<const char *>(&metricCount), sizeof(metricCount));
  for (const auto &entry : m_performanceMetrics) {
    uint32_t keyLen = static_cast<uint32_t>(entry.first.size());
    out.write(reinterpret_cast<const char *>(&keyLen), sizeof(keyLen));
    out.write(entry.first.data(), keyLen);
    out.write(reinterpret_cast<const char *>(&entry.second),
              sizeof(entry.second));
  }
  uint32_t statCount = static_cast<uint32_t>(m_stats.size());
  out.write(reinterpret_cast<const char *>(&statCount), sizeof(statCount));
  for (const auto &entry : m_stats) {
    uint32_t keyLen = static_cast<uint32_t>(entry.first.size());
    out.write(reinterpret_cast<const char *>(&keyLen), sizeof(keyLen));
    out.write(entry.first.data(), keyLen);
    out.write(reinterpret_cast<const char *>(&entry.second),
              sizeof(entry.second));
  }
  out.write(reinterpret_cast<const char *>(&m_jitThreshold),
            sizeof(m_jitThreshold));
  out.write(reinterpret_cast<const char *>(&m_memoryThreshold),
            sizeof(m_memoryThreshold));
  out.write(reinterpret_cast<const char *>(&m_fiberThreshold),
            sizeof(m_fiberThreshold));
  spdlog::info("AdaptiveEmulationOrchestrator state saved");
}

/**
 * @brief Loads the orchestrator state from a stream.
 * @details Deserializes performance metrics, statistics, and thresholds.
 * @param in The input stream.
 */
void AdaptiveEmulationOrchestrator::LoadState(std::istream &in) {
  COMPONENT_LOCK(m_mutex, "OrchestratorMutex");
  uint32_t version;
  in.read(reinterpret_cast<char *>(&version), sizeof(version));
  if (version != 1) {
    spdlog::error("Unsupported orchestrator state version: {}", version);
    throw std::runtime_error("Invalid orchestrator state version");
  }
  m_performanceMetrics.clear();
  m_stats.clear();
  uint32_t metricCount;
  in.read(reinterpret_cast<char *>(&metricCount), sizeof(metricCount));
  for (uint32_t i = 0; i < metricCount && in.good(); ++i) {
    uint32_t keyLen;
    in.read(reinterpret_cast<char *>(&keyLen), sizeof(keyLen));
    std::string key(keyLen, '\0');
    in.read(const_cast<char *>(key.data()), keyLen);
    float value;
    in.read(reinterpret_cast<char *>(&value), sizeof(value));
    m_performanceMetrics[key] = value;
  }
  uint32_t statCount;
  in.read(reinterpret_cast<char *>(&statCount), sizeof(statCount));
  for (uint32_t i = 0; i < statCount && in.good(); ++i) {
    uint32_t keyLen;
    in.read(reinterpret_cast<char *>(&keyLen), sizeof(keyLen));
    std::string key(keyLen, '\0');
    in.read(const_cast<char *>(key.data()), keyLen);
    uint64_t value;
    in.read(reinterpret_cast<char *>(&value), sizeof(value));
    m_stats[key] = value;
  }
  in.read(reinterpret_cast<char *>(&m_jitThreshold), sizeof(m_jitThreshold));
  in.read(reinterpret_cast<char *>(&m_memoryThreshold),
          sizeof(m_memoryThreshold));
  in.read(reinterpret_cast<char *>(&m_fiberThreshold),
          sizeof(m_fiberThreshold));
  spdlog::info("AdaptiveEmulationOrchestrator state loaded");
}

/**
 * @brief Validates collected performance metrics for accuracy and completeness.
 */
bool AdaptiveEmulationOrchestrator::ValidateMetrics() {
  try {
    // Check for required metrics
    std::vector<std::string> requiredMetrics = {
        "cpu_usage",  "memory_bandwidth", "jit_cache_usage",
        "fiber_load", "cache_hit_ratio",  "memory_compression_ratio"};

    for (const auto &metric : requiredMetrics) {
      auto it = m_performanceMetrics.find(metric);
      if (it == m_performanceMetrics.end()) {
        spdlog::warn("Missing required metric: {}", metric);
        return false;
      }

      float value = it->second;
      // Check for invalid values
      if (std::isnan(value) || std::isinf(value)) {
        spdlog::warn("Invalid value for metric {}: {}", metric, value);
        return false;
      }

      // Check for reasonable ranges
      if (value < 0.0f || value > 10.0f) { // Allow some metrics to exceed 1.0
        spdlog::warn("Metric {} out of reasonable range: {}", metric, value);
        return false;
      }
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ValidateMetrics failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Populates memory diagnostics that may be missing.
 */
void AdaptiveEmulationOrchestrator::PopulateMemoryDiagnostics() {
  try {
    auto &memory = m_emulatorCore.GetMMU();

    // Get comprehensive memory statistics
    auto memStats = memory.GetStats();

    // Calculate compression ratio if not already set
    if (m_performanceMetrics.find("memory_compression_ratio") ==
            m_performanceMetrics.end() ||
        m_performanceMetrics["memory_compression_ratio"] == 0.0f) {

      float compressionRatio = 1.0f; // Default no compression
      if (memStats.compressedPages > 0 && memStats.totalPages > 0) {
        compressionRatio = static_cast<float>(memStats.compressedPages) /
                           static_cast<float>(memStats.totalPages);
      }
      m_performanceMetrics["memory_compression_ratio"] = compressionRatio;
    }

    // Calculate memory utilization
    if (memStats.totalPages > 0) {
      float utilization = static_cast<float>(memStats.usedPages) /
                          static_cast<float>(memStats.totalPages);
      m_performanceMetrics["memory_utilization"] = utilization;
    }

    // Calculate page fault rate
    static uint64_t lastPageFaults = 0;
    static auto lastTime = std::chrono::steady_clock::now();

    auto currentTime = std::chrono::steady_clock::now();
    auto timeDiff = std::chrono::duration_cast<std::chrono::milliseconds>(
                        currentTime - lastTime)
                        .count();

    if (timeDiff > 0) {
      uint64_t faultDiff = memStats.pageFaults - lastPageFaults;
      float faultRate = static_cast<float>(faultDiff) /
                        static_cast<float>(timeDiff) *
                        1000.0f; // faults per second
      m_performanceMetrics["page_fault_rate"] = faultRate;
    }

    lastPageFaults = memStats.pageFaults;
    lastTime = currentTime;

    // Update swap usage
    if (memStats.totalPages > 0) {
      float swapUsage = static_cast<float>(memStats.swappedPages) /
                        static_cast<float>(memStats.totalPages);
      m_performanceMetrics["swap_usage"] = swapUsage;
    }

    spdlog::debug(
        "Memory diagnostics populated: compression={:.3f}, utilization={:.3f}, "
        "page_fault_rate={:.1f}, swap_usage={:.3f}",
        m_performanceMetrics["memory_compression_ratio"],
        m_performanceMetrics["memory_utilization"],
        m_performanceMetrics["page_fault_rate"],
        m_performanceMetrics["swap_usage"]);

  } catch (const std::exception &e) {
    spdlog::error("PopulateMemoryDiagnostics failed: {}", e.what());
  }
}

/**
 * @brief Calculates derived metrics from base measurements.
 */
void AdaptiveEmulationOrchestrator::CalculateDerivedMetrics() {
  try {
    // Calculate overall system performance score
    float cpuScore =
        1.0f - clamp(m_performanceMetrics["cpu_usage"], 0.0f, 1.0f);
    float memoryScore =
        1.0f - clamp(m_performanceMetrics["memory_bandwidth"], 0.0f, 1.0f);
    float cacheScore =
        clamp(m_performanceMetrics["cache_hit_ratio"], 0.0f, 1.0f);
    float fiberScore =
        1.0f - clamp(m_performanceMetrics["fiber_load"], 0.0f, 1.0f);

    float overallScore = (cpuScore * 0.3f + memoryScore * 0.25f +
                          cacheScore * 0.25f + fiberScore * 0.2f);
    m_performanceMetrics["overall_performance_score"] = overallScore;

    // Calculate stability score based on metric variance
    float stabilityScore = 0.0f;
    size_t stableMetrics = 0;

    for (const auto &entry : m_metricHistory) {
      if (entry.second.stable) {
        stableMetrics++;
      }
    }

    if (!m_metricHistory.empty()) {
      stabilityScore = static_cast<float>(stableMetrics) /
                       static_cast<float>(m_metricHistory.size());
    }
    m_performanceMetrics["stability_score"] = stabilityScore;

    // Calculate efficiency metrics
    float jitEfficiency = 1.0f;
    if (m_performanceMetrics["jit_cache_usage"] > 0.0f) {
      jitEfficiency = clamp(m_performanceMetrics["cache_hit_ratio"] /
                                m_performanceMetrics["jit_cache_usage"],
                            0.0f, 2.0f);
    }
    m_performanceMetrics["jit_efficiency"] = jitEfficiency;

    // Calculate memory efficiency
    float memoryEfficiency = 1.0f;
    if (m_performanceMetrics["memory_utilization"] > 0.0f) {
      memoryEfficiency =
          (1.0f - m_performanceMetrics["page_fault_rate"] / 1000.0f) *
          (1.0f + m_performanceMetrics["memory_compression_ratio"]);
      memoryEfficiency = clamp(memoryEfficiency, 0.0f, 2.0f);
    }
    m_performanceMetrics["memory_efficiency"] = memoryEfficiency;

    spdlog::debug("Derived metrics: overall_score={:.3f}, stability={:.3f}, "
                  "jit_efficiency={:.3f}, memory_efficiency={:.3f}",
                  overallScore, stabilityScore, jitEfficiency,
                  memoryEfficiency);

  } catch (const std::exception &e) {
    spdlog::error("CalculateDerivedMetrics failed: {}", e.what());
  }
}

/**
 * @brief Updates metric history for trend analysis.
 */
void AdaptiveEmulationOrchestrator::UpdateTuningHistory() {
  try {
    uint64_t currentTime =
        std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch())
            .count();

    for (const auto &entry : m_performanceMetrics) {
      const std::string &name = entry.first;
      float value = entry.second;
      auto &history = m_metricHistory[name];

      // Add current value
      history.values.push_back(value);
      history.timestamps.push_back(currentTime);

      // Maintain window size
      if (history.values.size() > m_historyWindowSize) {
        history.values.erase(history.values.begin());
        history.timestamps.erase(history.timestamps.begin());
      }

      // Calculate statistics if we have enough data
      if (history.values.size() >= 3) {
        // Calculate average
        float sum = 0.0f;
        for (float val : history.values) {
          sum += val;
        }
        history.average = sum / static_cast<float>(history.values.size());

        // Calculate variance
        float varianceSum = 0.0f;
        for (float val : history.values) {
          float diff = val - history.average;
          varianceSum += diff * diff;
        }
        history.variance =
            varianceSum / static_cast<float>(history.values.size());

        // Calculate trend (simple linear regression slope)
        if (history.values.size() >= 5) {
          float n = static_cast<float>(history.values.size());
          float sumX = 0.0f, sumY = 0.0f, sumXY = 0.0f, sumX2 = 0.0f;

          for (size_t i = 0; i < history.values.size(); ++i) {
            float x = static_cast<float>(i);
            float y = history.values[i];
            sumX += x;
            sumY += y;
            sumXY += x * y;
            sumX2 += x * x;
          }

          float denominator = n * sumX2 - sumX * sumX;
          if (std::abs(denominator) > 1e-6f) {
            history.trend = (n * sumXY - sumX * sumY) / denominator;
          }
        }

        // Determine stability
        float coefficientOfVariation =
            (history.average > 0.0f)
                ? std::sqrt(history.variance) / history.average
                : 0.0f;
        history.stable = coefficientOfVariation < m_stabilityThreshold;
      }
    }

    // Cleanup old tuning actions
    auto cutoffTime =
        currentTime - (m_tuningCooldownMs * 10); // Keep 10x cooldown period
    m_tuningHistory.erase(
        std::remove_if(m_tuningHistory.begin(), m_tuningHistory.end(),
                       [cutoffTime](const TuningAction &action) {
                         return action.timestamp < cutoffTime;
                       }),
        m_tuningHistory.end());

  } catch (const std::exception &e) {
    spdlog::error("UpdateTuningHistory failed: {}", e.what());
  }
}

/**
 * @brief Checks if a metric is stable based on variance threshold.
 */
bool AdaptiveEmulationOrchestrator::IsMetricStable(
    const std::string &metricName, float threshold) {
  try {
    auto it = m_metricHistory.find(metricName);
    if (it == m_metricHistory.end()) {
      return false; // No history, consider unstable
    }

    const MetricHistory &history = it->second;
    if (history.values.size() < 3) {
      return false; // Not enough data
    }

    // Calculate coefficient of variation
    float coefficientOfVariation =
        (history.average > 0.0f) ? std::sqrt(history.variance) / history.average
                                 : 0.0f;

    return coefficientOfVariation < threshold;
  } catch (const std::exception &e) {
    spdlog::error("IsMetricStable failed for {}: {}", metricName, e.what());
    return false;
  }
}

/**
 * @brief Applies adaptive tuning based on metric stability and trends.
 */
void AdaptiveEmulationOrchestrator::ApplyAdaptiveTuning() {
  try {
    // Check overall system stability
    float stabilityScore = m_performanceMetrics["stability_score"];
    float overallScore = m_performanceMetrics["overall_performance_score"];

    if (stabilityScore < 0.7f) {
      spdlog::debug(
          "System unstable (stability={:.3f}), reverting recent tuning",
          stabilityScore);
      RevertUnstableTuning();
      return;
    }

    // Apply tuning based on performance score
    if (overallScore < 0.6f) {
      // Performance is poor, apply aggressive tuning
      spdlog::info("Poor performance detected (score={:.3f}), applying "
                   "aggressive tuning",
                   overallScore);
      TuneJITCompiler();
      TuneMemoryHierarchy();
      TuneFiberManager();
    } else if (overallScore > 0.8f) {
      // Performance is good, apply conservative tuning
      spdlog::debug(
          "Good performance (score={:.3f}), applying conservative tuning",
          overallScore);
      AdjustThresholds();
    } else {
      // Moderate performance, selective tuning
      if (IsMetricStable("jit_cache_usage") &&
          m_performanceMetrics["jit_cache_usage"] > m_jitThreshold) {
        TuneJITCompiler();
      }
      if (IsMetricStable("memory_bandwidth") &&
          m_performanceMetrics["memory_bandwidth"] > m_memoryThreshold) {
        TuneMemoryHierarchy();
      }
      if (IsMetricStable("fiber_load") &&
          m_performanceMetrics["fiber_load"] > m_fiberThreshold) {
        TuneFiberManager();
      }
    }

    m_stats["adaptive_tuning_cycles"]++;
  } catch (const std::exception &e) {
    spdlog::error("ApplyAdaptiveTuning failed: {}", e.what());
  }
}

/**
 * @brief Reverts recent tuning actions that may have caused instability.
 */
void AdaptiveEmulationOrchestrator::RevertUnstableTuning() {
  try {
    uint64_t currentTime =
        std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch())
            .count();

    // Look for recent tuning actions within the last cooldown period
    uint64_t revertWindow = m_tuningCooldownMs * 2; // Look back 2x cooldown
    size_t revertedActions = 0;

    for (auto it = m_tuningHistory.rbegin(); it != m_tuningHistory.rend();
         ++it) {
      TuningAction &action = *it;

      // Only consider recent actions
      if (currentTime - action.timestamp > revertWindow) {
        break;
      }

      // Skip actions that were already unsuccessful
      if (!action.successful) {
        continue;
      }

      // Revert the action based on component type
      bool reverted = false;
      if (action.component == "JIT" && action.parameter == "cache_clear") {
        // For JIT cache clears, we can't really "undo" them, but we can
        // adjust the threshold to be more conservative
        if (m_jitThreshold < 0.95f) {
          m_jitThreshold += 0.05f;
          reverted = true;
          spdlog::info("Reverted JIT tuning: increased threshold to {:.2f}",
                       m_jitThreshold);
        }
      } else if (action.component == "Memory") {
        // Revert memory-related changes
        auto &memory = m_emulatorCore.GetMMU();
        if (action.parameter == "prefetch_policy") {
          // Revert to more conservative prefetch policy
          if (memory.GetPrefetchHint() ==
              ps4::PrefetchHint::PREFETCH_AGGRESSIVE) {
            memory.SetPrefetchHint(ps4::PrefetchHint::PREFETCH_CONSERVATIVE);
            reverted = true;
            spdlog::info(
                "Reverted memory tuning: downgraded to conservative prefetch");
          }
        } else if (action.parameter == "compression") {
          // Toggle compression state back
          memory.EnableCompression(action.oldValue > 0.5f);
          reverted = true;
          spdlog::info("Reverted memory compression to previous state");
        }
      } else if (action.component == "Fiber") {
        // Revert fiber manager changes
        auto &fiberManager = m_emulatorCore.GetFiberManager();
        if (action.parameter == "scheduling_policy") {
          // Revert to priority scheduling
          fiberManager.SetSchedulingPolicy(ps4::SchedulingPolicy::PRIORITY);
          reverted = true;
          spdlog::info(
              "Reverted fiber tuning: switched back to priority scheduling");
        }
      } else if (action.component == "Threshold") {
        // Revert threshold adjustments
        if (action.parameter == "jit_threshold") {
          m_jitThreshold = action.oldValue;
          reverted = true;
          spdlog::info("Reverted JIT threshold to {:.2f}", m_jitThreshold);
        } else if (action.parameter == "memory_threshold") {
          m_memoryThreshold = action.oldValue;
          reverted = true;
          spdlog::info("Reverted memory threshold to {:.2f}",
                       m_memoryThreshold);
        } else if (action.parameter == "fiber_threshold") {
          m_fiberThreshold = action.oldValue;
          reverted = true;
          spdlog::info("Reverted fiber threshold to {:.2f}", m_fiberThreshold);
        }
      }

      if (reverted) {
        action.successful = false; // Mark as reverted
        revertedActions++;

        // Record the reversion as a new action
        TuningAction revertAction;
        revertAction.component = action.component;
        revertAction.parameter = action.parameter + "_revert";
        revertAction.oldValue = action.newValue;
        revertAction.newValue = action.oldValue;
        revertAction.timestamp = currentTime;
        revertAction.performanceImpact = -action.performanceImpact;
        revertAction.successful = true;
        m_tuningHistory.push_back(revertAction);
      }
    }

    if (revertedActions > 0) {
      spdlog::warn(
          "Reverted {} unstable tuning actions due to system instability",
          revertedActions);
      m_stats["tuning_reverts"] += revertedActions;
    } else {
      spdlog::debug("No recent tuning actions to revert");
    }

  } catch (const std::exception &e) {
    spdlog::error("RevertUnstableTuning failed: {}", e.what());
  }
}

/**
 * @brief Gets the trend value for a specific metric.
 * @param metricName The name of the metric to get the trend for.
 * @param windowSize The number of recent values to consider (unused, uses
 * history window).
 * @return The trend value, or 0.0f if metric not found or insufficient data.
 */
float AdaptiveEmulationOrchestrator::GetMetricTrend(
    const std::string &metricName, size_t windowSize) {
  try {
    auto it = m_metricHistory.find(metricName);
    if (it == m_metricHistory.end()) {
      spdlog::debug("Metric {} not found in history", metricName);
      return 0.0f;
    }

    const MetricHistory &history = it->second;
    if (history.values.size() < 5) {
      spdlog::debug("Insufficient data for trend calculation of {}: {} values",
                    metricName, history.values.size());
      return 0.0f;
    }

    // Return the pre-calculated trend from UpdateTuningHistory
    return history.trend;

  } catch (const std::exception &e) {
    spdlog::error("GetMetricTrend failed for {}: {}", metricName, e.what());
    return 0.0f;
  }
}

} // namespace x86_64
