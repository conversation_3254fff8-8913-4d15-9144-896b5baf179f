// Copyright 2025 <Copyright Owner>
#pragma once

#include "../ps4/ps4_emulator.h"
#include <iostream>
#include <mutex>
#include <unordered_map>

/**
 * @brief Namespace for x86_64 architecture components.
 */
namespace x86_64 {

/**
 * @brief Orchestrates adaptive emulation optimizations for the PS4 emulator.
 */
class AdaptiveEmulationOrchestrator {
public:
  // Delete copy and move operations to ensure singleton-like behavior
  AdaptiveEmulationOrchestrator(const AdaptiveEmulationOrchestrator &) = delete;
  AdaptiveEmulationOrchestrator &
  operator=(const AdaptiveEmulationOrchestrator &) = delete;
  AdaptiveEmulationOrchestrator(AdaptiveEmulationOrchestrator &&) = delete;
  AdaptiveEmulationOrchestrator &
  operator=(AdaptiveEmulationOrchestrator &&) = delete;

  /**
   * @brief Constructs the orchestrator.
   * @param emulator Reference to the PS4Emulator instance.
   */
  explicit AdaptiveEmulationOrchestrator(ps4::PS4Emulator &emulator);

  /**
   * @brief Runs one optimization cycle.
   * @details Collects metrics and tunes JIT, memory, and fiber systems.
   */
  void Cycle();

  /**
   * @brief Thread-safe access to performance metrics.
   * @return Current performance metrics.
   */
  const std::unordered_map<std::string, float> &GetPerformanceMetrics() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_performanceMetrics;
  }

  /**
   * @brief Saves the orchestrator state.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the orchestrator state.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

private:
  /**
   * @brief Collects performance metrics from emulator components.
   */
  void CollectPerformanceMetrics();

  /**
   * @brief Tunes the JIT compiler based on performance metrics.
   */
  void TuneJITCompiler();

  /**
   * @brief Tunes the memory hierarchy based on performance metrics.
   */
  void TuneMemoryHierarchy();

  /**
   * @brief Tunes the fiber manager based on performance metrics.
   */
  void TuneFiberManager();

  /**
   * @brief Adjusts optimization thresholds based on performance metrics.
   */
  void AdjustThresholds();

  /**
   * @brief Enhanced metric validation and tuning methods.
   */
  bool ValidateMetrics();
  void PopulateMemoryDiagnostics();
  void CalculateDerivedMetrics();
  void UpdateTuningHistory();
  bool IsMetricStable(const std::string &metricName, float threshold = 0.1f);
  float GetMetricTrend(const std::string &metricName, size_t windowSize = 10);
  void ApplyAdaptiveTuning();
  void RevertUnstableTuning();

  /**
   * @brief Enhanced tuning structures.
   */
  struct TuningAction {
    std::string component;
    std::string parameter;
    float oldValue;
    float newValue;
    uint64_t timestamp;
    float performanceImpact;
    bool successful;
  };

  struct MetricHistory {
    std::vector<float> values;
    std::vector<uint64_t> timestamps;
    float average = 0.0f;
    float variance = 0.0f;
    float trend = 0.0f;
    bool stable = true;
  };

  ps4::PS4Emulator &m_emulatorCore; ///< Reference to the emulator
  std::unordered_map<std::string, float>
      m_performanceMetrics; ///< Performance metrics
  std::unordered_map<std::string, uint64_t>
      m_stats;                             ///< Optimization statistics
  float m_jitThreshold = 0.9f;             ///< JIT cache usage threshold
  float m_memoryThreshold = 0.8f;          ///< Memory bandwidth threshold
  float m_fiberThreshold = 0.7f;           ///< Fiber load threshold
  const float m_minJitThreshold = 0.5f;    ///< Minimum JIT threshold
  const float m_minMemoryThreshold = 0.4f; ///< Minimum memory threshold
  const float m_minFiberThreshold = 0.3f;  ///< Minimum fiber threshold
  mutable std::mutex m_mutex;              ///< Thread safety mutex

  // Enhanced tuning data
  std::unordered_map<std::string, MetricHistory>
      m_metricHistory;                       ///< Metric history tracking
  std::vector<TuningAction> m_tuningHistory; ///< Tuning action history
  uint64_t m_lastTuningTime = 0;             ///< Last tuning timestamp
  uint64_t m_tuningCooldownMs = 5000;  ///< Tuning cooldown period (5 seconds)
  float m_stabilityThreshold = 0.05f;  ///< Metric stability threshold (5%)
  size_t m_historyWindowSize = 20;     ///< History window size
  bool m_adaptiveTuningEnabled = true; ///< Adaptive tuning enabled
  float m_performanceBaseline = 0.0f;  ///< Performance baseline for comparison
};

} // namespace x86_64