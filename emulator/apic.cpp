// Copyright 2025 xAI
#include "apic.h"
#include "../common/lock_ordering.h"
#include <chrono>
#include <cstdint>
#include <mutex>
#include <stdexcept>
#include <string>
#include <iostream>
#include <unordered_map>
#include <array>

#ifdef _MSC_VER
#include <intrin.h>
#endif

// Include necessary headers for complete type definitions
#include "ps4_emulator.h"
#include "x86_64_cpu.h"
#include "interrupt_handler.h"

// Forward declarations to avoid circular dependencies
namespace ps4 {
class PS4Emulator;
}
namespace x86_64 {
class X86_64CPU;
}

// Simple logging replacement - disable spdlog usage
#ifndef SPDLOG_ACTIVE_LEVEL
#define SPDLOG_ACTIVE_LEVEL SPDLOG_LEVEL_OFF
#endif
#include <spdlog/spdlog.h>

// Note: Removed problematic spdlog override that was causing crashes

namespace x86_64 {
struct APICException : std::runtime_error {
  explicit APICException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief Constructs an APIC instance for a specific core.
 * @details Initializes the APIC with a core ID, default base address, and
 * registers.
 * @param coreId The ID of the associated CPU core.
 */
APIC::APIC(uint32_t coreId)
    : m_coreId(coreId), m_baseAddress(0xFEE00000), m_enabled(false),
      m_timerCycles(0), m_initializationComplete(false) {
  m_lapicRegisters.fill(0);
  m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::ID)] = coreId;
  m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::VERSION)] =
      0x14; // APIC version
  m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::SPURIOUS)] =
      0x1FF; // Spurious vector enabled
  spdlog::info("APIC initialized for core {}", coreId);
}

/**
 * @brief Destructs the APIC, ensuring proper shutdown.
 */
APIC::~APIC() noexcept {
  Shutdown();
  spdlog::info("APIC for core {} destroyed", m_coreId);
}

/**
 * @brief Initializes the APIC.
 * @details Sets up registers and enables the APIC for interrupt processing.
 * @return True on success, false on failure.
 */
bool APIC::Initialize() {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_apicMutex, "APICMutex");
  try {
    m_enabled = true;
    m_stats = APICStats();
    m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::SPURIOUS)] |=
        0x100; // Enable APIC
    m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::TIMER)] =
        0x20; // Timer vector

    // Mark initialization as complete
    m_initializationComplete = true;

    auto end = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    spdlog::info("APIC for core {} initialized in {}ms", m_coreId, duration.count());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("APIC initialization failed for core {}: {}", m_coreId,
                  e.what());
    m_initializationComplete = false;
    return false;
  }
}

/**
 * @brief Shuts down the APIC, disabling interrupt processing.
 */
void APIC::Shutdown() {
  COMPONENT_LOCK(m_apicMutex, "APICMutex");
  m_enabled = false;
  m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::SPURIOUS)] &=
      ~0x100; // Disable APIC
  spdlog::info("APIC for core {} shut down", m_coreId);
}

/**
 * @brief Gets the device name.
 * @return Device name string.
 */
std::string APIC::GetName() const {
  return "APIC";
}

/**
 * @brief Reads an APIC register.
 * @details Retrieves the value of the specified LAPIC register, thread-safely.
 * @param reg The register to read.
 * @return The register value.
 */
uint32_t APIC::ReadRegister(LAPICRegister reg) {
  COMPONENT_LOCK(m_apicMutex, "APICMutex");
  auto start = std::chrono::steady_clock::now();
  if (!m_enabled) {
    spdlog::warn("Read from disabled APIC core {} register {}", m_coreId,
                 static_cast<uint32_t>(reg));
    return 0;
  }
  
  // CRITICAL: Bounds check for register array access
  uint32_t regIndex = static_cast<uint32_t>(reg);
  if (regIndex >= m_lapicRegisters.size()) {
    spdlog::error("APIC ReadRegister: register index {} out of bounds", regIndex);
    return 0;
  }
  
  uint32_t value = m_lapicRegisters[regIndex];
  m_stats.operationCount++;
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  spdlog::trace("APIC core {} read register {}: 0x{:x}", m_coreId,
                static_cast<uint32_t>(reg), value);
  return value;
}

/**
 * @brief Writes to an APIC register.
 * @details Updates the specified LAPIC register, handling special cases like
 * ICR and timer writes.
 * @param reg The register to write.
 * @param value The value to write.
 */
void APIC::WriteRegister(LAPICRegister reg, uint32_t value) {
  COMPONENT_LOCK(m_apicMutex, "APICMutex");
  auto start = std::chrono::steady_clock::now();
  if (!m_enabled) {
    spdlog::warn("Write to disabled APIC core {} register {}", m_coreId,
                 static_cast<uint32_t>(reg));
    return;
  }
  try {
    switch (reg) {
    case LAPICRegister::EOI:
      spdlog::trace("APIC core {} EOI written", m_coreId);
      break;
    case LAPICRegister::ICR_LOW: {
      uint32_t icrLowIndex = static_cast<uint32_t>(LAPICRegister::ICR_LOW);
      uint32_t icrHighIndex = static_cast<uint32_t>(LAPICRegister::ICR_HIGH);
      // CRITICAL: Bounds checks for register array access
      if (icrLowIndex >= m_lapicRegisters.size() || icrHighIndex >= m_lapicRegisters.size()) {
        spdlog::error("APIC WriteRegister: ICR register index out of bounds");
        return;
      }
      m_lapicRegisters[icrLowIndex] = value;
      HandleICRWrite(value, m_lapicRegisters[icrHighIndex]);
      break;
    }
    case LAPICRegister::ICR_HIGH: {
      uint32_t icrHighIndex = static_cast<uint32_t>(LAPICRegister::ICR_HIGH);
      if (icrHighIndex >= m_lapicRegisters.size()) {
        spdlog::error("APIC WriteRegister: ICR_HIGH register index out of bounds");
        return;
      }
      m_lapicRegisters[icrHighIndex] = value;
      break;
    }
    case LAPICRegister::SPURIOUS:
      m_lapicRegisters[static_cast<uint32_t>(reg)] = value & 0x1FF;
      m_enabled = (value & 0x100) != 0;
      spdlog::trace("APIC core {} spurious register set: enabled={}", m_coreId,
                    m_enabled);
      break;
    case LAPICRegister::TIMER_INITIAL_COUNT:
      m_lapicRegisters[static_cast<uint32_t>(reg)] = value;
      m_timerCycles = value;
      spdlog::trace("APIC core {} timer initial count set: {}", m_coreId,
                    value);
      break;
    default: {
      uint32_t regIndex = static_cast<uint32_t>(reg);
      // CRITICAL: Bounds check for register array access
      if (regIndex >= m_lapicRegisters.size()) {
        spdlog::error("APIC WriteRegister: register index {} out of bounds", regIndex);
        return;
      }
      m_lapicRegisters[regIndex] = value;
      spdlog::trace("APIC core {} write register {}: 0x{:x}", m_coreId,
                    regIndex, value);
    }
    }
    m_stats.operationCount++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("APIC core {} register write failed: {}", m_coreId, e.what());
  }
}

/**
 * @brief Sends an Inter-Processor Interrupt (IPI).
 * @details Dispatches an IPI to the specified destination core with the given
 * vector and type.
 * @param destination The destination core ID or logical ID.
 * @param vector The interrupt vector.
 * @param type The IPI type.
 */
void APIC::SendIPI(uint32_t destination, uint32_t vector, IPIType type) {
  COMPONENT_LOCK(m_apicMutex, "APICMutex");
  auto start = std::chrono::steady_clock::now();

  // Add timeout protection for IPI operations
  const auto timeout = std::chrono::milliseconds(100);

  if (!m_enabled) {
    spdlog::warn("IPI sent from disabled APIC core {}", m_coreId);
    return;
  }

  // Skip complex IPI handling during initialization to prevent deadlocks
  if (!m_initializationComplete) {
    spdlog::debug("Skipping IPI during initialization on core {}", m_coreId);
    return;
  }

  uint32_t targetCore = destination;
  try {
    if (type == IPIType::LOWEST_PRIORITY) {
      targetCore = FindLowestPriorityCore();
      if (targetCore == UINT32_MAX) {
        spdlog::error("No valid target for lowest priority IPI from core {}",
                      m_coreId);
        return;
      }
    } else {
      targetCore = FindLogicalDestination(destination);
      if (targetCore == UINT32_MAX) {
        spdlog::trace(
            "No valid logical destination 0x{:x} for IPI from core {}",
            destination, m_coreId);
        return;
      }
    }

    // Check for timeout
    auto current = std::chrono::steady_clock::now();
    if (current - start > timeout) {
      spdlog::warn("IPI operation timed out on core {}", m_coreId);
      return;
    }

    // Implement proper IPI delivery mechanism with timeout protection
    // Get target CPU and its APIC for IPI delivery
    ps4::PS4Emulator *emulator = &ps4::PS4Emulator::GetInstance();
    // CRITICAL: Bounds check for CPU core access
    if (emulator && emulator->GetCPUCount() > 0 && targetCore < emulator->GetCPUCount()) {
      try {
        x86_64::X86_64CPU &targetCpu = emulator->GetCPU(targetCore);
        x86_64::APIC &targetApic = targetCpu.GetAPIC();

        // Check if target APIC is enabled before delivery
        if (targetApic.ReadRegister(LAPICRegister::SPURIOUS) & 0x100) {
          // Deliver the interrupt to the target APIC
          targetApic.HandleInterrupt(vector);
          spdlog::trace("IPI delivered to core {}: vector=0x{:x}, type={}",
                        targetCore, vector, static_cast<uint32_t>(type));
        } else {
          spdlog::warn("IPI to disabled APIC core {} dropped", targetCore);
        }
      } catch (const std::exception &e) {
        spdlog::error("Failed to deliver IPI to core {}: {}", targetCore, e.what());
        return;
      }
    } else {
      spdlog::error("Invalid target core {} for IPI delivery", targetCore);
    }

    m_stats.operationCount++;
    m_stats.interruptCounts[vector]++;
    m_stats.ipiTypeCounts[type]++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("APIC core {} sent IPI to core {}: vector=0x{:x}, type={}",
                 m_coreId, targetCore, vector, static_cast<uint32_t>(type));
  } catch (const std::exception &e) {
    spdlog::error("APIC core {} failed to send IPI: {}", m_coreId, e.what());
  }
}

/**
 * @brief Handles an interrupt received by the APIC.
 * @details Processes the interrupt vector, forwarding it to the CPU’s interrupt
 * handler.
 * @param vector The interrupt vector.
 */
void APIC::HandleInterrupt(uint32_t vector) {
  COMPONENT_LOCK(m_apicMutex, "APICMutex");
  auto start = std::chrono::steady_clock::now();

  // Add timeout protection
  const auto timeout = std::chrono::milliseconds(50);

  if (!m_enabled) {
    spdlog::warn("Interrupt 0x{:x} ignored on disabled APIC core {}", vector,
                 m_coreId);
    return;
  }

  // Simplified interrupt handling during initialization
  if (!m_initializationComplete) {
    spdlog::debug("Queuing interrupt 0x{:x} during initialization on core {}", vector, m_coreId);
    m_stats.operationCount++;
    m_stats.interruptCounts[vector]++;
    return;
  }

try {
  // Get the associated CPU and trigger the interrupt through the interrupt
  // handler
  ps4::PS4Emulator *emulator = &ps4::PS4Emulator::GetInstance();
  if (emulator) {
    // Check timeout
    auto current = std::chrono::steady_clock::now();
    if (current - start > timeout) {
      spdlog::warn("Interrupt handling timed out on core {}", m_coreId);
      return;
    }

    // Check if we have enough CPUs and the interrupt handler is available
    if (m_coreId >= emulator->GetCPUCount()) {
      spdlog::warn("Invalid core ID {} for interrupt delivery", m_coreId);
      return;
    }

    x86_64::X86_64CPU &cpu = emulator->GetCPU(m_coreId);

    // Check if interrupt handler is available (it might not be during initialization)
    try {
      x86_64::InterruptHandler &intHandler = emulator->GetInterruptHandler();

      // Check priority against TPR (Task Priority Register)
      uint32_t tpr =
          m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::TPR)];
      uint32_t vectorPriority = (vector >> 4) & 0xF;
      uint32_t tprPriority = (tpr >> 4) & 0xF;

      if (vectorPriority > tprPriority) {
        // Trigger the interrupt through the CPU's interrupt handler
        intHandler.HandleInterrupt(static_cast<uint8_t>(vector), 0);
        spdlog::trace(
            "Interrupt 0x{:x} delivered to core {} (priority {} > TPR {})",
            vector, m_coreId, vectorPriority, tprPriority);
      } else {
        spdlog::trace("Interrupt 0x{:x} blocked by TPR on core {} (priority {} "
                      "<= TPR {})",
                      vector, m_coreId, vectorPriority, tprPriority);
      }
    } catch (const std::exception &e) {
      spdlog::debug("Interrupt handler not available during initialization, "
                    "queuing interrupt 0x{:x} for core {}", vector, m_coreId);
      // Queue the interrupt directly to the CPU instead
      cpu.QueueInterrupt(static_cast<uint8_t>(vector), 0);
    }
  } else {
    spdlog::error(
        "Cannot deliver interrupt: emulator instance not available");
  }

  m_stats.operationCount++;
  m_stats.interruptCounts[vector]++;
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  spdlog::trace("APIC core {} handled interrupt 0x{:x}", m_coreId, vector);
} catch (const std::exception &e) {
  spdlog::error("APIC core {} failed to handle interrupt 0x{:x}: {}", m_coreId,
                vector, e.what());
}
}

/**
 * @brief Updates the APIC timer state.
 * @details Decrements the timer count and triggers a timer interrupt if
 * necessary.
 * @param cycles Number of CPU cycles to advance.
 */
void APIC::UpdateTimer(uint64_t cycles) {
  COMPONENT_LOCK(m_apicMutex, "APICMutex");
  if (!m_enabled || m_timerCycles == 0) {
    return;
  }
  try {
    m_timerCycles = (m_timerCycles > cycles) ? m_timerCycles - cycles : 0;
    if (m_timerCycles == 0) {
      uint32_t vector =
          m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::TIMER)] & 0xFF;
      HandleInterrupt(vector);
      m_stats.timerInterrupts++;
      // Reload timer if periodic mode is enabled
      if (m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::TIMER)] &
          0x20000) {
        m_timerCycles = m_lapicRegisters[static_cast<uint32_t>(
            LAPICRegister::TIMER_INITIAL_COUNT)];
      }
      spdlog::trace("APIC core {} timer interrupt triggered: vector=0x{:x}",
                    m_coreId, vector);
    }
  } catch (const std::exception &e) {
    spdlog::error("APIC core {} timer update failed: {}", m_coreId, e.what());
  }
}

/**
 * @brief Handles writes to the Interrupt Command Register (ICR).
 * @details Processes ICR low and high values to send IPIs based on the
 * specified parameters.
 * @param icrLow The low 32 bits of the ICR.
 * @param icrHigh The high 32 bits of the ICR.
 */
void APIC::HandleICRWrite(uint32_t icrLow, uint32_t icrHigh) {
  uint32_t vector = icrLow & 0xFF;
  uint32_t deliveryMode = (icrLow >> 8) & 0x7;
  uint32_t destinationMode =
      (icrLow >> 11) & 0x1; // TODO: Use for physical vs logical addressing
  uint32_t destination = (icrHigh >> 24) & 0xFF;
  IPIType type = static_cast<IPIType>(deliveryMode);
  spdlog::trace(
      "APIC core {} ICR write: vector=0x{:x}, mode=0x{:x}, dest=0x{:x}",
      m_coreId, vector, deliveryMode, destination);
  SendIPI(destination, vector, type);
}

/**
 * @brief Finds the core corresponding to a logical destination ID.
 * @details Maps the logical ID to a physical core ID.
 * @param logicalId The logical destination ID.
 * @return The core ID, or UINT32_MAX if not found.
 */
uint32_t APIC::FindLogicalDestination(
    uint32_t logicalId) { // Implement proper logical destination mapping
  auto start = std::chrono::steady_clock::now();
  const auto timeout = std::chrono::milliseconds(10); // Short timeout for this operation

  try {
    ps4::PS4Emulator *emulator = &ps4::PS4Emulator::GetInstance();
    if (!emulator) {
      return UINT32_MAX;
    }

    uint32_t coreCount = emulator->GetCPUCount();

    // Limit core count to prevent infinite loops
    coreCount = std::min(coreCount, 32u);

    // Check destination format register to determine addressing mode
    uint32_t dfr = m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::DFR)];
    bool flatModel = (dfr & 0xF0000000) == 0xF0000000;

    if (flatModel) {
      // Flat model: bit N in logical ID corresponds to core N
      for (uint32_t coreId = 0; coreId < coreCount && coreId < 32; ++coreId) {
        // Check timeout to prevent infinite loops
        auto current = std::chrono::steady_clock::now();
        if (current - start > timeout) {
          spdlog::warn("FindLogicalDestination timed out in flat model");
          break;
        }

        if (logicalId & (1U << coreId)) {
          try {
            x86_64::X86_64CPU &cpu = emulator->GetCPU(coreId);
            x86_64::APIC &apic = cpu.GetAPIC();
            uint32_t ldr = apic.ReadRegister(LAPICRegister::LDR);

            // Check if this core's logical destination register matches
            if ((ldr >> 24) & (1U << coreId)) {
              return coreId;
            }
          } catch (const std::exception &e) {
            spdlog::debug("Error accessing core {} in logical destination mapping: {}", coreId, e.what());
            continue;
          }
        }
      }
    } else {
      // Cluster model: use cluster ID and member mask
      uint32_t clusterId = (logicalId >> 4) & 0xF;
      uint32_t memberMask = logicalId & 0xF;

      for (uint32_t coreId = 0; coreId < coreCount; ++coreId) {
        // Check timeout to prevent infinite loops
        auto current = std::chrono::steady_clock::now();
        if (current - start > timeout) {
          spdlog::warn("FindLogicalDestination timed out in cluster model");
          break;
        }

        try {
          x86_64::X86_64CPU &cpu = emulator->GetCPU(coreId);
          x86_64::APIC &apic = cpu.GetAPIC();
          uint32_t ldr = apic.ReadRegister(LAPICRegister::LDR);

          uint32_t coreCluster = (ldr >> 28) & 0xF;
          uint32_t coreMask = (ldr >> 24) & 0xF;

          if (coreCluster == clusterId && (coreMask & memberMask)) {
            return coreId;
          }
        } catch (const std::exception &e) {
          spdlog::debug("Error accessing core {} in cluster model: {}", coreId, e.what());
          continue;
        }
      }
    }
  } catch (const std::exception &e) {
    spdlog::error("Logical destination mapping failed: {}", e.what());
  }

  // Fallback: direct mapping for cores 0-7 if no match found
  if (logicalId < 8) {
    return logicalId;
  }
  return UINT32_MAX;
}

/**
 * @brief Finds the core with the lowest priority for IPI delivery.
 * @details Selects a core based on TPR values from each core's APIC.
 * @return The core ID, or UINT32_MAX if none available.
 */
uint32_t APIC::FindLowestPriorityCore() { // Implement proper priority-based
                                          // core selection using TPR values
  auto start = std::chrono::steady_clock::now();
  const auto timeout = std::chrono::milliseconds(10); // Short timeout

  try {
    ps4::PS4Emulator *emulator = &ps4::PS4Emulator::GetInstance();
    if (!emulator) {
      return 0; // Fallback to core 0
    }

    uint32_t coreCount = emulator->GetCPUCount();

    // Limit core count to prevent infinite loops
    coreCount = std::min(coreCount, 32u);

    uint32_t selectedCore = UINT32_MAX;
    uint32_t lowestPriority = UINT32_MAX;

    // Search for the core with the lowest TPR (Task Priority Register) value
    for (uint32_t coreId = 0; coreId < coreCount; ++coreId) {
      // Check timeout to prevent infinite loops
      auto current = std::chrono::steady_clock::now();
      if (current - start > timeout) {
        spdlog::warn("FindLowestPriorityCore timed out");
        break;
      }

      try {
        x86_64::X86_64CPU &cpu = emulator->GetCPU(coreId);
        x86_64::APIC &apic = cpu.GetAPIC();

        // Check if APIC is enabled
        uint32_t spurious = apic.ReadRegister(LAPICRegister::SPURIOUS);
        if (!(spurious & 0x100)) {
          continue; // Skip disabled APICs
        }

        uint32_t tpr = apic.ReadRegister(LAPICRegister::TPR);
        uint32_t priority = (tpr >> 4) & 0xF; // Extract priority class

        // Lower TPR value means higher interrupt acceptance priority
        if (priority < lowestPriority) {
          lowestPriority = priority;
          selectedCore = coreId;
        }
      } catch (const std::exception &e) {
        spdlog::debug("Error accessing core {} for priority selection: {}",
                     coreId, e.what());
        continue;
      }
    }

    if (selectedCore != UINT32_MAX) {
      spdlog::trace(
          "Selected core {} for lowest priority IPI (TPR priority={})",
          selectedCore, lowestPriority);
      return selectedCore;
    }
  } catch (const std::exception &e) {
    spdlog::error("Priority-based core selection failed: {}", e.what());
  }

  // Fallback to core 0 if no suitable core found
  uint32_t targetCore = 0;

  // Use our own TPR as a baseline
  uint32_t ourTpr = m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::TPR)];
  spdlog::trace("Using fallback core {} for lowest priority IPI (TPR={})",
                targetCore, ourTpr);

  return targetCore;
}

/**
 * @brief Counts trailing zeros in a 32-bit value.
 * @param value The input value.
 * @return The number of trailing zeros.
 */
uint32_t APIC::CountTrailingZeros(uint32_t value) {
  if (value == 0)
    return 32;
#ifdef _MSC_VER
  unsigned long idx;
  _BitScanForward(&idx, value);
  return idx;
#else
  return __builtin_ctz(value);
#endif
}

/**
 * @brief Counts leading zeros in a 32-bit value.
 * @param value The input value.
 * @return The number of leading zeros.
 */
uint32_t APIC::CountLeadingZeros(uint32_t value) {
  if (value == 0)
    return 32;
#ifdef _MSC_VER
  unsigned long idx;
  _BitScanReverse(&idx, value);
  return 31 - idx;
#else
  return __builtin_clz(value);
#endif
}

/**
 * @brief Saves the APIC state to a stream.
 * @details Serializes register values, statistics, and timer state.
 * @param out The output stream.
 */
void APIC::SaveState(std::ostream &out) const {
  COMPONENT_LOCK(m_apicMutex, "APICMutex");
  uint32_t version = 1;
  out.write(reinterpret_cast<const char *>(&version), sizeof(version));
  out.write(reinterpret_cast<const char *>(&m_coreId), sizeof(m_coreId));
  out.write(reinterpret_cast<const char *>(&m_baseAddress),
            sizeof(m_baseAddress));
  out.write(reinterpret_cast<const char *>(&m_enabled), sizeof(m_enabled));
  out.write(reinterpret_cast<const char *>(m_lapicRegisters.data()),
            m_lapicRegisters.size() * sizeof(uint32_t));
  out.write(reinterpret_cast<const char *>(&m_stats.operationCount),
            sizeof(m_stats.operationCount));
  out.write(reinterpret_cast<const char *>(&m_stats.totalLatencyUs),
            sizeof(m_stats.totalLatencyUs));
  out.write(reinterpret_cast<const char *>(&m_timerCycles),
            sizeof(m_timerCycles));
  uint32_t interruptCount =
      static_cast<uint32_t>(m_stats.interruptCounts.size());
  out.write(reinterpret_cast<const char *>(&interruptCount),
            sizeof(interruptCount));
  for (const auto &entry : m_stats.interruptCounts) {
    out.write(reinterpret_cast<const char *>(&entry.first),
              sizeof(entry.first));
    out.write(reinterpret_cast<const char *>(&entry.second),
              sizeof(entry.second));
  }
  uint32_t ipiTypeCount = static_cast<uint32_t>(m_stats.ipiTypeCounts.size());
  out.write(reinterpret_cast<const char *>(&ipiTypeCount),
            sizeof(ipiTypeCount));
  for (const auto &entry : m_stats.ipiTypeCounts) {
    out.write(reinterpret_cast<const char *>(&entry.first),
              sizeof(entry.first));
    out.write(reinterpret_cast<const char *>(&entry.second),
              sizeof(entry.second));
  }
  out.write(reinterpret_cast<const char *>(&m_stats.timerInterrupts),
            sizeof(m_stats.timerInterrupts));
  spdlog::info("APIC core {} state saved", m_coreId);
}

/**
 * @brief Loads the APIC state from a stream.
 * @details Deserializes register values, statistics, and timer state.
 * @param in The input stream.
 */
void APIC::LoadState(std::istream &in) {
  COMPONENT_LOCK(m_apicMutex, "APICMutex");
  uint32_t version;
  in.read(reinterpret_cast<char *>(&version), sizeof(version));
  if (version != 1) {
    spdlog::error("Unsupported APIC state version: {}", version);
    throw APICException("Invalid APIC state version");
  }
  in.read(reinterpret_cast<char *>(&m_coreId), sizeof(m_coreId));
  in.read(reinterpret_cast<char *>(&m_baseAddress), sizeof(m_baseAddress));
  in.read(reinterpret_cast<char *>(&m_enabled), sizeof(m_enabled));
  in.read(reinterpret_cast<char *>(m_lapicRegisters.data()),
          m_lapicRegisters.size() * sizeof(uint32_t));
  in.read(reinterpret_cast<char *>(&m_stats.operationCount),
          sizeof(m_stats.operationCount));
  in.read(reinterpret_cast<char *>(&m_stats.totalLatencyUs),
          sizeof(m_stats.totalLatencyUs));
  in.read(reinterpret_cast<char *>(&m_timerCycles), sizeof(m_timerCycles));
  uint32_t interruptCount;
  in.read(reinterpret_cast<char *>(&interruptCount), sizeof(interruptCount));
  m_stats.interruptCounts.clear();
  for (uint32_t i = 0; i < interruptCount && in.good(); ++i) {
    uint32_t vector;
    uint64_t count;
    in.read(reinterpret_cast<char *>(&vector), sizeof(vector));
    in.read(reinterpret_cast<char *>(&count), sizeof(count));
    m_stats.interruptCounts[vector] = count;
  }
  uint32_t ipiTypeCount;
  in.read(reinterpret_cast<char *>(&ipiTypeCount), sizeof(ipiTypeCount));
  m_stats.ipiTypeCounts.clear();
  for (uint32_t i = 0; i < ipiTypeCount && in.good(); ++i) {
    IPIType type;
    uint64_t count;
    in.read(reinterpret_cast<char *>(&type), sizeof(type));
    in.read(reinterpret_cast<char *>(&count), sizeof(count));
    m_stats.ipiTypeCounts[type] = count;
  }
  in.read(reinterpret_cast<char *>(&m_stats.timerInterrupts),
          sizeof(m_stats.timerInterrupts));
  spdlog::info("APIC core {} state loaded", m_coreId);
}

/**
 * @brief Retrieves APIC operation statistics.
 * @return The APIC statistics structure.
 */
APICStats APIC::GetStats() const {
  COMPONENT_LOCK(m_apicMutex, "APICMutex");
  return m_stats;
}

// Device interface implementations
uint64_t APIC::Read(uint64_t address, uint8_t size) {
  if (size != 4) {
    spdlog::warn("APIC: Invalid read size {} at address 0x{:x}", size, address);
    return 0;
  }

  // Convert address to register offset
  uint32_t regOffset = static_cast<uint32_t>(address - m_baseAddress);
  LAPICRegister reg = static_cast<LAPICRegister>(regOffset / 4);

  return ReadRegister(reg);
}

void APIC::Write(uint64_t address, uint64_t value, uint8_t size) {
  if (size != 4) {
    spdlog::warn("APIC: Invalid write size {} at address 0x{:x}", size, address);
    return;
  }

  // Convert address to register offset
  uint32_t regOffset = static_cast<uint32_t>(address - m_baseAddress);
  LAPICRegister reg = static_cast<LAPICRegister>(regOffset / 4);

  WriteRegister(reg, static_cast<uint32_t>(value));
}

void APIC::Reset() {
  COMPONENT_LOCK(m_apicMutex, "APICMutex");

  // Reset all registers to default values
  m_lapicRegisters.fill(0);
  m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::ID)] = m_coreId;
  m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::VERSION)] = 0x14;
  m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::SPURIOUS)] = 0x1FF;

  // Reset state
  m_enabled = false;
  m_timerCycles = 0;

  // Reset statistics
  m_stats = {};

  spdlog::info("APIC core {} reset to default state", m_coreId);
}

} // namespace x86_64