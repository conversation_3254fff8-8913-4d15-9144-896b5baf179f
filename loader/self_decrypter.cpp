#include "self_decrypter.h"
#include "crypto_utils.h"
#include "key_store.h"
#include "pkg_installer.h"
#include "../debug/vector_debug.h"
#include <algorithm>
#include <cstring>
#include <filesystem>
#include <fstream>
#include <memory>
#include <openssl/aes.h>
#include <openssl/evp.h>
#include <openssl/hmac.h>
#include <openssl/rand.h>
#include <openssl/sha.h>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <zlib.h>

// Define WIN32_LEAN_AND_MEAN to reduce Windows header bloat
#define WIN32_LEAN_AND_MEAN
#ifdef _WIN32// Must come before windows.h if used elsewhere
#else
#include <arpa/inet.h>
#endif

namespace ps4 {

SelfDecrypter::SelfDecrypter() : is_loaded_(false), is_decrypted_(false) {
  ClearError();
  if (!LoadSystemKeys()) {
    SetError("Failed to load system keys. This may be due to a missing or "
             "invalid './ps4_root/keys.json' file.");
  }
  spdlog::info("SelfDecrypter initialized");
}

SelfDecrypter::~SelfDecrypter() {
  std::fill(self_data_.begin(), self_data_.end(), 0);
  std::fill(decrypted_elf_.begin(), decrypted_elf_.end(), 0);
  system_keys_.clear();
  spdlog::info("SelfDecrypter destroyed");
}

bool SelfDecrypter::LoadSelfFile(const std::string &filepath) {
  try {
    if (!std::filesystem::exists(filepath)) {
      SetError("SELF file does not exist: " + filepath);
      spdlog::error("{}", last_error_);
      return false;
    }

    std::ifstream file(filepath, std::ios::binary | std::ios::ate);
    if (!file.is_open()) {
      SetError("Failed to open SELF file: " + filepath);
      spdlog::error("{}", last_error_);
      return false;
    }

    size_t file_size = file.tellg();
    if (file_size < MIN_SELF_SIZE || file_size > MAX_SELF_SIZE) {
      SetError("Invalid SELF file size: " + std::to_string(file_size));
      spdlog::error("{}", last_error_);
      return false;
    }

    self_data_.resize(file_size);
    file.seekg(0, std::ios::beg);
    file.read(reinterpret_cast<char *>(self_data_.data()), file_size);
    if (!file.good()) {
      SetError("Failed to read SELF file data");
      spdlog::error("{}", last_error_);
      self_data_.clear();
      return false;
    }

    is_loaded_ = ParseSelfHeader(self_data_.data(), self_data_.size());
    if (!is_loaded_) {
      self_data_.clear();
      return false;
    }

    spdlog::info("Successfully loaded SELF file: {}", filepath);
    return true;
  } catch (const std::exception &e) {
    SetError("Exception loading SELF file: " + std::string(e.what()));
    spdlog::error("{}", last_error_);
    return false;
  }
}

bool SelfDecrypter::LoadSelfData(const std::vector<uint8_t> &data) {
  try {
    if (data.size() < MIN_SELF_SIZE || data.size() > MAX_SELF_SIZE) {
      SetError("Invalid SELF data size: " + std::to_string(data.size()));
      spdlog::error("{}", last_error_);
      return false;
    }

    self_data_ = data;
    is_loaded_ = ParseSelfHeader(self_data_.data(), self_data_.size());
    if (!is_loaded_) {
      self_data_.clear();
      return false;
    }

    spdlog::info("Successfully loaded SELF data (size: {} bytes)", data.size());
    return true;
  } catch (const std::exception &e) {
    SetError("Exception loading SELF data: " + std::string(e.what()));
    spdlog::error("{}", last_error_);
    return false;
  }
}

bool SelfDecrypter::DecryptSelf() {
  if (!is_loaded_) {
    SetError("No SELF data loaded");
    spdlog::error("{}", last_error_);
    return false;
  }

  if (!IsValidSelf()) {
    SetError("Invalid SELF file");
    spdlog::error("{}", last_error_);
    return false;
  }

  // Check if already decrypted ELF
  // CRITICAL: Add bounds check for ELF magic check
  if (self_data_.size() >= 4 &&
      SAFE_VECTOR_ACCESS(self_data_, 0, "SELF ELF magic check") == 0x7F &&
      SAFE_VECTOR_ACCESS(self_data_, 1, "SELF ELF magic check") == 'E' &&
      SAFE_VECTOR_ACCESS(self_data_, 2, "SELF ELF magic check") == 'L' &&
      SAFE_VECTOR_ACCESS(self_data_, 3, "SELF ELF magic check") == 'F') {
    spdlog::info("Input is already a decrypted ELF, skipping decryption");
    decrypted_elf_ = self_data_;
    is_decrypted_ = true;
    return true;
  }

  // Check if keys are available
  if (system_keys_.empty()) {
    SetError("No decryption keys available. Please provide a decrypted ELF "
             "file or valid keys in ./ps4_root/keys.json");
    spdlog::error("{}", last_error_);
    return false;
  }

  try {
    std::vector<SelfSegmentInfo> segment_infos;
    std::vector<uint8_t> metadata(self_data_.begin() + sizeof(SelfHeader),
                                  self_data_.begin() + sizeof(SelfHeader) +
                                      self_header_.meta_size);

    if (!ParseMetadata(metadata, self_header_, segment_infos)) {
      SetError("Failed to parse SELF metadata");
      spdlog::error("{}", last_error_);
      return false;
    }

    if (segment_infos.empty()) {
      SetError("No segments found in SELF file");
      spdlog::error("{}", last_error_);
      return false;
    }

    spdlog::info("Found {} segments to decrypt", segment_infos.size());

    // Calculate output size
    uint64_t max_offset = 0;
    for (const auto &seg_info : segment_infos) {
      uint64_t end_offset = seg_info.memory_offset + seg_info.memory_size;
      if (end_offset > max_offset) {
        max_offset = end_offset;
      }
    }

    decrypted_elf_.assign(max_offset, 0);

    // Process each segment
    for (size_t i = 0; i < segment_infos.size(); ++i) {
      const auto &seg_info = segment_infos[i];
      std::vector<uint8_t> processed_segment;

      if (seg_info.file_offset + seg_info.file_size > self_data_.size()) {
        SetError(fmt::format("Segment {} file data is out of bounds.", i));
        spdlog::error("{}", last_error_);
        return false;
      }

      // Enhanced bounds checking for segment data extraction
      VALIDATE_VECTOR(self_data_, "SelfDecrypter::DecryptSelf self_data_");
      if (seg_info.file_offset >= self_data_.size() ||
          seg_info.file_offset + seg_info.file_size > self_data_.size()) {
        SetError(fmt::format("Segment {} data extraction would exceed bounds", i));
        spdlog::error("{}", last_error_);
        return false;
      }

      std::vector<uint8_t> segment_data(
          self_data_.begin() + seg_info.file_offset,
          self_data_.begin() + seg_info.file_offset + seg_info.file_size);

      if (seg_info.encryption != 0) {
        if (!DecryptSegment(self_data_, seg_info, segment_data)) {
          SetError("Failed to decrypt segment " + std::to_string(i));
          spdlog::error("{}", last_error_);
          decrypted_elf_.clear();
          return false;
        }
      }

      if (seg_info.compression != 0) {
        if (!DecompressSegment(segment_data, processed_segment)) {
          SetError("Failed to decompress segment " + std::to_string(i));
          spdlog::error("{}", last_error_);
          decrypted_elf_.clear();
          return false;
        }
      } else {
        processed_segment = segment_data;
      }

      if (!VerifySegmentHash(processed_segment, seg_info)) {
        SetError("Segment " + std::to_string(i) + " hash verification failed");
        spdlog::warn("{}", last_error_); // Warn instead of fail
      }

      uint64_t copy_size =
          std::min(static_cast<uint64_t>(processed_segment.size()),
                   seg_info.memory_size);

      if (seg_info.memory_offset + copy_size > decrypted_elf_.size()) {
        SetError("Segment " + std::to_string(i) +
                 " would overflow output buffer");
        spdlog::error("{}", last_error_);
        decrypted_elf_.clear();
        return false;
      }

      // Enhanced bounds checking for segment copy operation
      VALIDATE_VECTOR(processed_segment, "SelfDecrypter::DecryptSelf processed_segment");
      VALIDATE_VECTOR(decrypted_elf_, "SelfDecrypter::DecryptSelf decrypted_elf_");

      if (copy_size > processed_segment.size()) {
        SetError(fmt::format("Segment {} copy size {} exceeds processed segment size {}",
                           i, copy_size, processed_segment.size()));
        spdlog::error("{}", last_error_);
        decrypted_elf_.clear();
        return false;
      }

      std::copy(processed_segment.begin(),
                processed_segment.begin() + copy_size,
                decrypted_elf_.begin() + seg_info.memory_offset);

      spdlog::debug("Copied segment {} to memory offset 0x{:X}", i,
                    seg_info.memory_offset);
    }

    is_decrypted_ = true;
    spdlog::info("Successfully decrypted SELF file, output size: 0x{:X} bytes",
                 decrypted_elf_.size());
    return true;
  } catch (const std::exception &e) {
    SetError("Exception during SELF decryption: " + std::string(e.what()));
    spdlog::error("{}", last_error_);
    decrypted_elf_.clear();
    return false;
  }
}

bool SelfDecrypter::ExtractElfData(std::vector<uint8_t> &elf_data) {
  if (!is_decrypted_) {
    SetError("SELF file not decrypted");
    spdlog::error("{}", last_error_);
    return false;
  }

  try {
    elf_data = decrypted_elf_;
    spdlog::info("Extracted ELF data, size: 0x{:X} bytes", elf_data.size());
    return true;
  } catch (const std::exception &e) {
    SetError("Exception extracting ELF data: " + std::string(e.what()));
    spdlog::error("{}", last_error_);
    return false;
  }
}

bool SelfDecrypter::IsEncryptedSelf(const std::vector<uint8_t> &data) {
  if (data.size() < sizeof(SelfHeader)) {
    spdlog::debug("Data too small for SELF header: {} bytes", data.size());
    return false;
  }

  // Check for ELF magic first (decrypted file)
  if (data.size() >= 4 && data[0] == 0x7F && data[1] == 'E' && data[2] == 'L' &&
      data[3] == 'F') {
    spdlog::debug(
        "Data appears to be a decrypted ELF file, not an encrypted SELF");
    return false;
  }

  const auto *header = reinterpret_cast<const SelfHeader *>(data.data());
  uint32_t magic_be = be32toh(header->magic);

  if (magic_be != SCE_MAGIC) {
    spdlog::debug("Invalid SELF magic: 0x{:08X}", magic_be);
    return false;
  }

  return true;
}

std::vector<uint8_t>
SelfDecrypter::DecryptSelf(const std::vector<uint8_t> &data) {
  SelfDecrypter decrypter;
  if (!decrypter.LoadSelfData(data)) {
    spdlog::error("Failed to load SELF data: {}", decrypter.GetLastError());
    return {};
  }

  if (!decrypter.DecryptSelf()) {
    spdlog::error("Failed to decrypt SELF: {}", decrypter.GetLastError());
    return {};
  }

  std::vector<uint8_t> elf_data;
  if (!decrypter.ExtractElfData(elf_data)) {
    spdlog::error("Failed to extract ELF data: {}", decrypter.GetLastError());
    return {};
  }

  return elf_data;
}

bool SelfDecrypter::IsValidSelf() const {
  return is_loaded_ && self_header_.magic == SCE_MAGIC &&
         self_header_.header_size >= sizeof(SelfHeader) &&
         self_header_.meta_size > 0 &&
         self_header_.file_size <= self_data_.size() &&
         self_header_.segment_count > 0;
}

bool SelfDecrypter::IsEncrypted() const {
  return IsValidSelf() &&
         (self_header_.key_type == static_cast<uint32_t>(SelfKeyType::DEBUG) ||
          self_header_.key_type == static_cast<uint32_t>(SelfKeyType::RETAIL) ||
          self_header_.key_type ==
              static_cast<uint32_t>(SelfKeyType::TESTKIT));
}

bool SelfDecrypter::ParseSelfHeader(const uint8_t *data, size_t size) {
  if (size < sizeof(SelfHeader)) {
    SetError("Data too small for SELF header: " + std::to_string(size));
    return false;
  }

  std::memcpy(&self_header_, data, sizeof(SelfHeader));
  self_header_.magic = be32toh(self_header_.magic);
  self_header_.version = be32toh(self_header_.version);
  self_header_.key_type = be32toh(self_header_.key_type);
  self_header_.header_size = be32toh(self_header_.header_size);
  if (!IsValidSelf()) {
    SetError(fmt::format(
        "Invalid SELF header: magic=0x{:08X}, header_size=0x{:X}, "
        "meta_size=0x{:X}, file_size=0x{:X}, segment_count={}",
        self_header_.magic, self_header_.header_size, self_header_.meta_size,
        self_header_.file_size, self_header_.segment_count));
    spdlog::error("{}", last_error_);
    return false;
  }

  // Parse additional metadata
  size_t offset = self_header_.header_size;
  if (!ParseAppInfo(data, offset)) {
    return false;
  }

  offset += sizeof(AppInfo);
  if (!ParseElfDigest(data, offset)) {
    return false;
  }

  offset += sizeof(ElfDigest);
  if (!ParseNpdHeader(data, offset)) {
    return false;
  }

  offset += sizeof(NpdHeader);
  if (!ParseControlInfo(data, offset, size - offset)) {
    return false;
  }

  return true;
}

bool SelfDecrypter::ParseAppInfo(const uint8_t *data, size_t offset) {
  if (offset + sizeof(AppInfo) > self_data_.size()) {
    SetError("Invalid offset for AppInfo: " + std::to_string(offset));
    spdlog::error("{}", last_error_);
    return false;
  }

  std::memcpy(&app_info_, data + offset, sizeof(AppInfo));
  app_info_.auth_id = be64toh(app_info_.auth_id);
  app_info_.vendor_id = be32toh(app_info_.vendor_id);
  app_info_.self_type = be32toh(app_info_.self_type);
  app_info_.version = be64toh(app_info_.version);
  app_info_.padding = be64toh(app_info_.padding);

  spdlog::debug("Parsed AppInfo: auth_id=0x{:X}, vendor_id=0x{:X}, "
                "self_type=0x{:X}, version=0x{:X}",
                app_info_.auth_id, app_info_.vendor_id, app_info_.self_type,
                app_info_.version);
  return true;
}

bool SelfDecrypter::ParseElfDigest(const uint8_t *data, size_t offset) {
  if (offset + sizeof(ElfDigest) > self_data_.size()) {
    SetError("Invalid offset for ElfDigest: " + std::to_string(offset));
    spdlog::error("{}", last_error_);
    return false;
  }

  std::memcpy(&elf_digest_, data + offset, sizeof(ElfDigest));
  spdlog::debug("Parsed ElfDigest");
  return true;
}

bool SelfDecrypter::ParseControlInfo(const uint8_t *data, size_t offset,
                                     size_t size) {
  control_info_.clear();
  while (size >= sizeof(uint32_t) * 2 + sizeof(uint64_t)) {
    ControlInfo info;
    std::memcpy(&info, data + offset, sizeof(uint32_t) * 2 + sizeof(uint64_t));
    info.type = be32toh(info.type);
    info.size = be32toh(info.size);
    info.next = be64toh(info.next);

    if (info.size > size ||
        info.size < (sizeof(uint32_t) * 2 + sizeof(uint64_t))) {
      SetError("Invalid ControlInfo size: " + std::to_string(info.size));
      spdlog::error("{}", last_error_);
      return false;
    }

    size_t data_size = info.size - (sizeof(uint32_t) * 2 + sizeof(uint64_t));
    if (data_size > 0) {
      info.data.resize(data_size);
      std::memcpy(info.data.data(),
                  data + offset + sizeof(uint32_t) * 2 + sizeof(uint64_t),
                  data_size);
    }
    control_info_.push_back(std::move(info));

    if (info.next == 0)
      break;
    offset += info.next;
    size = (offset < self_data_.size()) ? self_data_.size() - offset : 0;
  }

  spdlog::debug("Parsed {} ControlInfo entries", control_info_.size());
  return true;
}

bool SelfDecrypter::ParseNpdHeader(const uint8_t *data, size_t offset) {
  if (offset + sizeof(NpdHeader) > self_data_.size()) {
    SetError("Invalid offset for NpdHeader: " + std::to_string(offset));
    spdlog::error("{}", last_error_);
    return false;
  }

  std::memcpy(&npd_header_, data + offset, sizeof(NpdHeader));
  npd_header_.magic = be32toh(npd_header_.magic);
  if (npd_header_.magic != NPD_MAGIC) {
    SetError(fmt::format("Invalid NPD magic: 0x{:08X}", npd_header_.magic));
    spdlog::warn("{}", last_error_); // Warn instead of fail for non-NPDRM SELF
    return true;
  }

  spdlog::debug("Parsed NpdHeader: magic=0x{:X}, version=0x{:X}, type=0x{:X}",
                npd_header_.magic, npd_header_.version, npd_header_.type);
  return true;
}

bool SelfDecrypter::ParseMetadata(const std::vector<uint8_t> &data,
                                  const SelfHeader &header,
                                  std::vector<SelfSegmentInfo> &segments) {
  if (data.size() < sizeof(SelfHeader)) {
    SetError("Metadata too small: " + std::to_string(data.size()));
    spdlog::error("{}", last_error_);
    return false;
  }

  const auto *program_headers =
      reinterpret_cast<const SelfSegmentInfo *>(data.data());
  segments.clear();
  for (uint32_t i = 0; i < header.segment_count; ++i) {
    SelfSegmentInfo seg_info;
    std::memcpy(&seg_info, &program_headers[i], sizeof(SelfSegmentInfo));

    seg_info.flags = be64toh(seg_info.flags);
    seg_info.file_offset = be64toh(seg_info.file_offset);
    seg_info.file_size = be64toh(seg_info.file_size);
    seg_info.memory_offset = be64toh(seg_info.memory_offset);
    seg_info.memory_size = be64toh(seg_info.memory_size);
    seg_info.compression = be32toh(seg_info.compression);
    seg_info.encryption = be32toh(seg_info.encryption);

    segments.push_back(seg_info);
  }

  // Find and parse digests from control info
  for (const auto &ctrl_info : control_info_) {
    if (ctrl_info.type == 2) { // Type 2 is DIGEST
      const auto *digest_data =
          reinterpret_cast<const SelfDigestEntry *>(ctrl_info.data.data());
      uint64_t digest_count = ctrl_info.data.size() / sizeof(SelfDigestEntry);
      for (uint64_t j = 0; j < digest_count; ++j) {
        SelfDigestEntry digest = digest_data[j];
        digest.offset = be64toh(digest.offset);
        digest.size = be64toh(digest.size);
        encryption_info_.digests.push_back(digest);
      }
    }
  }

  return !segments.empty();
}

bool SelfDecrypter::DecryptSegment(const std::vector<uint8_t> &input,
                                   const SelfSegmentInfo &seg_info,
                                   std::vector<uint8_t> &output) {
  // Bounds check before accessing input vector
  if (seg_info.file_offset >= input.size() ||
      seg_info.file_offset + seg_info.file_size > input.size() ||
      seg_info.file_size == 0) {
    SetError(fmt::format("Invalid segment bounds: offset={}, size={}, input_size={}",
                         seg_info.file_offset, seg_info.file_size, input.size()));
    spdlog::error("{}", last_error_);
    return false;
  }

  output.assign(input.begin() + seg_info.file_offset,
                input.begin() + seg_info.file_offset + seg_info.file_size);

  if (seg_info.encryption == 0) {
    spdlog::debug("Segment is not encrypted.");
    return true;
  }

  // Get the decryption key for this segment
  std::vector<uint8_t> key = GetKey(self_header_.key_type);
  if (key.empty()) {
    SetError(fmt::format("Failed to get key for segment: key_type={}",
                         self_header_.key_type));
    spdlog::error("{}", last_error_);
    return false;
  }

  try {
    if (seg_info.encryption == 2) { // AES-128-CTR
      std::vector<uint8_t> iv_vec = GetIv(seg_info.iv_idx);
      if (iv_vec.size() != 16) {
        SetError(fmt::format("Invalid IV size for segment: {}", iv_vec.size()));
        spdlog::error("{}", last_error_);
        return false;
      }
      std::array<uint8_t, 16> iv;
      std::copy_n(iv_vec.begin(), 16, iv.begin());
      return AesCtrDecrypt(output, {key, iv_vec}, iv, output);
    } else if (seg_info.encryption == 1) { // AES-CBC
      DecryptionKey dec_key = {key, GetIv(seg_info.iv_idx)};
      return AesDecrypt(output, dec_key, output);
    } else {
      SetError(
          fmt::format("Unsupported encryption type: {}", seg_info.encryption));
      return false;
    }
  } catch (const std::exception &e) {
    SetError("Exception during segment decryption: " + std::string(e.what()));
    return false;
  }
}

bool SelfDecrypter::DecryptSegment(const SelfEntry &entry,
                                   const uint8_t *encrypted_data,
                                   std::vector<uint8_t> &decrypted_data) {
  SelfSegmentInfo seg_info;
  seg_info.file_offset = entry.offset;
  seg_info.file_size = entry.size;
  seg_info.key_idx = entry.key_idx;
  seg_info.iv_idx = entry.iv_idx;
  seg_info.encryption = entry.algorithm;
  seg_info.compression = entry.compressed;

  std::vector<uint8_t> input(encrypted_data, encrypted_data + entry.size);
  return DecryptSegment(input, seg_info, decrypted_data);
}

bool SelfDecrypter::DecompressSegment(std::vector<uint8_t> &data) {
  return DecompressSegment(data, data);
}

bool SelfDecrypter::DecompressSegment(
    const std::vector<uint8_t> &compressed_data,
    std::vector<uint8_t> &decompressed_data) {
  if (compressed_data.empty()) {
    decompressed_data.clear();
    return true;
  }
  z_stream stream{};
  stream.zalloc = Z_NULL;
  stream.zfree = Z_NULL;
  stream.opaque = Z_NULL;

  if (inflateInit(&stream) != Z_OK) {
    SetError("Failed to initialize zlib for decompression");
    return false;
  }

  std::vector<uint8_t> buffer(16384);
  decompressed_data.clear();

  stream.avail_in = compressed_data.size();
  stream.next_in = const_cast<Bytef *>(compressed_data.data());

  int ret;
  do {
    stream.avail_out = buffer.size();
    stream.next_out = buffer.data();
    ret = inflate(&stream, Z_NO_FLUSH);
    if (ret < 0) { // Z_STREAM_ERROR, Z_DATA_ERROR, etc.
      inflateEnd(&stream);
      SetError(fmt::format("Zlib decompression error: {} ({})", ret,
                           stream.msg ? stream.msg : "N/A"));
      return false;
    }
    size_t have = buffer.size() - stream.avail_out;
    decompressed_data.insert(decompressed_data.end(), buffer.begin(),
                             buffer.begin() + have);
  } while (stream.avail_out == 0);

  if (ret != Z_STREAM_END) {
    inflateEnd(&stream);
    SetError(fmt::format(
        "Zlib decompression finished prematurely. Final status: {}", ret));
    return false;
  }
  inflateEnd(&stream);

  spdlog::debug("Decompressed segment: {} bytes -> {} bytes",
                compressed_data.size(), decompressed_data.size());
  return true;
}

bool SelfDecrypter::VerifySegmentHash(const std::vector<uint8_t> &data,
                                      const SelfSegmentInfo &seg_info) {
  std::array<uint8_t, 32> calculated_hash;
  if (!ps4::SHACrypto::ComputeSHA256(data, calculated_hash)) {
    SetError("Failed to compute segment hash");
    return false;
  }

  for (const auto &digest : encryption_info_.digests) {
    if (digest.offset == seg_info.file_offset &&
        digest.size == seg_info.file_size) {
      if (std::memcmp(calculated_hash.data(), digest.digest, 32) != 0) {
        SetError(fmt::format("Segment hash mismatch: offset=0x{:X}",
                             seg_info.file_offset));
        return false;
      }
      spdlog::debug("Segment hash verified: offset=0x{:X}",
                    seg_info.file_offset);
      return true;
    }
  }

  spdlog::warn("No digest found for segment: offset=0x{:X}",
               seg_info.file_offset);
  return true; // Allow missing digests
}

bool SelfDecrypter::LoadSystemKeys() {
  ps4::KeyStore key_store;
  std::string master_key = "your_secure_master_key_32_bytes_long_123";
  std::string key_file_path = "./ps4_root/keys.json";
  system_keys_.clear();

  if (!std::filesystem::exists(key_file_path)) {
    SetError("Key file not found: " + key_file_path +
             ". Please provide a decrypted ELF or valid keys.");
    spdlog::warn("{}", last_error_);
    return false;
  }

  if (!key_store.Initialize(master_key, key_file_path)) {
    SetError("Failed to initialize KeyStore: " + key_store.GetLastError());
    spdlog::warn("{}", last_error_);
    return false;
  }

  for (uint32_t key_type = 0; key_type <= 2; ++key_type) {
    for (uint32_t index = 0; index < 20; ++index) {
      auto key = key_store.GetKey(key_type, index);
      auto iv = key_store.GetIV(key_type, index);
      if (!key.empty() && !iv.empty()) {
        DecryptionKey dec_key;
        dec_key.key = key;
        dec_key.iv = iv;
        dec_key.key_type = key_type;
        dec_key.key_revision = index;
        system_keys_.push_back(dec_key);
        spdlog::debug(
            "Loaded system key: type={}, index={}, key_size={}, iv_size={}",
            key_type, index, key.size(), iv.size());
      }
    }
  }

  if (system_keys_.empty()) {
    SetError("No system keys loaded from KeyStore. Please provide a decrypted "
             "ELF file.");
    spdlog::warn("{}", last_error_);
    return false;
  }

  spdlog::info("Loaded {} system keys", system_keys_.size());
  return true;
}

bool SelfDecrypter::DeriveContentKey(const DecryptionKey &master_key,
                                     const std::array<uint8_t, 16> &iv,
                                     DecryptionKey &content_key) const {
  try {
    std::vector<uint8_t> seed(npd_header_.content_id.begin(),
                              npd_header_.content_id.end());

    std::array<uint8_t, 32> derived_key;
    if (!ps4::SHACrypto::ComputeHMAC_SHA256(seed, master_key.key,
                                            derived_key)) {
      const_cast<SelfDecrypter *>(this)->SetError(
          "Failed to compute HMAC-SHA256 for key derivation");
      return false;
    }

    content_key.key.assign(derived_key.begin(), derived_key.begin() + 16);
    content_key.iv.assign(iv.begin(), iv.end());
    content_key.key_type = master_key.key_type;
    content_key.key_revision = master_key.key_revision;

    return true;
  } catch (const std::exception &e) {
    const_cast<SelfDecrypter *>(this)->SetError(
        "Exception during key derivation: " + std::string(e.what()));
    return false;
  }
}

std::vector<uint8_t> SelfDecrypter::GetKey(uint32_t key_type_idx) const {
  for (const auto &key : system_keys_) {
    if (key.key_type == key_type_idx) {
      return key.key;
    }
  }
  spdlog::warn("Key not found for type_idx: {}", key_type_idx);
  return {};
}

std::vector<uint8_t> SelfDecrypter::GetIv(uint32_t iv_idx) const {
  for (const auto &key : system_keys_) {
    if (key.key_type == iv_idx) {
      return key.iv;
    }
  }
  spdlog::warn("Unknown IV index: {}", iv_idx);
  return {};
}

bool SelfDecrypter::AesDecrypt(const std::vector<uint8_t> &encrypted_data,
                               const DecryptionKey &key,
                               std::vector<uint8_t> &decrypted_data) {
  std::vector<uint8_t> key_vec = key.key;
  std::array<uint8_t, 16> iv_array;

  // Bounds check before copying IV
  if (key.iv.size() < 16) {
    SetError(fmt::format("Invalid IV size: {} (expected 16)", key.iv.size()));
    return false;
  }
  std::copy(key.iv.begin(), key.iv.end(), iv_array.begin());

  if (key_vec.size() == 16) {
    std::array<uint8_t, 16> key_array_128;
    // Bounds check is implicit since we verified size == 16
    std::copy(key_vec.begin(), key_vec.end(), key_array_128.begin());
    return ps4::AESCrypto::DecryptAES128_CBC(encrypted_data, key_array_128,
                                             iv_array, decrypted_data);
  }
  SetError(
      fmt::format("Invalid key size for AES decryption: {}", key_vec.size()));
  return false;
}

bool SelfDecrypter::AesCtrDecrypt(const std::vector<uint8_t> &encrypted_data,
                                  const DecryptionKey &key,
                                  const std::array<uint8_t, 16> &iv,
                                  std::vector<uint8_t> &decrypted_data) const {
  if (key.key.size() != 16) {
    const_cast<SelfDecrypter *>(this)->SetError(
        "Invalid key size for AES-128-CTR decryption.");
    return false;
  }
  std::array<uint8_t, 16> key_array;
  std::copy_n(key.key.begin(), 16, key_array.begin());

  return ps4::AESCrypto::DecryptAES128_CTR(encrypted_data, key_array, iv,
                                           decrypted_data);
}

bool SelfDecrypter::ValidateDigest(
    const std::vector<uint8_t> &data,
    const std::array<uint8_t, 32> &expected_digest) {
  std::array<uint8_t, 32> calculated_hash;
  if (!ps4::SHACrypto::ComputeSHA256(data, calculated_hash)) {
    SetError("Failed to compute digest");
    return false;
  }

  if (calculated_hash != expected_digest) {
    SetError("Digest validation failed");
    return false;
  }
  return true;
}

void SelfDecrypter::SetError(const std::string &error) { last_error_ = error; }

void SelfDecrypter::ClearError() { last_error_.clear(); }

namespace SelfUtils {
bool IsSelfFile(const std::vector<uint8_t> &data) {
  return SelfDecrypter::IsEncryptedSelf(data);
}

bool IsSelfFile(const std::string &filepath) {
  std::ifstream file(filepath, std::ios::binary);
  if (!file)
    return false;
  std::vector<uint8_t> header_data(sizeof(SelfHeader));
  file.read(reinterpret_cast<char *>(header_data.data()), sizeof(SelfHeader));
  if (!file)
    return false;
  return IsSelfFile(header_data);
}

uint32_t GetSelfType(const std::vector<uint8_t> &data) {
  SelfDecrypter decrypter;
  if (decrypter.LoadSelfData(data)) {
    return decrypter.GetSelfType();
  }
  return 0;
}

bool ExtractSelfInfo(const std::vector<uint8_t> &data, SelfHeader &header,
                     AppInfo &app_info) {
  SelfDecrypter decrypter;
  if (!decrypter.LoadSelfData(data)) {
    return false;
  }
  header = decrypter.GetSelfHeader();
  app_info = decrypter.GetAppInfo();
  return true;
}
} // namespace SelfUtils
} // namespace ps4
