// Copyright 2025 <Copyright Owner>

#pragma once

#include "../memory/ps4_mmu.h"
#include "elf.h"
#include "common/lock_ordering.h"
#include <cstdint>
#include <fstream>
#include <string>
#include <unordered_map>
#include <vector>
#include <mutex>

namespace ps4 {
class PS4Emulator;
}

namespace ps4 {

struct LoadedElf {
    struct Segment {
        uint64_t address;
        uint64_t size;
        int protection;
    };

    std::vector<Segment> loadedSegments;
    uint64_t entryPoint = 0;
    uint64_t baseLoadAddress = 0;
    uint64_t phdrAddress = 0;
    uint16_t phdrEntrySize = 0;
    uint16_t phdrNum = 0;
    uint64_t dynamicAddress = 0;
    uint64_t dynamicSize = 0;
    uint64_t dynSymTableAddr = 0;
    uint64_t dynStrTableAddr = 0;
    uint64_t dynStrTableSize = 0;
    uint64_t relaDynAddr = 0;
    uint64_t relaDynSize = 0;
    uint64_t relaPltAddr = 0;
    uint64_t relaPltSize = 0;
    uint64_t pltGotAddr = 0;
    std::vector<elf::Elf64_Sym> dynSymbols;
    std::vector<char> dynStringTable;
    std::unordered_map<std::string, uint64_t> resolvedSymbols;
    uint64_t metadataAddr = 0;
    uint64_t metadataSize = 0;
    bool isSelf = false; // Indicates SELF file
    std::vector<elf::self_segment_header> selfSegments; // SELF segment headers
    std::vector<uint8_t> self_data_; // Raw file data for SELF decryption

    void Save(std::ostream& out) const;
    void Load(std::istream& in);
};

class ElfLoader {
public:
    explicit ElfLoader(PS4Emulator& emulator);
    ~ElfLoader() noexcept;

    bool Load(const std::string& filename, LoadedElf& loadedElf,
              bool isSharedObject = false, uint64_t processId = 1);
    const std::unordered_map<std::string, uint64_t>& GetStats() const {
        COMPONENT_LOCK(m_mutex, "ElfLoaderMutex");
        return m_stats;
    }

private:
    bool LoadSegments(std::ifstream& file, const elf::Elf64_Ehdr& elfHeader,
                      LoadedElf& loadedElf, std::streamsize fileSize);
    bool ParseDynamicSection(LoadedElf& loadedElf);
    bool ReadDynamicTables(LoadedElf& loadedElf);
    bool ProcessRelocations(LoadedElf& loadedElf, uint64_t relaAddr,
                            uint64_t relaSize, bool isPlt);
    uint64_t ResolveSymbol(const std::string& name, LoadedElf& currentElf);

    PS4Emulator& m_emulator;
    uint64_t m_currentProcessId;
    std::unordered_map<std::string, uint64_t> m_stats;
    mutable std::mutex m_mutex;
};

} // namespace ps4