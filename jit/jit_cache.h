#pragma once

#include <cstdint>
#include <memory>
#include <unordered_map>
#include <mutex>

namespace jit {

enum class Status {
    Success,
    Failed,
    NotFound,
    InvalidInput,
    OutOfMemory
};

struct JitBlock {
    uint64_t pc;
    uint8_t* code;
    size_t size;
    bool translated;
    uint64_t lastUsed;
    uint32_t executionCount;

    JitBlock() : pc(0), code(nullptr), size(0), translated(false), lastUsed(0), executionCount(0) {}

    JitBlock(uint64_t pc_val) : pc(pc_val), code(nullptr), size(0), translated(false), lastUsed(0), executionCount(0) {}

    ~JitBlock() {
        if (code) {
            freeExecutableMemory();
        }
    }

    // Move constructor
    JitBlock(JitBlock&& other) noexcept
        : pc(other.pc), code(other.code), size(other.size),
          translated(other.translated), lastUsed(other.lastUsed),
          executionCount(other.executionCount) {
        other.code = nullptr;
        other.size = 0;
    }

    // Move assignment
    JitBlock& operator=(JitBlock&& other) noexcept {
        if (this != &other) {
            if (code) {
                freeExecutableMemory();
            }
            pc = other.pc;
            code = other.code;
            size = other.size;
            translated = other.translated;
            lastUsed = other.lastUsed;
            executionCount = other.executionCount;

            other.code = nullptr;
            other.size = 0;
        }
        return *this;
    }

    // Delete copy constructor and assignment
    JitBlock(const JitBlock&) = delete;
    JitBlock& operator=(const JitBlock&) = delete;

private:
    void freeExecutableMemory();
};

class JitCache {
public:
    JitCache();
    ~JitCache();

    // Get or create a JIT block for the given program counter
    JitBlock& getOrCreate(uint64_t pc);

    // Find an existing block
    JitBlock* find(uint64_t pc);

    // Remove a block from cache
    void remove(uint64_t pc);

    // Clear all cached blocks
    void clear();

    // Get cache statistics
    struct Stats {
        uint64_t hits;
        uint64_t misses;
        uint64_t evictions;
        size_t totalSize;
        size_t blockCount;

        Stats() : hits(0), misses(0), evictions(0), totalSize(0), blockCount(0) {}
    };

    Stats getStats() const;

    // Cache management
    void setMaxSize(size_t maxSize);
    size_t getMaxSize() const;

private:
    std::unordered_map<uint64_t, std::unique_ptr<JitBlock>> blocks_;
    mutable std::mutex mutex_;
    size_t maxSize_;
    size_t currentSize_;

    // Statistics
    mutable Stats stats_;

    // Internal methods
    void evictLRU();
    void updateStats(bool hit);
};

} // namespace jit
