#pragma once

#include "jit_cache.h"
#include <cstdint>
#include <vector>

// Forward declarations
namespace x86_64 {
    class DecodedInstruction;
    class X86_64CPU;
    enum class Register : uint8_t;
}

namespace ps4 {
    class PS4MMU;
}

namespace jit {

class JitTranslator {
public:
    JitTranslator();
    ~JitTranslator();
    
    // Translate a block of instructions starting at the given PC
    Status translate(JitBlock& block, uint64_t pc);
    
    // Set the CPU context for translation
    void setCPU(x86_64::X86_64CPU* cpu);
    
    // Set the memory manager for translation
    void setMemoryManager(ps4::PS4MMU* memory);
    
    // Optimization levels
    enum class OptimizationLevel {
        None,
        Basic,
        Aggressive
    };
    
    // Code generation backends
    enum class CodeGenBackend {
        X86_64_HOST,
        INTERPRETER,
        LLVM
    };
    
    // Translation options
    struct Options {
        bool enableOptimizations;
        bool enableSIMD;
        bool enableVectorization;
        bool enableDeadCodeElimination;
        bool enableConstantFolding;
        bool enableRegisterAllocation;
        OptimizationLevel optimizationLevel;
        CodeGenBackend backend;
        uint32_t maxBlockSize;
        uint32_t maxInstructions;
        
        Options() : enableOptimizations(true), enableSIMD(true), 
                   enableVectorization(false), enableDeadCodeElimination(true),
                   enableConstantFolding(true), enableRegisterAllocation(true),
                   optimizationLevel(OptimizationLevel::Basic), backend(CodeGenBackend::X86_64_HOST),
                   maxBlockSize(4096), maxInstructions(100) {}
    };
    
    void setOptions(const Options& options);
    const Options& getOptions() const;
    
    // Translation statistics
    struct Stats {
        uint64_t blocksTranslated;
        uint64_t instructionsTranslated;
        uint64_t translationTime;
        uint64_t optimizationTime;
        uint64_t codeGenTime;
        uint64_t failures;
        uint64_t totalCodeSize;
        
        Stats() : blocksTranslated(0), instructionsTranslated(0), 
                 translationTime(0), optimizationTime(0), codeGenTime(0), failures(0), totalCodeSize(0) {}
    };
    
    Stats getStats() const;
    void resetStats();
    
    // Block invalidation methods
    void invalidateBlock(uint64_t pc);
    void invalidateRange(uint64_t startPC, uint64_t endPC);
    bool isBlockTranslated(uint64_t pc) const;
    
private:
    x86_64::X86_64CPU* cpu_;
    ps4::PS4MMU* memory_;
    Options options_;
    mutable Stats stats_;
    
    // Decoder state and context structures
    struct DecoderState {
        uint64_t currentPC;
        uint64_t instructionCount;
        bool blockTerminated;
        uint8_t* buffer;
        size_t bufferSize;
        size_t offset;
    };
    
    struct CodeGenContext {
        uint8_t* codeBuffer;
        size_t bufferSize;
        size_t currentOffset;
        std::vector<uint8_t> generatedCode;
    };
    
    // Internal translation methods
    Status translateBlock(JitBlock& block, uint64_t startPC);
    Status decodeInstructions(uint64_t pc, std::vector<x86_64::DecodedInstruction>& instructions);
    Status generateCode(const std::vector<x86_64::DecodedInstruction>& instructions, JitBlock& block);
    Status optimizeCode(JitBlock& block);
    
    // Validation and optimization methods
    bool validateInstructions(const std::vector<x86_64::DecodedInstruction>& instructions);
    bool validateBlock(const std::vector<x86_64::DecodedInstruction>& instructions);
    bool validateBlock(const JitBlock& block);
    Status deadCodeElimination(std::vector<x86_64::DecodedInstruction>& instructions);
    Status constantFolding(std::vector<x86_64::DecodedInstruction>& instructions);
    Status registerAllocation(std::vector<x86_64::DecodedInstruction>& instructions);
    Status peepholeOptimization(CodeGenContext& context);
    
    // Code generation helper methods
    void emitWord(CodeGenContext& context, uint16_t word);
    void emitRelativeJump(CodeGenContext& context, uint64_t target);
    uint8_t mapGuestRegisterToHost(x86_64::Register guestReg);
    bool isRegisterAvailable(uint8_t hostReg);
    
    // Decoder methods
    Status decodeInstruction(uint64_t pc, x86_64::DecodedInstruction& instr);
    bool readMemoryBytes(uint64_t address, uint8_t* buffer, size_t size);
    
    // Code generation methods
    Status generateX86Code(const std::vector<x86_64::DecodedInstruction>& instructions, CodeGenContext& context);
    Status generateInterpreterCode(const std::vector<x86_64::DecodedInstruction>& instructions, CodeGenContext& context);
    
    // Code emission helpers
    void emitByte(CodeGenContext& context, uint8_t byte);
    void emitDword(CodeGenContext& context, uint32_t value);
    void emitQword(CodeGenContext& context, uint64_t value);
    
    // Helper methods
    bool isBlockTerminator(const x86_64::DecodedInstruction& instr);
    size_t estimateCodeSize(const std::vector<x86_64::DecodedInstruction>& instructions);
    uint8_t* allocateExecutableMemory(size_t size);
    void freeExecutableMemory(uint8_t* ptr, size_t size);
};

} // namespace jit
